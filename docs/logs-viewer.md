# 日志查看器 (Logs Viewer)

## 概述

日志查看器是一个功能强大的日志管理和分析工具，提供了直观的界面来查看、搜索、分析和导出系统日志。它基于现有的 HBLog 数据结构构建，支持多维度搜索、实时监控、数据可视化等功能。

## 功能特性

### 🔍 强大的搜索功能
- **基础搜索**: 支持按时间范围、日志ID、会话ID、供应商等快速筛选
- **高级搜索**: 提供更多搜索维度，包括用户ID、API路径、HTTP状态码、业务错误码等
- **关键词搜索**: 支持在请求/响应体中搜索特定关键词
- **性能筛选**: 按响应时间范围筛选日志

### 📊 数据可视化
- **统计概览**: 实时显示成功、警告、错误请求的统计数据
- **响应时间分布**: 饼图展示不同响应时间区间的分布
- **供应商调用统计**: 柱状图显示各供应商的调用次数
- **错误率趋势**: 折线图展示错误率随时间的变化趋势

### 📋 详细的日志展示
- **列表视图**: 表格形式展示日志列表，支持排序和分页
- **状态标识**: 通过颜色编码快速识别日志状态（成功/警告/错误）
- **快速预览**: 鼠标悬停显示请求/响应的前100字符
- **详情查看**: 点击日志行查看完整的请求/响应数据

### 🔧 高级功能
- **Monaco Editor**: 使用 VSCode 同款编辑器展示 JSON 数据，支持语法高亮和格式化
- **实时监控**: 支持自动刷新，实时查看最新日志
- **数据导出**: 支持 CSV、JSON 格式导出，可自定义导出字段
- **对比视图**: 并排显示请求和响应数据，便于对比分析

## 技术架构

### 后端架构
```
bi/service/log_viewer_api.go    # 日志查看器服务
├── QueryLogs                   # 查询日志列表
├── GetLogDetail               # 获取日志详情
├── ExportLogs                 # 导出日志
└── GetLogStatistics           # 获取统计数据

api/service/log_viewer.go       # API 层服务包装器
```

### 前端架构
```
admin-fe/src/views/system/logs/
├── index.vue                   # 主页面
├── components/
│   ├── LogDetailDialog.vue     # 日志详情弹窗
│   ├── ExportDialog.vue        # 导出弹窗
│   └── LogStatistics.vue       # 统计组件
└── api/logViewer/              # API 接口定义
    ├── index.ts
    └── types.ts
```

### 核心组件
- **Monaco Editor**: 代码编辑器，用于展示 JSON 数据
- **ECharts**: 图表库，用于数据可视化
- **Element Plus**: UI 组件库

## 使用指南

### 基础搜索
1. 选择时间范围（默认最近1小时）
2. 输入搜索条件（日志ID、会话ID、供应商等）
3. 点击"搜索"按钮

### 高级搜索
1. 点击"高级搜索"按钮展开高级搜索面板
2. 填写更多搜索条件
3. 设置耗时范围、关键词等
4. 点击"搜索"执行查询

### 查看日志详情
1. 在日志列表中点击任意一行
2. 在弹出的详情窗口中查看完整信息
3. 切换"请求"、"响应"、"对比"标签查看不同视图
4. 使用 Monaco Editor 查看格式化的 JSON 数据

### 数据可视化
1. 点击"显示统计"按钮
2. 查看统计卡片了解整体情况
3. 分析响应时间分布和供应商调用统计
4. 观察错误率趋势图

### 导出数据
1. 点击"导出"按钮
2. 选择导出格式（CSV 或 JSON）
3. 设置导出数量和字段（CSV 格式）
4. 点击"开始导出"下载文件

### 实时监控
1. 开启"自动刷新"开关
2. 系统每10秒自动刷新一次数据
3. 关闭开关停止自动刷新

## API 接口

### 查询日志列表
```http
POST /api/logViewer/queryLogs
Content-Type: application/json

{
  "page": 1,
  "pageSize": 20,
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-01-01T23:59:59Z",
  "logId": "",
  "sessionId": "",
  "apiOutSupplier": "dida"
}
```

### 获取日志详情
```http
POST /api/logViewer/getLogDetail
Content-Type: application/json

{
  "logId": "log-id-here"
}
```

### 导出日志
```http
POST /api/logViewer/exportLogs
Content-Type: application/json

{
  "format": "csv",
  "pageSize": 1000,
  "fields": ["timestamp", "logId", "sessionId", "apiInPath"]
}
```

### 获取统计数据
```http
POST /api/logViewer/getLogStatistics
Content-Type: application/json

{
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-01-01T23:59:59Z",
  "groupBy": "supplier"
}
```

## 权限配置

日志查看器需要以下权限：
- `system:logs:read` - 查看日志列表和详情
- `system:logs:export` - 导出日志数据
- `system:logs:statistics` - 查看统计数据

## 性能优化

### 后端优化
- 分页查询，避免一次性加载大量数据
- 索引优化，提高查询性能
- 缓存统计数据，减少重复计算

### 前端优化
- 虚拟滚动，处理大量数据展示
- 防抖搜索，减少不必要的请求
- 组件懒加载，提高页面加载速度

## 故障排除

### 常见问题

1. **日志加载缓慢**
   - 检查时间范围是否过大
   - 优化搜索条件，缩小查询范围
   - 检查数据库索引

2. **Monaco Editor 不显示**
   - 检查 Monaco Editor 依赖是否正确安装
   - 确认浏览器兼容性

3. **图表不显示**
   - 检查 ECharts 依赖
   - 确认数据格式正确

4. **导出功能失败**
   - 检查导出数量是否超过限制
   - 确认服务器磁盘空间充足

### 调试方法
- 打开浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 查看后端日志了解详细错误

## 扩展开发

### 添加新的搜索字段
1. 在 `QueryLogsReq` 结构体中添加新字段
2. 在 `buildLogQuery` 方法中处理新字段
3. 在前端搜索表单中添加对应的输入控件

### 添加新的图表
1. 在 `LogStatistics.vue` 中添加新的图表容器
2. 使用 ECharts 创建图表实例
3. 在数据更新时刷新图表

### 自定义导出格式
1. 在 `ExportLogsReq` 中添加新的格式选项
2. 实现对应的导出逻辑
3. 在前端导出弹窗中添加新选项

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础搜索和列表展示功能
- 日志详情查看
- 数据可视化统计
- 导出功能
- 实时监控功能

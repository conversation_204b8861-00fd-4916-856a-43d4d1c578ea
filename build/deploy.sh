curl -u ptsj01zzpcty:323b0227b8194fdafaa2c9e24f555625be62230f \
   -v -X POST  'https://oscillator.coding.net/api/cci/job/5779901/trigger' \
   -H 'Content-Type: application/json' \
   -d '
    {
    "ref": "master",
    "envs": [
        {
            "name": "DOCKER_IMAGE_NAME",
            "value": "golang-http-app",
            "sensitive": 0
        },
        {
            "name": "DOCKER_REPO_NAME",
            "value": "backend",
            "sensitive": 0
        },
        {
            "name": "DOCKERFILE_PATH",
            "value": "Dockerfile",
            "sensitive": 0
        },
        {
            "name": "REMOTE_SSH_PORT",
            "value": "22",
            "sensitive": 0
        },
        {
            "name": "DOCKER_BUILD_CONTEXT",
            "value": "./",
            "sensitive": 0
        },
        {
            "name": "DOCKER_IMAGE_VERSION",
            "value": "${GIT_LOCAL_BRANCH}-${GIT_COMMIT}",
            "sensitive": 0
        },
        {
            "name": "REMOTE_HOST",
            "value": "ec2-18-136-195-226.ap-southeast-1.compute.amazonaws.com",
            "sensitive": 0
        },
        {
            "name": "REMOTE_CRED",
            "value": "a742dc35-e67b-48ff-9a84-cefb1b605116",
            "sensitive": 0
        },
        {
            "name": "REMOTE_USER_NAME",
            "value": "ubuntu",
            "sensitive": 0
        }
    ]
}'
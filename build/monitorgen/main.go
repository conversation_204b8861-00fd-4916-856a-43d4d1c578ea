package main

import (
	"bytes"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"log"
	"os"
	"path"
	"strconv"
	"strings"
	"text/template"
)

type MethodInfo struct {
	Name    string
	Params  []Param
	Results []Param
}

type Param struct {
	Name       string
	Type       string
	IsVariadic bool // 新增字段
}

const (
	fullTemplateStr = `// Code generated by monitorgen; DO NOT EDIT.

package {{.Package}}

import (
	"context"
	"time"
	
	{{if .MonitorAlias}}{{.MonitorAlias}} {{end}}"{{.MonitorPkg}}"
)

{{range $type, $methods := .Types}}
// Monitor wrappers for {{$type}}
{{range $methods}}

func (s *{{$type}}) Monitored{{.Name}}({{template "params" .}}) {{template "returns" .}} {
	start := time.Now()
	defer func() {
		{{$.MonitorAlias}}.BusinessMethodTiming.Timing(ctx, "{{$type}}", time.Since(start).Milliseconds(), "{{.Name}}")
	}()
	return s.{{.Name}}({{range $i, $p := .Params}}{{if $i}}, {{end}}{{if $p.IsVariadic}} {{$p.Name}}...{{else}}{{$p.Name}}{{end}}{{end}})
}
{{end}}
{{end}}`

	paramTemplateStr = `{{define "params"}}ctx context.Context{{range $i, $p := .Params}}{{if not (isContext $p.Type)}}, {{if $p.Name}}{{$p.Name}}{{else}}p{{$i}}{{end}} {{$p.Type}}{{end}}{{end}}{{end}}`

	returnTemplateStr = `{{define "returns"}}({{if .Results}}{{range $i, $r := .Results}}{{if $i}}, {{end}}{{$r.Type}}{{end}}{{else}}error{{end}}){{end}}`
)

func main() {
	fset := token.NewFileSet()
	pkgs, err := parser.ParseDir(fset, ".", nil, parser.ParseComments)
	if err != nil {
		log.Fatal("Parse error:", err)
	}
	// 获取监控包别名和路径
	monitorPkg := "hotel/common/metrics"
	monitorAlias := getPackageAlias(monitorPkg)
	data := struct {
		Package      string
		MonitorPkg   string
		MonitorAlias string
		Types        map[string][]MethodInfo
	}{
		MonitorPkg:   monitorPkg,
		MonitorAlias: monitorAlias,
		Types:        make(map[string][]MethodInfo),
	}

	for _, pkg := range pkgs {
		data.Package = pkg.Name
		for _, file := range pkg.Files {
			ast.Inspect(file, func(n ast.Node) bool {
				switch t := n.(type) {
				case *ast.TypeSpec:
					if hasGenComment(t.Doc, "mon:gen") {
						data.Types[t.Name.Name] = []MethodInfo{}
					}
				case *ast.FuncDecl:
					if t.Recv != nil && hasGenComment(t.Doc, "mon:gen") {
						recvType := getReceiverType(t.Recv.List[0].Type)
						method := parseMethod(t)
						data.Types[recvType] = append(data.Types[recvType], method)
					}
				}
				return true
			})
		}
	}

	generateCode(data)
}

func generateCode(data interface{}) {
	tmpl := template.New("main").Funcs(template.FuncMap{
		"isContext": isContextType,
	})
	// 将全部模板内容合并解析
	tmpl = template.Must(tmpl.Parse(fullTemplateStr))
	tmpl = template.Must(tmpl.Parse(paramTemplateStr))
	tmpl = template.Must(tmpl.Parse(returnTemplateStr))

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		log.Fatal("Template execution error:", err)
	}

	formatted, err := format.Source(buf.Bytes())
	if err != nil {
		log.Fatal("Format error:", err)
	}

	if err := os.WriteFile("monitored_gen.go", formatted, 0644); err != nil {
		log.Fatal("Write file error:", err)
	}

	fmt.Println("Successfully generated monitored_gen.go")
}

// Helper functions
func hasGenComment(doc *ast.CommentGroup, tag string) bool {
	if doc == nil {
		return false
	}
	for _, c := range doc.List {
		if strings.Contains(c.Text, tag) {
			return true
		}
	}
	return false
}

func getReceiverType(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.StarExpr:
		return exprToString(t.X)
	case *ast.Ident:
		return t.Name
	default:
		return ""
	}
}

func parseMethod(fn *ast.FuncDecl) MethodInfo {
	mi := MethodInfo{Name: fn.Name.Name}

	// Parse parameters
	mi.Params = parseFieldList(fn.Type.Params, "p")

	// Parse results
	mi.Results = parseFieldList(fn.Type.Results, "r")

	return mi
}

func parseFieldList(fl *ast.FieldList, prefix string) []Param {
	if fl == nil {
		return nil
	}

	var params []Param
	for i, f := range fl.List {
		typeStr := exprToString(f.Type)
		var isVariadic bool
		// 处理可变参数语法
		if ellipsis, ok := f.Type.(*ast.Ellipsis); ok {
			typeStr = "..." + exprToString(ellipsis.Elt)
			isVariadic = true
		}

		if len(f.Names) > 0 {
			for _, name := range f.Names {
				params = append(params, Param{
					Name:       name.Name,
					Type:       typeStr,
					IsVariadic: isVariadic,
				})
			}
		} else {
			params = append(params, Param{
				Name:       fmt.Sprintf("%s%d", prefix, i),
				Type:       typeStr,
				IsVariadic: isVariadic,
			})
		}
	}
	return params
}

func exprToString(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.StarExpr:
		return "*" + exprToString(t.X)
	case *ast.SelectorExpr:
		return exprToString(t.X) + "." + t.Sel.Name
	case *ast.ArrayType:
		return "[]" + exprToString(t.Elt)
	case *ast.MapType:
		return "map[" + exprToString(t.Key) + "]" + exprToString(t.Value)
	case *ast.Ellipsis: // 处理可变参数
		return "..." + exprToString(t.Elt)
	default:
		return fmt.Sprintf("%T", expr)
	}
}

func isContextType(typ string) bool {
	return typ == "context.Context" || strings.HasSuffix(typ, ".Context")
}

func getPackageAlias(importPath string) string {
	// 处理带版本号的路径如：github.com/foo/bar/v3 → bar
	base := path.Base(importPath)
	if isVersionDir(base) {
		return path.Base(path.Dir(importPath))
	}
	return base
}

func isVersionDir(name string) bool {
	if len(name) < 2 {
		return false
	}
	if name[0] == 'v' {
		_, err := strconv.Atoi(name[1:])
		return err == nil
	}
	return false
}

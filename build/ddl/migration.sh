#!/bin/bash
@echo "flyway"
# 校准
flyway baseline -configFiles="${PROJECT_PATH}/build/ddl/hoteldb/flyway.toml,${PROJECT_PATH}/build/ddl/hoteldb/flyway.user.toml" -workingDirectory="${PROJECT_PATH}/build/ddl/hoteldb" -schemaModelLocation="schema-model" -schemaModelSchemas= -environment=hoteldev
# repair
flyway repair -configFiles="${PROJECT_PATH}/build/ddl/hoteldb/flyway.toml,${PROJECT_PATH}/build/ddl/hoteldb/flyway.user.toml" -workingDirectory="${PROJECT_PATH}/build/ddl/hoteldb" -schemaModelLocation="schema-model" -schemaModelSchemas= -environment=hoteldev
# 执行迁移
flyway migrate -configFiles="${PROJECT_PATH}/build/ddl/hoteldb/flyway.toml,${PROJECT_PATH}/build/ddl/hoteldb/flyway.user.toml" -workingDirectory="${PROJECT_PATH}/build/ddl/hoteldb" -schemaModelLocation="schema-model" -schemaModelSchemas= -environment=hoteldev

@echo "goctl"
goctl model mysql ddl --src ./trade/dao/order.sql --dir ./trade/dao/ -i 'id'
goctl model mysql ddl --src ./user/mysql/user.sql --dir ./user/mysql/ -i 'id'
goctl model mysql ddl --src ./content/mysql/hotel_extra.sql --dir ./content/mysql/ -i 'id'
goctl model mysql ddl --src ./content/mysql/starling.sql --dir ./content/mysql/ -i 'id'
goctl model mysql ddl --src ./supplier/mysql/room.sql --dir ./supplier/mysql/ -i 'id'

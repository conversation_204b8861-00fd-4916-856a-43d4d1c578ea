package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
)

func main() {
	// 把各个目录下的 xx.sql 文件收集到 build/ddl/hoteldb/migrations 目录，以供 flyway 执行migration
	projectRoot := os.Getenv("PROJECT_PATH") // 相对于当前 build/ddl 目录的项目根目录
	targetDir := filepath.Join(projectRoot, "build/ddl/hoteldb/migrations")
	ignoreDirs := []string{filepath.Join(projectRoot, "build/")}

	// 确保目标目录存在
	err := os.MkdirAll(targetDir, os.ModePerm)
	if err != nil {
		log.Fatalf("无法创建目标目录 %s: %v", targetDir, err)
	}

	fmt.Printf("开始收集 SQL 文件到 %s\n", targetDir)

	err = filepath.Walk(projectRoot, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			fmt.Printf("访问路径 %q 时出错: %v\n", path, err)
			return err
		}
		if info.IsDir() && strings.HasPrefix(info.Name(), ".") {
		}

		// 检查是否是文件并且以 .sql 结尾
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".sql") {
			// 检查文件是否在目标目录或其子目录中，如果是则跳过
			relPath, err := filepath.Rel(targetDir, path)
			if err == nil && !strings.HasPrefix(relPath, "..") {
				fmt.Printf("跳过目标目录中的文件: %s\n", path)
				return nil
			}

			// 检查是否在忽略目录中
			for _, ignoreDir := range ignoreDirs {
				if strings.HasPrefix(path, ignoreDir) {
					fmt.Printf("跳过忽略目录中的文件: %s\n", path)
					return nil
				}
			}

			// 构造目标文件路径
			// 001_20250430224554  time.Now().Format("20060102"),
			destPath := filepath.Join(targetDir, fmt.Sprintf("R__%s", info.Name())) // 直接使用原始文件名

			// 复制文件
			fmt.Printf("找到 SQL 文件: %s -> %s\n", path, destPath)
			err = copyFile(path, destPath)
			if err != nil {
				log.Printf("复制文件 %s 到 %s 时出错: %v", path, destPath, err)
				// 选择继续处理其他文件
				return nil
			}
		}
		return nil
	})

	if err != nil {
		log.Fatalf("遍历目录时出错: %v", err)
	}

	fmt.Println("SQL 文件收集完成。")
}

// copyFile 复制文件内容
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("无法打开源文件 %s: %w", src, err)
	}
	defer sourceFile.Close()

	destinationFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("无法创建目标文件 %s: %w", dst, err)
	}
	defer destinationFile.Close()

	_, err = io.Copy(destinationFile, sourceFile)
	if err != nil {
		return fmt.Errorf("无法从 %s 复制到 %s: %w", src, dst, err)
	}

	// 确保数据写入磁盘
	err = destinationFile.Sync()
	if err != nil {
		return fmt.Errorf("无法同步目标文件 %s: %w", dst, err)
	}

	return nil
}

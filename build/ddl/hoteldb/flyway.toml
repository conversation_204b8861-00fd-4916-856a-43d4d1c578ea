databaseType = "MySql"
id = "54c6a0af-5557-4e6a-a0db-3c0358668942"
name = "hoteldb"

[environments.hoteldev]
url = "************************************"
user = "root"
password = "123456"
schemas = ["hoteldev"]

[flyway]
locations = [ "filesystem:migrations" ]
mixed = true
outOfOrder = true
schemaModelLocation = "schema-model"
validateMigrationNaming = true

[flyway.check]
majorTolerance = 0

[flywayDesktop]
developmentEnvironment = "development"
shadowEnvironment = "shadow"

[redgateCompare]
filterFile = "filter.rgf"

[redgateCompare.mysql.options.ignores]
ignoreNewlinesInTextObjects = "off"
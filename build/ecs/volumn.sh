
#sudo fdisk -l
#sudo mkfs.ext4 /dev/xvdbb
#sudo mount /dev/xvdbb /app


sudo systemctl stop docker
sudo mkdir -p /tmp/docker-data
sudo rsync -aP /var/lib/docker/ /tmp/docker-data/
# 先卸载原有挂载（如果有）
sudo umount /app

# 格式化新磁盘（按需执行，注意：会清空数据！）
sudo mkfs.ext4 /dev/xvdb  # 替换为实际磁盘设备名

# 挂载到/app
sudo mount /dev/xvdb /app

# 确保挂载信息持久化（修改fstab）
sudo vi /etc/fstab
# 添加以下行（替换设备名）
/dev/xvdb       /app    ext4    defaults        0 0

# 验证挂载
sudo mount -a
df -h | grep /app
# 移除原Docker目录
sudo rm -rf /var/lib/docker

# 在/app下创建Docker目录并复制数据
sudo mkdir -p /app/docker
sudo rsync -aP /tmp/docker-data/ /app/docker/

# 创建符号链接
sudo ln -s /app/docker /var/lib/docker

# 恢复Docker服务
sudo systemctl start docker
docker info | grep "Docker Root Dir"  # 应显示/app/docker
docker ps  # 检查容器是否正常运行
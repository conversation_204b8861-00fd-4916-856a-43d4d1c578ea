# build

本目录为构建、脚本、自动化相关目录，包含数据库DDL、监控生成、API工具等。

## 主要内容

- `ddl/`：数据库建表脚本
- `monitorgen/`：监控配置生成
- `api/`：API工具/代码生成
- `dev/`：开发辅助脚本
- `scripts/`：数据库导入、部署等构建脚本

## 脚本目录 (scripts/)

包含各种构建和部署相关的脚本：

### 数据库导入脚本
- `import_hotel_data.sh`：完整的酒店数据导入脚本，支持多种运行模式
- `quick_import.sh`：快速导入脚本，适合快速执行
- `test_circuit_breaker_fix.sh`：熔断器测试脚本

### 使用方法
```bash
# 导入酒店数据到远程数据库
./build/scripts/import_hotel_data.sh

# 快速导入
./build/scripts/quick_import.sh

# 查看详细使用说明
cat build/scripts/README.md
```

## 迭代开发约定

- 所有脚本需有注释和使用说明
- 自动化工具需关注兼容性和可扩展性
- 变更需同步更新README
- 脚本应放在合适的子目录中，便于管理

## 注意事项

- 禁止将生产敏感信息写入脚本
- 脚本需适配多环境
- 变更需评估对主流程的影响
- 数据库相关脚本应包含适当的错误处理和验证

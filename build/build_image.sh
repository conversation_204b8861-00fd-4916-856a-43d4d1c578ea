#!/bin/bash
# 0. 登录 coding hub
docker login -u rwcGPnXeTS -p d727ffb557a76f0e29f37d901aaedb129b28c404 oscillator-docker.pkg.coding.net

# 1. 构建 Docker 镜像（当前目录，Dockerfile 需存在）
#docker build --platform linux/amd64 -t temp-image .
docker build --no-cache --platform linux/amd64 -t temp-image .

# 2. 提取 Git 信息（分支名、提交哈希）
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
GIT_COMMIT=$(git rev-parse --short HEAD)

# 3. 生成镜像标签（格式：分支名-提交哈希）
TAG="${GIT_BRANCH}-${GIT_COMMIT}"

# 4. 给镜像打标签（关联远程仓库）
REMOTE_REPO="oscillator-docker.pkg.coding.net/hotelcode/backend/golang-http-app"
docker tag temp-image ${REMOTE_REPO}:${TAG}

# 5. 推送镜像到远程仓库（需提前 docker login 登录）
docker push ${REMOTE_REPO}:${TAG}

# 6. （可选）清理临时标签
#docker rmi temp-image
#docker push oscillator-docker.pkg.coding.net/hotelcode/backend/golang-http-app:master-bc0477fe19672f1adc34f7dce3b177b79f482d7d
#!/bin/bash

# 酒店数据导入脚本
# 将本地MySQL数据库的hotel和hotel_name表导入到远程数据库

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 进度条函数
show_progress() {
    local current=$1
    local total=$2
    local width=50
    local percentage=$((current * 100 / total))
    local completed=$((width * current / total))
    local remaining=$((width - completed))
    
    printf "\r["
    printf "%${completed}s" | tr ' ' '#'
    printf "%${remaining}s" | tr ' ' '-'
    printf "] %d%%" $percentage
    
    if [ $current -eq $total ]; then
        echo ""
    fi
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 配置变量
LOCAL_HOST="127.0.0.1"
LOCAL_USER="root"
LOCAL_PASSWORD="123456"
LOCAL_DATABASE="hoteldev"

REMOTE_HOST="hoteltest.chayosky20ji.ap-southeast-1.rds.amazonaws.com"
REMOTE_USER="admin"
REMOTE_PASSWORD="Amit2025"
REMOTE_PORT="3306"
REMOTE_DATABASE="hoteltest"

# 备份文件目录和文件名
BACKUP_DIR="/Users/<USER>/Downloads/data"
BACKUP_FILE="$BACKUP_DIR/hotel_tables_backup.sql"
TABLES="hotel hotel_name"

# 检查并创建备份目录
check_backup_directory() {
    if [ ! -d "$BACKUP_DIR" ]; then
        log_info "创建备份目录: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
        if [ $? -ne 0 ]; then
            log_error "无法创建备份目录: $BACKUP_DIR"
            exit 1
        fi
    fi
    log_success "备份目录检查完成: $BACKUP_DIR"
}

# 检查MySQL客户端是否安装
check_mysql_client() {
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装，请先安装MySQL客户端"
        exit 1
    fi
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump工具未安装，请先安装MySQL客户端"
        exit 1
    fi
}

# 测试本地数据库连接
test_local_connection() {
    log_info "测试本地数据库连接..."
    if mysql -h "$LOCAL_HOST" -u "$LOCAL_USER" -p"$LOCAL_PASSWORD" -P 3306 "$LOCAL_DATABASE" -e "SELECT 1;" &> /dev/null; then
        log_success "本地数据库连接成功"
    else
        log_error "本地数据库连接失败，请检查配置"
        exit 1
    fi
}

# 测试远程数据库连接
test_remote_connection() {
    log_info "测试远程数据库连接..."
    if mysql -h "$REMOTE_HOST" -u "$REMOTE_USER" -p"$REMOTE_PASSWORD" -P "$REMOTE_PORT" "$REMOTE_DATABASE" -e "SELECT 1;" &> /dev/null; then
        log_success "远程数据库连接成功"
    else
        log_error "远程数据库连接失败，请检查配置"
        exit 1
    fi
}

# 检查表是否存在
check_tables_exist() {
    log_info "检查本地数据库中的表..."
    for table in $TABLES; do
        if mysql -h "$LOCAL_HOST" -u "$LOCAL_USER" -p"$LOCAL_PASSWORD" -P 3306 "$LOCAL_DATABASE" -e "SHOW TABLES LIKE '$table';" | grep -q "$table"; then
            log_success "表 $table 存在"
        else
            log_error "表 $table 不存在于本地数据库中"
            exit 1
        fi
    done
}

# 获取数据量对比
get_data_comparison() {
    log_step "获取数据量对比..."
    
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│                    数据量对比表                              │"
    echo "├─────────────────┬─────────────────┬─────────────────────────┤"
    echo "│      表名       │    本地数据库    │     远程数据库           │"
    echo "├─────────────────┼─────────────────┼─────────────────────────┤"
    
    for table in $TABLES; do
        # 使用 TABLE_ROWS 从 information_schema 获取近似行数，避免 COUNT(*) 查询
        local_count=$(mysql -h "$LOCAL_HOST" -u "$LOCAL_USER" -p"$LOCAL_PASSWORD" -P 3306 "$LOCAL_DATABASE" \
            -e "SELECT IFNULL(TABLE_ROWS, 0) FROM information_schema.TABLES WHERE TABLE_SCHEMA = '$LOCAL_DATABASE' AND TABLE_NAME = '$table';" -s -N 2>/dev/null)
        
        remote_count=$(mysql -h "$REMOTE_HOST" -u "$REMOTE_USER" -p"$REMOTE_PASSWORD" -P "$REMOTE_PORT" "$REMOTE_DATABASE" \
            -e "SELECT IFNULL(TABLE_ROWS, 0) FROM information_schema.TABLES WHERE TABLE_SCHEMA = '$REMOTE_DATABASE' AND TABLE_NAME = '$table';" -s -N 2>/dev/null)
        
        # 格式化显示
        printf "│ %-15s │ %-15s │ %-21s │\n" "$table" "$local_count" "$remote_count"
    done
    
    echo "└─────────────────┴─────────────────┴─────────────────────────┘"
    echo ""
    
    log_info "注意：显示的数据量是基于统计信息的近似值，可能与实际行数有差异"
}

# 导出数据（带进度显示）
export_data() {
    log_step "开始导出数据..."
    
    # 删除旧的备份文件
    if [ -f "$BACKUP_FILE" ]; then
        rm "$BACKUP_FILE"
        log_info "删除旧的备份文件"
    fi
    
    # 获取总数据量用于进度计算
    total_records=0
    for table in $TABLES; do
        count=$(mysql -h "$LOCAL_HOST" -u "$LOCAL_USER" -p"$LOCAL_PASSWORD" -P 3306 "$LOCAL_DATABASE" -e "SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null)
        total_records=$((total_records + count))
    done
    
    log_info "总计需要导出 $total_records 条记录"
    echo ""
    
    # 导出数据 - 修复MySQL 8.0兼容性问题，使用INSERT IGNORE，添加hex-blob处理JSON数据
    log_info "正在导出数据..."
    mysqldump -h "$LOCAL_HOST" -u "$LOCAL_USER" -p"$LOCAL_PASSWORD" -P 3306 \
        --default-character-set=utf8mb4 \
        --single-transaction \
        --skip-triggers \
        --skip-lock-tables \
        --no-create-db \
        --no-create-info \
        --insert-ignore \
        --hex-blob \
        --complete-insert \
        --extended-insert \
        --net_buffer_length=16384 \
        "$LOCAL_DATABASE" $TABLES > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "数据导出成功"
        log_info "备份文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"
    else
        log_error "数据导出失败"
        exit 1
    fi
}

# 导入数据
import_data() {
    log_step "开始导入数据到远程数据库..."
    
    if [ ! -f "$BACKUP_FILE" ]; then
        log_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    log_info "正在导入数据..."
    
    # 直接导入，不使用进度条（因为无法准确监控mysql导入进度）
    mysql -h "$REMOTE_HOST" -u "$REMOTE_USER" -p"$REMOTE_PASSWORD" -P "$REMOTE_PORT" \
        --default-character-set=utf8mb4 \
        "$REMOTE_DATABASE" < "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "数据导入成功"
    else
        log_error "数据导入失败"
        exit 1
    fi
}

# 验证导入结果
verify_import() {
    log_step "验证导入结果..."
    echo ""
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│                    导入结果验证                              │"
    echo "├─────────────────┬─────────────────┬─────────────────────────┤"
    echo "│      表名       │    导入前数量    │     导入后数量           │"
    echo "├─────────────────┼─────────────────┼─────────────────────────┤"
    
    for table in $TABLES; do
        # 获取导入后的数据量
        final_count=$(mysql -h "$REMOTE_HOST" -u "$REMOTE_USER" -p"$REMOTE_PASSWORD" -P "$REMOTE_PORT" "$REMOTE_DATABASE" -e "SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null)
        
        # 获取导入前的数据量（从之前的对比中获取）
        local_count=$(mysql -h "$LOCAL_HOST" -u "$LOCAL_USER" -p"$LOCAL_PASSWORD" -P 3306 "$LOCAL_DATABASE" -e "SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null)
        
        # 格式化显示
        printf "│ %-15s │ %-15s │ %-15s │\n" "$table" "$local_count" "$final_count"
    done
    
    echo "└─────────────────┴─────────────────┴─────────────────────────┘"
    echo ""
}

# 清理备份文件
cleanup() {
    if [ "$1" = "keep" ]; then
        log_info "保留备份文件: $BACKUP_FILE"
    else
        if [ -f "$BACKUP_FILE" ]; then
            rm "$BACKUP_FILE"
            log_info "清理备份文件"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "酒店数据导入脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -k, --keep     保留备份文件"
    echo "  -t, --test     仅测试连接，不执行导入"
    echo "  -v, --verify   仅验证远程数据库数据"
    echo "  -q, --quick    快速模式（跳过确认步骤）"
    echo ""
    echo "示例:"
    echo "  $0             执行完整导入流程"
    echo "  $0 -k          执行导入并保留备份文件"
    echo "  $0 -t          仅测试数据库连接"
    echo "  $0 -v          仅验证远程数据库数据"
    echo "  $0 -q          快速模式执行导入"
}

# 主函数
main() {
    local keep_backup=false
    local test_only=false
    local verify_only=false
    local quick_mode=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -k|--keep)
                keep_backup=true
                shift
                ;;
            -t|--test)
                test_only=true
                shift
                ;;
            -v|--verify)
                verify_only=true
                shift
                ;;
            -q|--quick)
                quick_mode=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "=== 酒店数据导入脚本 ==="
    log_info "本地数据库: $LOCAL_USER@$LOCAL_HOST:$LOCAL_DATABASE"
    log_info "远程数据库: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PORT/$REMOTE_DATABASE"
    log_info "目标表: $TABLES"
    echo ""
    
    # 检查依赖
    check_mysql_client
    check_backup_directory
    
    # 测试连接
    test_local_connection
    test_remote_connection
    
    if [ "$test_only" = true ]; then
        log_success "连接测试完成"
        exit 0
    fi
    
    if [ "$verify_only" = true ]; then
        get_data_comparison
        exit 0
    fi
    
    # 检查表是否存在
    check_tables_exist
    
    # 获取数据对比
    get_data_comparison
    
    # 确认操作（除非是快速模式）
    if [ "$quick_mode" = false ]; then
        log_warning "即将开始数据导入，使用INSERT IGNORE处理重复数据"
        read -p "是否继续？(y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
    
    # 执行导入流程
    export_data
    import_data
    verify_import
    
    # 清理
    if [ "$keep_backup" = true ]; then
        cleanup "keep"
    else
        cleanup
    fi
    
    log_success "数据导入完成！"
}

# 执行主函数
main "$@" 
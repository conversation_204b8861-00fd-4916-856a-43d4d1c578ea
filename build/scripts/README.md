# 酒店数据导入脚本

## 概述

`import_hotel_data.sh` 是一个用于将本地MySQL数据库中的酒店数据导入到远程数据库的脚本。

## 功能特性

- ✅ 支持MySQL 8.0兼容性
- ✅ 使用INSERT IGNORE处理重复数据
- ✅ 彩色日志输出
- ✅ 多种运行模式
- ✅ 完整的错误处理
- ✅ 数据验证功能

## 配置

脚本中的数据库配置（可根据需要修改）：

```bash
# 本地数据库配置
LOCAL_HOST="127.0.0.1"
LOCAL_USER="root"
LOCAL_PASSWORD="123456"
LOCAL_DATABASE="hoteldev"

# 远程数据库配置
REMOTE_HOST="hoteltest.chayosky20ji.ap-southeast-1.rds.amazonaws.com"
REMOTE_USER="admin"
REMOTE_PASSWORD="Amit2025"
REMOTE_PORT="3306"
REMOTE_DATABASE="hoteltest"
```

## 使用方法

### 基本用法

```bash
# 执行完整导入流程（需要确认）
./import_hotel_data.sh

# 快速模式（跳过确认步骤）
./import_hotel_data.sh -q

# 保留备份文件
./import_hotel_data.sh -k
```

### 高级用法

```bash
# 仅测试数据库连接
./import_hotel_data.sh -t

# 仅验证远程数据库数据
./import_hotel_data.sh -v

# 组合使用：快速模式 + 保留备份
./import_hotel_data.sh -q -k
```

### 命令行选项

| 选项 | 长选项 | 说明 |
|------|--------|------|
| `-h` | `--help` | 显示帮助信息 |
| `-k` | `--keep` | 保留备份文件 |
| `-t` | `--test` | 仅测试连接，不执行导入 |
| `-v` | `--verify` | 仅验证远程数据库数据 |
| `-q` | `--quick` | 快速模式（跳过确认步骤） |

## 导入的表

- `hotel` - 酒店基本信息表
- `hotel_name` - 酒店名称表

## 注意事项

1. **MySQL版本兼容性**: 脚本已修复MySQL 8.0兼容性问题
2. **重复数据处理**: 使用`INSERT IGNORE`处理主键冲突
3. **字符集**: 使用`utf8mb4`字符集确保中文支持
4. **备份文件**: 默认自动清理备份文件，可使用`-k`选项保留

## 故障排除

### 常见错误

1. **MySQL客户端未安装**
   ```bash
   # macOS
   brew install mysql-client
   
   # Ubuntu/Debian
   sudo apt-get install mysql-client
   ```

2. **连接失败**
   - 检查数据库配置是否正确
   - 确认网络连接正常
   - 验证数据库用户权限

3. **权限错误**
   - 确保数据库用户有足够的权限
   - 检查防火墙设置

## 示例输出

```
[INFO] === 酒店数据导入脚本 ===
[INFO] 本地数据库: root@127.0.0.1:hoteldev
[INFO] 远程数据库: <EMAIL>:3306/hoteltest
[INFO] 目标表: hotel hotel_name

[INFO] 测试本地数据库连接...
[SUCCESS] 本地数据库连接成功
[INFO] 测试远程数据库连接...
[SUCCESS] 远程数据库连接成功
[INFO] 检查本地数据库中的表...
[SUCCESS] 表 hotel 存在
[SUCCESS] 表 hotel_name 存在
[INFO] 获取表数据量...
[INFO] 表 hotel 有 1317893 条记录
[INFO] 表 hotel_name 有 2064790 条记录

[WARNING] 即将开始数据导入，使用INSERT IGNORE处理重复数据
是否继续？(y/N): y

[INFO] 开始导出数据...
[SUCCESS] 数据导出成功
[INFO] 备份文件大小: 67M
[INFO] 开始导入数据到远程数据库...
[SUCCESS] 数据导入成功
[INFO] 验证导入结果...
[SUCCESS] 远程数据库表 hotel 有 1317893 条记录
[SUCCESS] 远程数据库表 hotel_name 有 2064790 条记录
[INFO] 清理备份文件
[SUCCESS] 数据导入完成！
``` 
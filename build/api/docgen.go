package main

import (
	"container/list"
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"slices"
	"strings"
	"time"

	"hotel/build/api/asthelper"
	"hotel/common/config"
	"hotel/common/utils"

	"gopkg.in/yaml.v3"

	_ "net/http/pprof" // 匿名导入，自动注册分析路由

	"github.com/getkin/kin-openapi/openapi3"
	"golang.org/x/tools/go/packages"
)

var (
	rootDir string
)

func main() {
	st := time.Now()
	go func() {
		http.ListenAndServe("localhost:6060", nil)
	}()
	// 获取项目根目录
	var err error
	if rootDir, err = os.Getwd(); err != nil {
		log.Fatal(err)
	}

	if err = asthelper.InitModuleConfig(); err != nil {
		log.Fatal(err)
	}
	buildInternalDoc()
	fmt.Printf("buidInternalDoc done in %v\n", time.Since(st))
	st2 := time.Now()
	buildPublicDoc()
	fmt.Printf("buidPublicDoc done in %v\n", time.Since(st2))
	fmt.Printf("all done. costTime=%s", time.Since(st))
}

func buildInternalDoc() {
	// 初始化文档
	doc := &openapi3.T{
		OpenAPI: "3.0.0", // i want 3.1.0
		Info: &openapi3.Info{
			Title:       asthelper.DocTitle_Internal,
			Version:     "0.0.1",
			Description: `HotelByte is a hotel booking engine empowering B2B and B2C tenantes.`,
		},
		Paths: &openapi3.Paths{
			Extensions: nil,
			Origin:     nil,
		},
		Servers: openapi3.Servers{
			{
				URL:         "http://localhost:8081",
				Description: "Hotel API - Dev",
			},
			{
				URL:         "http://api-test.hotelbyte.com",
				Description: "Hotel API - Test (WIP)",
			},
			{
				URL:         "https://api.hotelbyte.com",
				Description: "Hotel API - Production (WIP)",
			},
		},
		Components: &openapi3.Components{
			Schemas: make(openapi3.Schemas),
		},
	}
	GenerateModels(doc, asthelper.TargetModelPkgIDs, nil)
	var targetServicePkgIDs = []string{
		"*/service",
	}
	// 定义全局标签顺序
	doc.Tags = openapi3.Tags{
		{Name: "Auth", Description: "Auth APIs"},
		{Name: "Admin.Hotelbyte.Com", Description: "admin.hotelbyte.com"},
		{Name: "Admin.Hotelbyte.Com/Tenant", Description: "admin.hotelbyte.com"},
		{Name: "Admin.Hotelbyte.Com/Customer", Description: "admin.hotelbyte.com"},
		{Name: "Admin.Hotelbyte.Com/Trade", Description: "admin.hotelbyte.com"},
		{Name: "Booking.Hotelbyte.Com", Description: "booking.hotelbyte.com"},
		{Name: "Booking.Hotelbyte.Com/Content", Description: "booking.hotelbyte.com"},
		{Name: "Booking.Hotelbyte.Com/Resource", Description: "booking.hotelbyte.com"},
		{Name: "Booking.Hotelbyte.Com/Trade", Description: "booking.hotelbyte.com"},
		{Name: "User", Description: "User APIs"},
		{Name: "Trade", Description: "Trade APIs"},
	}
	GenerateAPIs(doc, targetServicePkgIDs)
	// 生成文档
	if err := generateInternalYAML(doc); err != nil {
		log.Fatal(err)
	}

	if os.Getenv("UPLOAD") == "true" {
		log.Println("upload local docs to remote")
		if err := upload(path.Join(config.GetProjectPath(), "docs"), "hotel-be/docs/"); err != nil {
			panic(err)
		}
	}
}

func buildPublicDoc() {
	doc := &openapi3.T{
		OpenAPI: "3.0.0", // i want 3.1.0
		Info: &openapi3.Info{
			Title:       asthelper.DocTitle_Public,
			Version:     "0.0.1",
			Description: `HotelByte is a hotel booking engine empowering B2B and B2C businesses.`,
		},
		Paths: &openapi3.Paths{
			Extensions: nil,
			Origin:     nil,
		},
		Servers: openapi3.Servers{
			{
				URL:         "http://api-test.hotelbyte.com",
				Description: "Hotel API - Test (WIP)",
			},
			{
				URL:         "https://api.hotelbyte.com",
				Description: "Hotel API - Production (WIP)",
			},
		},
		Components: &openapi3.Components{
			Schemas: make(openapi3.Schemas),
		},
	}

	GenerateModels(doc, asthelper.PublicTargetModelPkgIDs, nil)
	var targetServicePkgIDs = []string{
		"*/service",
	}
	// 定义全局标签顺序
	doc.Tags = openapi3.Tags{
		{Name: "Auth", Description: "Auth APIs"},
		{Name: "Resource", Description: "Resource APIs"},
		{Name: "Trade", Description: "Trade APIs"},
	}
	GenerateAPIs(doc, targetServicePkgIDs)
	// 生成文档
	if err := generatePublicYAML(doc); err != nil {
		log.Fatal(err)
	}
}
func GenerateAPIs(doc *openapi3.T, targetPkgIDs []string) {
	asthelper.MountAllErrors(doc)
	cfg := &packages.Config{
		Mode: packages.NeedSyntax |
			packages.NeedTypes |
			packages.NeedImports |
			packages.NeedDeps,
	}
	pkgs, err := packages.Load(cfg, "./...")
	if err != nil {
		panic(err)
	}
	patterns := compilePatterns(targetPkgIDs)
	for _, pkg := range pkgs {
		// 计算相对模块路径
		relativePath := getRelativePath(pkg.ID)

		// 匹配目标模式
		if matchesAnyPattern(relativePath, patterns) {
			services := asthelper.FindServiceImplementations(pkg)
			for _, service := range services {
				for _, method := range service.Methods {
					asthelper.BuildPathItem(doc, service, method)
				}
			}
		}
	}
}
func GenerateModels(doc *openapi3.T, inclusive, exclusive []string) {
	// 确保先收集所有模型
	cfg := &packages.Config{
		Mode: packages.NeedName |
			packages.NeedTypes |
			packages.NeedSyntax |
			packages.NeedImports |
			packages.NeedDeps |
			packages.NeedTypesInfo, // 关键：获取类型信息
	}
	pkgs, _ := packages.Load(cfg, "./...")
	// 过滤目标包
	// 转换通配符为正则表达式
	inclusivePatterns := compilePatterns(inclusive)
	exclusivePatterns := compilePatterns(exclusive)
	defers := list.List{}
	// 收集所有类型定义
	graph := buildDependencyGraph(pkgs)
	sorted, err := topologicalSort(graph)
	if err != nil {
		panic(err)
	}

	for _, pkgID := range sorted {
		for _, pkg := range pkgs {
			if pkg.ID == pkgID {
				// 计算相对模块路径
				relativePath := getRelativePath(pkg.ID)

				if matchesAnyPattern(relativePath, exclusivePatterns) {
					continue
				}
				// 匹配目标模式
				if matchesAnyPattern(relativePath, inclusivePatterns) {
					defers.PushBackList(asthelper.ProcessModels(pkg, doc))
				}
			}
		}
	}

	for v := defers.Front(); v != nil; {
		v.Value.(func())()
		v = v.Next()
	}
}

// buildDependencyGraph 构建包的依赖关系图
func buildDependencyGraph(pkgs []*packages.Package) map[string][]string {
	graph := make(map[string][]string)
	for _, pkg := range pkgs {
		for _, imp := range pkg.Imports {
			graph[pkg.ID] = append(graph[pkg.ID], imp.ID)
		}
	}
	return graph
}

// topologicalSort 拓扑排序
func topologicalSort(graph map[string][]string) ([]string, error) {
	var sorted []string
	visited := make(map[string]bool)
	temp := make(map[string]bool)

	var visit func(node string) error
	visit = func(node string) error {
		if temp[node] {
			return fmt.Errorf("circular dependency detected: %s", node)
		}
		if !visited[node] {
			temp[node] = true
			for _, neighbor := range graph[node] {
				if err := visit(neighbor); err != nil {
					return err
				}
			}
			temp[node] = false
			visited[node] = true
			sorted = append(sorted, node)
		}
		return nil
	}

	for node := range graph {
		if err := visit(node); err != nil {
			return nil, err
		}
	}

	return sorted, nil
}

// 获取相对于模块的路径
func getRelativePath(fullPath string) string {
	return strings.TrimPrefix(fullPath, asthelper.ModuleName+"/")
}

// 编译通配符模式为相对路径正则
func compilePatterns(patterns []string) []*regexp.Regexp {
	var res []*regexp.Regexp
	for _, p := range patterns {
		// 转换通配符语法
		pattern := "^" + strings.ReplaceAll(p, "*", ".*") + "$"

		// 缓存编译结果
		re, _ := regexp.Compile(pattern)
		res = append(res, re)
	}
	return res
}

// 匹配任意模式
func matchesAnyPattern(path string, patterns []*regexp.Regexp) bool {
	for _, re := range patterns {
		if re.MatchString(path) {
			return true
		}
	}
	return false
}

func generateInternalYAML(doc *openapi3.T) error {
	outputDir := filepath.Join(rootDir, "docs/api")
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	yamlData, err := GenerateYAML(doc)
	if err != nil {
		return err
	}
	outputPath := filepath.Join(outputDir, "api.yaml")
	return os.WriteFile(outputPath, yamlData, 0644)
}

func generatePublicYAML(doc *openapi3.T) error {
	outputDir := filepath.Join(rootDir, "docs/api")
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	blacklistTypes := []string{
		// todo:
	}
	toRemove := []string{}
	for k, v := range doc.Components.Schemas {
		for _, b := range blacklistTypes {
			if v.Value.Type.Includes(b) {
				toRemove = append(toRemove, k)
			}
		}
	}
	for _, k := range toRemove {
		delete(doc.Components.Schemas, k)
	}
	for k, v := range doc.Paths.Map() {
		ops := []*openapi3.Operation{v.Get, v.Post}
		for _, op := range ops {
			if op == nil {
				continue
			}
			tags := utils.Filter(op.Tags, func(s string) bool {
				return s != "Openapi" // tag 都做了大写首字母处理，这里注意
			})
			if len(tags) == len(op.Tags) { // 不包含 openapi
				doc.Paths.Delete(k)
				continue
			}
			op.Tags = utils.Filter(tags, func(s string) bool {
				return !strings.Contains(s, ".Com") // tag 都做了大写首字母处理，这里注意
			})
		}
	}
	doc.Servers = openapi3.Servers{
		{
			URL:         "http://api-test.hotelbyte.com",
			Description: "HotelByte API - Test (WIP)",
		},
		{
			URL:         "https://api.hotelbyte.com",
			Description: "HotelByte API - Production (WIP)",
		},
	}
	// 定义全局标签顺序
	doc.Tags = openapi3.Tags{
		{Name: "Search", Description: "Search Resource"},
		{Name: "Trade", Description: "Trade Resource"},
	}
	yamlData, err := GenerateYAML(doc)
	if err != nil {
		return err
	}
	outputPath := filepath.Join(outputDir, "openapi.yaml")
	return os.WriteFile(outputPath, yamlData, 0644)
}

func validateOpenAPI(doc *openapi3.T) error {
	// 验证组件
	if err := doc.Components.Validate(context.Background()); err != nil {
		//return fmt.Errorf("components validation failed: %w", err)
		fmt.Printf("components validation failed: %v\n", err)
	}

	// 验证整体文档
	if err := doc.Validate(context.Background()); err != nil {
		//return fmt.Errorf("openapi validation failed: %w", err)
		fmt.Printf("validation failed: %v\n", err)
	}

	return nil
}

// GenerateYAML 使用自定义包装器序列化
func GenerateYAML(doc *openapi3.T) ([]byte, error) {
	// 1. 清理无效引用
	for k, v := range doc.Components.Schemas {
		if vv, updated := hideOneOf(v, ""); updated {
			doc.Components.Schemas[k] = vv
		}
	}
	for k, v := range doc.Components.Schemas {
		doc.Components.Schemas[k] = slimProperties(doc, v)
	}
	// 2. 验证文档
	if err := validateOpenAPI(doc); err != nil {
		fmt.Printf("validateOpenAPI failed: %v\n", err)
		return nil, err
	}

	// 3. 安全序列化
	type safeDoc openapi3.T
	return yaml.Marshal((*safeDoc)(doc))
}

func parseTitleGenericTypes(title string) []string {
	return utils.Filter(strings.Split(asthelper.ExtractGenericType(title), ","), func(s string) bool {
		return s != ""
	})
}

func slimProperties(doc *openapi3.T, rootRef *openapi3.SchemaRef) *openapi3.SchemaRef {
	if rootRef.Ref != "" && doc.Components.Schemas[asthelper.ExtractFullKeyFromRef(rootRef)] == nil {
		return asthelper.BasicTypeSchemas["object"].NewRef()
	}
	root := rootRef.Value
	if root == nil {
		return rootRef
	}

	for k, prop := range root.Properties {
		if v, ok := doc.Components.Schemas[prop.Value.Title]; ok && v != prop {
			// struct里走 select（外部包引用的）字段值会有重复，这里特殊处理下
			prop.Ref = asthelper.NewRefFromFullKey(prop.Value.Title)
		}
		root.Properties[k] = slimProperties(doc, prop)
	}
	return rootRef
}

func hideOneOf(rootRef *openapi3.SchemaRef, parentTitle string) (*openapi3.SchemaRef, bool) {
	//return rootRef, false
	root := rootRef.Value
	if root == nil {
		return rootRef, false
	}

	title := root.Title
	if slices.Contains(asthelper.GlobalGenericTitles, title) {
		//log.Printf("hideOneOf ignored %s\n", title)
		return rootRef, false
	}
	vs := parseTitleGenericTypes(title)
	if len(vs) == 0 {
		title = parentTitle
		vs = parseTitleGenericTypes(title)
	}
	if len(vs) > 0 && len(root.OneOf) > 0 {
		ofs := make(openapi3.SchemaRefs, 0, len(vs))
		var pairs []string
		for _, of := range root.OneOf {
			for _, vv := range vs {
				if strings.HasSuffix(of.Ref, vv) {
					ofs = append(ofs, of)
					break
				} else {
					pairs = append(pairs, of.Ref, vv)
				}
			}
		}

		if removed := len(root.OneOf) - len(ofs); removed > 0 {
			//log.Printf("rootTitle(%s) removed(%d) by pairs(%v), parentTitle(%s) title(%s) parsed(%v)\n", root.Title, removed, pairs, parentTitle, title, vs)
			if len(ofs) == 1 {
				return ofs[0], true
			}
			cop := *root
			cop.OneOf = ofs
			return &openapi3.SchemaRef{
				Ref:   rootRef.Ref,
				Value: &cop,
			}, true
		}
	}

	overwritePM := make(map[string]*openapi3.SchemaRef)
	for k, v := range root.Properties {
		if v == nil || v.Value == nil {
			continue
		}

		if vv, ok := hideOneOf(v, title); ok {
			overwritePM[k] = vv
		}
	}
	if len(overwritePM) > 0 {
		cop := *root
		cop.Properties = make(map[string]*openapi3.SchemaRef)
		for k, v := range root.Properties {
			cop.Properties[k] = v
		}
		for k, v := range overwritePM {
			cop.Properties[k] = v
		}

		tmp := *rootRef
		tmp.Value = &cop
		return &tmp, true
	}

	if root.Items != nil {
		if vv, ok := hideOneOf(root.Items, title); ok {
			cop := *root
			cop.Items = vv
			tmp := *rootRef
			tmp.Value = &cop
			return &tmp, true
		}
	}
	return rootRef, false
}

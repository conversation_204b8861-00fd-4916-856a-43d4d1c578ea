#!/bin/bash

# 配置参数
APIFOX_API="https://api.apifox.cn/api/v1"
OPENAPI_FILE="/Users/<USER>/GolandProjects/hotel-be/docs/openapi.yaml"          # OpenAPI文件路径
# ---------- 关键修复 ----------
# 确保脚本从项目根目录执行（解决路径问题）
cd "$(dirname "$0")" || exit 1

# 检查文件存在性
if [ ! -f "$OPENAPI_FILE" ]; then
  echo "❌ 错误：文件不存在或路径错误: $OPENAPI_FILE"
  exit 1
fi

# 使用更可靠的参数名（Apifox 实际需要 'schema' 参数）
# 这个API暂时没有在Apifox官网上公布，暂不能用
curl -X POST \
  -H "X-Apifox-Version: 2024-05-20" \
  -H "Authorization: Bearer ${APIFOX_TOKEN}" \
  -F "schema=@${OPENAPI_FILE}" \       # 参数名必须为 schema
  -F "importType=override" \           # 强制覆盖已有文档
  "${APIFOX_API}/projects/${PROJECT_ID}/import-data"

echo ${APIFOX_TOKEN}
echo ${OPENAPI_FILE}
echo ${APIFOX_API}
echo ${PROJECT_ID}
# 检查执行结果
if [ $? -eq 0 ]; then
  echo "✅ OpenAPI 文件同步成功"
else
  echo "❌ 同步失败，请检查网络或配置"
  exit 1
fi
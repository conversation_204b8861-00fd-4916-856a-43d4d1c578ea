package asthelper

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"testing"
)

func TestComment(t *testing.T) {
	src := `
package protocol
type ButtonEntrance struct {
    SupplierHotelId       string ` + "`json:\"id\"`" + `       // 跟前端约定的值，前端根据这个 id 执行相应的动作
    Disabled bool   ` + "`json:\"disabled\"`" + ` // 是否禁用，处理用户可见，但不可点击的按钮
    // 按钮提示文案
    Hint i18n.I18N ` + "`json:\"hint\"`" + `
}`

	// 创建文件集
	fset := token.NewFileSet()

	// 解析源代码
	node, err := parser.ParseFile(fset, "", src, parser.ParseComments)
	if err != nil {
		fmt.Println(err)
		return
	}

	// 遍历AST节点
	ast.Inspect(node, func(n ast.Node) bool {
		// 查找结构体类型声明
		if ts, ok := n.(*ast.TypeSpec); ok {
			if ts.Name.Name == "ButtonEntrance" {
				// 查找结构体字段
				if st, ok := ts.Type.(*ast.StructType); ok {
					for _, field := range st.Fields.List {
						// 打印字段名
						for _, name := range field.Names {
							fmt.Printf("Field: %s\n", name.Name)
						}
						fmt.Printf("Desc: %s\n", extractDescription(field.Doc, field.Comment))
						// 打印行前注释
						if field.Doc != nil {
							for _, comment := range field.Doc.List {
								fmt.Printf("Line Comment: %s\n", comment.Text)
							}
						}
						// 打印行内注释
						if field.Comment != nil {
							for _, comment := range field.Comment.List {
								fmt.Printf("Inline Comment: %s\n", comment.Text)
							}
						}
					}
				}
			}
		}
		return true
	})
}

package asthelper

import (
	"sort"

	"github.com/getkin/kin-openapi/openapi3"

	"hotel/common/bizerr"
	"hotel/common/utils"
)

func buildResponses(method *Method, doc *openapi3.T) *openapi3.Responses {
	responses := openapi3.NewResponses()

	// 默认成功响应
	successSchema := &openapi3.Schema{}
	sort.Slice(method.Responses, func(i, j int) bool {
		return method.Responses[i].Code < method.Responses[j].Code
	})
	for _, resp := range method.Responses {
		if v := buildSuccessDataSchema(resp, doc); v != nil {
			successSchema.OneOf = append(successSchema.OneOf, v)
		}
	}
	if len(successSchema.OneOf) == 1 {
		successSchema = successSchema.OneOf[0].Value //todo: slim
	}

	headerSchema := doc.Components.Schemas[ResponseHeaderFullKey]
	doc.Components.Headers = func() openapi3.Headers {
		out := openapi3.Headers{}
		for k, v := range headerSchema.Value.Properties {
			out[k] = &openapi3.HeaderRef{
				Value: &openapi3.Header{
					Parameter: openapi3.Parameter{Schema: v},
				},
			}
		}
		return out
	}()
	successResp := &openapi3.ResponseRef{
		Value: &openapi3.Response{
			Description: utils.String2Pointer("Success"),
			Headers:     doc.Components.Headers,
			Content: openapi3.NewContentWithSchema(
				successSchema,
				[]string{"application/json"},
			),
		},
	}
	responses.Set("200", successResp)
	responses.Set("400", &openapi3.ResponseRef{
		Value: openapi3.NewResponse().
			WithDescription("Failed"),
	})
	responses.Set("401", &openapi3.ResponseRef{
		Value: openapi3.NewResponse().
			WithDescription("Not Authenticated"),
	})
	responses.Set("403", &openapi3.ResponseRef{
		Value: openapi3.NewResponse().
			WithDescription("Not Authorized"),
	})

	return responses
}

// common/response_builder.go
func WithDescription(s *openapi3.Schema, desc string) *openapi3.Schema {
	s.Description = desc
	return s
}

// 辅助函数：判断是否基础类型
func isBasicType(types ...string) bool {
	for _, t := range types {
		if BasicTypeSchemas[t] != nil {
			continue
		}
		return false
	}
	return true
}

func getBasicTypeSchema(name string) *openapi3.Schema {
	return BasicTypeSchemas[name]
}

// 基础类型映射表
var BasicTypeSchemas = map[string]*openapi3.Schema{
	"github.com.shopspring.decimal.Decimal": openapi3.NewFloat64Schema().WithFormat("decimal"),
	"object":                                openapi3.NewObjectSchema(),
	"int":                                   openapi3.NewIntegerSchema(),
	"uint8":                                 openapi3.NewIntegerSchema(),
	"int8":                                  openapi3.NewIntegerSchema(),
	"int16":                                 openapi3.NewIntegerSchema(),
	"byte":                                  openapi3.NewStringSchema().WithFormat("byte"),
	"int32":                                 openapi3.NewInt32Schema().WithFormat("int32"),
	"uint32":                                openapi3.NewInt64Schema().WithFormat("uint32"),
	"uint16":                                openapi3.NewInt32Schema().WithFormat("uint16"),
	"uint64":                                openapi3.NewInt64Schema().WithFormat("uint64"),
	"int64":                                 openapi3.NewInt64Schema().WithFormat("int64"),
	"string":                                openapi3.NewStringSchema(),
	"bool":                                  openapi3.NewBoolSchema(),
	"float64":                               openapi3.NewFloat64Schema(),
	"time.Duration":                         openapi3.NewInt64Schema().WithFormat("time.Duration"),
	"map.string.interface{}":                openapi3.NewObjectSchema(),
	"any":                                   openapi3.NewObjectSchema(),
	"time.Time":                             WithDescription(openapi3.NewStringSchema().WithFormat("string"), `time.RFC3339, // "2006-01-02T15:04:05Z07:00"`),
	"time":                                  WithDescription(openapi3.NewStringSchema().WithFormat("string"), `time.RFC3339, // "2006-01-02T15:04:05Z07:00"`),
	"error":                                 WithDescription(openapi3.NewStringSchema().WithFormat("string"), `error`),
	"encoding.json.RawMessage":              WithDescription(openapi3.NewStringSchema().WithFormat("string"), `JSON`),
	// 添加其他基础类型...
}

func convertBizerrToSchema(b *bizerr.BizError) *openapi3.SchemaRef {
	code := openapi3.NewIntegerSchema()
	code.Example = b.StatusCode()
	msg := openapi3.NewStringSchema()
	msg.Example = b.StatusMessage()
	return openapi3.NewObjectSchema().
		WithProperty("code", code).
		WithProperty("msg", msg).NewRef()
}

func buildSuccessDataSchema(spec ResponseSpec, doc *openapi3.T) *openapi3.SchemaRef {
	v := spec.DataType.FullKey
	schema := searchSchemaFromDoc(doc, v)
	if spec.Code != "200" {
		if be := FindBizError(v, spec.DataType.Description); be != nil {
			if schema != nil {
				return NewSchemaRefFromFullKey(v) // 不直接返回 schema 省空间
			}
			// schema为空
			return convertBizerrToSchema(be)
		}
	}

	schemaRef := openapi3.NewObjectSchema().
		WithProperty("code", openapi3.NewIntegerSchema()).
		WithProperty("msg", openapi3.NewStringSchema())
	// 检查是否是基础类型
	if baseSchema, isBasic := BasicTypeSchemas[v]; isBasic {
		schemaRef.WithProperty("data", baseSchema)
	} else if schema != nil {
		schemaRef.WithPropertyRef("data", NewSchemaRefFromFullKey(v)) // 不直接返回 schema 省空间
	}
	return schemaRef.NewRef()
}

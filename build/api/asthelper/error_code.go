package asthelper

import (
	"github.com/spf13/cast"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"

	"hotel/common/bizerr"
)

var (
	_allErrMap = map[string]*bizerr.BizError{}
)

func init() {
	for _, err := range bizerr.GetAllBizErrors() {
		_allErrMap[err.Name()] = err
		_allErrMap[cast.ToString(err.StatusCode())] = err
		// todo: 1个 httpcode可能有多个
		httpCode := cast.ToString(err.HTTPCode())
		if _, ok := _allErrMap[httpCode]; !ok {
			_allErrMap[httpCode] = err
		}
	}
}

func FindBizError(s, desc string) *bizerr.BizError {
	vs := strings.Split(s, ".")
	if v := _allErrMap[vs[len(vs)-1]]; v != nil {
		return v
	}
	return _allErrMap[desc]
}

func MountAllErrors(doc *openapi3.T) {
	//todo: 对外对内错误码分开？根据 code 大小？
	for _, err := range bizerr.GetAllBizErrors() {
		is := openapi3.NewIntegerSchema()
		is.Example = err.StatusCode()

		ss := openapi3.NewStringSchema()
		ss.Example = err.StatusMessage()
		name := err.Name()
		if name == "" {
			continue
		}
		fk := "hotel.common.bizerr." + name
		doc.Components.Schemas[fk] = &openapi3.SchemaRef{
			Value: &openapi3.Schema{
				Title:       fk,
				Type:        &openapi3.Types{"object"},
				Description: err.Name(),
				Properties: map[string]*openapi3.SchemaRef{
					"code":    is.NewRef(),
					"message": ss.NewRef(),
				},
			},
		}
	}
}

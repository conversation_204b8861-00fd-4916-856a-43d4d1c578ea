package asthelper

import (
	"container/list"
	"fmt"
	"go/ast"
	"go/token"
	"go/types"
	"path"
	"regexp"
	"strings"
	"sync"
	"unicode"

	"github.com/hashicorp/go-set/v3"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/spf13/cast"
	"golang.org/x/tools/go/packages"

	"hotel/common/log"

	"hotel/common/utils"
)

var (
	pkgCache     sync.Map // importPath -> *packages.Package
	typeKeyCache sync.Map // map[ast.Expr]string 表达式到类型键的缓存
	keyTypeCache sync.Map // map[ast.Expr]string 表达式到类型键的缓存
)

type TypeParser struct {
	doc *openapi3.T
	pkg *packages.Package

	schemaMu sync.RWMutex // 保护doc的写操作
	parseMu  sync.Mutex   // 专门用于防止解析递归死锁
	defers   *list.List
}

func (p *TypeParser) APIDocTitle() string {
	return p.doc.Info.Title
}

func NewTypeParser(pkg *packages.Package, doc *openapi3.T) *TypeParser {
	return &TypeParser{
		doc:    doc,
		pkg:    pkg,
		defers: list.New(),
	}
}

// ParseAllDecls 解析整个包的所有类型声明
func (p *TypeParser) ParseAllDecls() {
	for _, file := range p.pkg.Syntax {
		pkgAliases := parseFileImports(file)

		for _, decl := range file.Decls {
			// 处理类型声明（如 type User struct）
			if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.TYPE {
				for _, spec := range genDecl.Specs {
					if typeSpec, ok := spec.(*ast.TypeSpec); ok {
						ctx := &ParseContext{
							importAlias: pkgAliases,
							Doc:         genDecl.Doc,
							Comment:     typeSpec.Comment,
						}
						p.parseTypeSpec(typeSpec, ctx) // 调用类型声明解析
					}
				}
			}
		}
	}
}

// parseTypeSpec 处理类型声明（如 type User struct）
func (p *TypeParser) parseTypeSpec(typeSpec *ast.TypeSpec, ctx *ParseContext) {
	typeName := typeSpec.Name.Name
	if p.APIDocTitle() == DocTitle_Public && strings.HasPrefix(typeName, "Internal") { // hardcode
		//log.Info("hide Internal schema(%s) in Public API doc", typeName)
		return
	}
	fk := p.generateStructKey(typeName)
	if v := p.getRealSchemaFromDoc(fk); v != nil {
		p.updateSchemaInDoc(fk, updateDescription(v, ctx.Doc, ctx.Comment))
	} else {
		p.occupySchema(fk) // 占位符
		p.parseTypeSpecOccupied(typeSpec, ctx, fk)
	}
}

func (p *TypeParser) parseTypeSpecOccupied(typeSpec *ast.TypeSpec, ctx *ParseContext, fullKey string) {
	var (
		schema *openapi3.SchemaRef
		// 获取底层类型表达式（如 "string" 或 "int"）
		underlyingType = "default"
	)
	if typeSpec.TypeParams != nil {
		gt := typeSpec.TypeParams.List[0].Type // todo: 目前仅支持单个泛型参数！
		ctx.GenericValue = gt
	}
	ctx.FullKey = fullKey
	switch t := typeSpec.Type.(type) {
	case *ast.StructType:
		underlyingType = "StructType"
		schema = p.parseStruct(t, ctx)
	case *ast.Ident:
		// 处理类型定义（如 type T string）
		underlyingType = t.Name
		schema = p.parse(t, ctx)
	case *ast.SelectorExpr:
		// 处理外部类型（如 time.Time）
		underlyingType = fmt.Sprintf("%s.%s", t.X, t.Sel.Name)
		schema = p.parse(t, ctx)
	default:
		schema = p.parse(t, ctx)
	}

	if isEnumType(p.pkg, typeSpec) {
		enumSchema := generateEnumSchema(p.pkg, typeSpec, underlyingType)
		p.updateSchemaInDoc(fullKey, enumSchema)
	} else {
		p.updateSchemaInDoc(fullKey, schema)
	}
}

func updateDescription(ref *openapi3.SchemaRef, doc, comment *ast.CommentGroup) *openapi3.SchemaRef {
	schemaCopy := *ref.Value // don't use deep copy
	schemaCopy.Description = extractDescription(doc, comment)
	return &openapi3.SchemaRef{
		Value: &schemaCopy,
		Ref:   ref.Ref,
	}
}

func copyRef(ref *openapi3.SchemaRef) *openapi3.SchemaRef {
	schemaCopy := *ref.Value // don't use deep copy
	return &openapi3.SchemaRef{
		Ref:   ref.Ref,
		Value: &schemaCopy,
	}
}

// 内部无锁解析核心
func (p *TypeParser) parse(expr ast.Expr, ctx *ParseContext) (schema *openapi3.SchemaRef) {
	// 实际解析（无锁环境）
	var (
		tp string
	)
	fk := ctx.FullKey
	if fk == "" {
		fk = p.generateTypeKey(expr)
	}
	if ctx.GenericValue != nil {
		fk = getGenericFullKey(fk, ctx.GenericValue)
		ctx.FullKey = fk
	}
	defer func() {
		if schema != nil && schema.Value != nil && ctx != nil {
			if schema.Value.Description == "" {
				schema.Value.Description = extractDescription(ctx.Doc, ctx.Comment)
			}
			schema.Value.Nullable = isNullableFromField(ctx.Field)
		}
		// 这里 nil 也要允许 update！
		// 只有部分类型需要写 schema
		if strings.HasPrefix(fk, "hotel.") {
			p.updateSchemaInDoc(fk, schema)
		} else {
			p.deleteSchema(fk)
		}
		if schema == nil {
			p.deleteSchema(fk)
		}
	}()

	if cached := p.getRealSchemaFromDoc(fk); cached != nil {
		return copyRef(cached)
	}

	vs := strings.Split(fk, ".")
	last := vs[len(vs)-1]
	// hide schema automatically
	if p.APIDocTitle() == DocTitle_Public && strings.HasPrefix(last, "Internal") {
		return nil
	}

	if v := getBasicTypeSchema(last); v != nil {
		return copyRef(v.NewRef())
	}

	p.occupySchema(fk)
	switch t := expr.(type) {
	case *ast.Ident:
		tp = "Ident"
		schema = p.parseIdent(t, ctx)
	case *ast.IndexExpr:
		tp = "IndexExpr"
		fk, schema = p.parseIndexExpr(fk, t, ctx)
	case *ast.IndexListExpr:
		tp = "IndexListExpr"
		log.Info("parseIndexListExpr not supported yet")
	case *ast.StarExpr:
		tp = "StarExpr"
		schema = p.parsePointer(t, ctx)
	case *ast.ArrayType:
		tp = "ArrayType"
		ctx.FullKey = ""
		schema = p.parseArray(t, ctx)
	case *ast.StructType:
		tp = "StructType"
		ctx.FullKey = ""
		schema = p.parseStruct(t, ctx)
	case *ast.SelectorExpr:
		tp = "SelectorExpr"
		schema = p.parseSelector(t, ctx)
	case *ast.MapType:
		tp = "MapType"
		ctx.FullKey = ""
		schema = p.parseMap(t, ctx)
	case *ast.InterfaceType:
		tp = "InterfaceType"
		ctx.FullKey = ""
		schema = p.parseInterface(t, ctx)
	case *ast.FuncType:
		return nil
	default:
		tp = "Unknown"
		log.Info("parse %s unknown: %T %T\n", fk, t, tp)
		return nil
	}
	// 这里不能 copyRef
	return schema
}

// 生成结构体的完整组件键名
func (p *TypeParser) generateStructKey(typeName string) string {
	return fullComponentName(p.pkg.PkgPath, typeName)
}

func suffixAdd(a, b string) string {
	if strings.HasSuffix(a, b) {
		return a
	}
	return a + b
}

func getGenericFullKey(fk string, expr ast.Expr) string {
	if expr == nil {
		return fk
	}
	switch v := expr.(type) {
	case *ast.Ident:
		if v.Name == "any" || v.Name == "T" {
			return fk
		}
		return fk + v.Name
	case *ast.SelectorExpr:
		if v.Sel.Name == "any" || v.Sel.Name == "T" {
			return fk
		}
		return fk + v.Sel.Name
	default:
		return fk
	}
}

// parseIndexExpr 处理泛型类型表达式
func (p *TypeParser) parseIndexExpr(fk string, ide *ast.IndexExpr, ctx *ParseContext) (string, *openapi3.SchemaRef) {
	// 延迟到具体类型拿到的时候再解决
	ctx2 := *ctx
	if ctx.GenericValue == nil {
		ctx2.GenericValue = ide.Index
	}
	return fk, p.parse(ide.X, &ctx2)
}

func (p *TypeParser) parseMap(expr *ast.MapType, ctx *ParseContext) *openapi3.SchemaRef {
	keySchema := p.parse(expr.Key, &ParseContext{importAlias: ctx.importAlias, GenericValue: ctx.GenericValue})
	valueSchema := p.parse(expr.Value, &ParseContext{importAlias: ctx.importAlias, GenericValue: ctx.GenericValue})

	sc := defaultSchemaRef()
	if valueSchema != nil && valueSchema.Value != nil {
		sc = valueSchema
	}
	// 创建符合OpenAPI规范的map schema
	return &openapi3.SchemaRef{
		Value: &openapi3.Schema{
			Type:  &openapi3.Types{"object"},
			Items: sc,
			Extensions: map[string]interface{}{
				"x-go-map-key": keySchema,
			},
		},
	}
}

func (p *TypeParser) getRealSchemaFromDoc(key string) *openapi3.SchemaRef {
	p.schemaMu.Lock()
	defer p.schemaMu.Unlock()
	v := p.doc.Components.Schemas[key]
	if v == nil {
		return nil
	}
	if isDefaultSchema(v.Value) {
		return nil
	}
	return v
}

func (p *TypeParser) getSchemaFromDoc(key string) *openapi3.SchemaRef {
	p.schemaMu.Lock()
	defer p.schemaMu.Unlock()
	return p.doc.Components.Schemas[key]
}
func (p *TypeParser) searchSchemaFromDoc(key string) *openapi3.SchemaRef {
	p.schemaMu.Lock()
	defer p.schemaMu.Unlock()
	return searchSchemaFromDoc(p.doc, key)
}

func searchSchemaFromDoc(doc *openapi3.T, key string) *openapi3.SchemaRef {
	v := doc.Components.Schemas[key]
	if v != nil {
		return v
	}
	key = "." + key
	for k, v := range doc.Components.Schemas {
		if strings.HasSuffix(k, key) {
			return v
		}
	}
	return nil
}

func (p *TypeParser) searchExpr(key string) (out ast.Expr) {
	if key == "" {
		return nil
	}
	vs := strings.Split(key, ".")
	key = vs[len(vs)-1]
	keyTypeCache.Range(func(k, v any) bool {
		if strings.HasSuffix(k.(string), key) {
			out = v.(ast.Expr)
			return false
		}
		return true
	})
	return nil
}

// 处理结构体类型（核心实现）
func (p *TypeParser) parseStruct(st *ast.StructType, ctx *ParseContext) *openapi3.SchemaRef {
	schema := openapi3.NewObjectSchema()
	fieldCnt := len(st.Fields.List)
	for _, field := range st.Fields.List {
		// 创建字段级上下文
		fieldCtx := &ParseContext{
			importAlias:  ctx.importAlias,
			Doc:          field.Doc,
			Comment:      field.Comment,
			Field:        field,
			GenericValue: ctx.GenericValue,
		}
		// 获取字段名和是否跳过(匿名字段存在 tag 的话可以整体跳过
		jsonName, skip := getAPIFieldName(field, p.APIDocTitle())
		if skip {
			continue
		}

		var tagdocGenerics []string
		genericTypeArg := ExtractGenericTypeArg(field)
		if genericTypeArg != "" { // 自动解析，跟注释一样的效果
			vs := strings.Split(genericTypeArg, ".")
			suffixKey := vs[len(vs)-1]
			if suffixKey == "T" || suffixKey == "any" {
				tagdocGenerics = set.From[string](extractTagValueFromDocComments(field.Doc, field.Comment, "generic")).Slice()
			} else {
				tagdocGenerics = []string{suffixKey}
			}
		}
		if fieldCnt == 1 && len(tagdocGenerics) > 0 {
			p.defers.PushFront(func() {
				gtk := tagdocGenerics[0]
				if ref := p.searchSchemaFromDoc(gtk); ref != nil {
					schema.Title = ctx.FullKey + "[" + gtk + "]"
				}
			})
		}

		fieldCtx.GenericTypes = tagdocGenerics //important
		gtk := p.generateTypeKey(fieldCtx.GenericValue)
		genericComm := utils.Filter(append(tagdocGenerics, gtk),
			func(s string) bool {
				return s != "" && s != "any" && s != "T"
			})

		ft := field.Type
		isGeneric := genericTypeArg == "T"
		if isGeneric {
			fieldCtx.GenericTypes = ctx.GenericTypes
			genericComm = append(genericComm, ctx.GenericTypes...)
			ft = fieldCtx.GenericValue
		}

		genericComm = set.From[string](genericComm).Slice()

		// 处理匿名字段（嵌入结构体）
		if len(field.Names) == 0 {
			if embeddedRef := p.parse(ft, fieldCtx); embeddedRef != nil {
				if embeddedRef.Value != nil {
					schema.Properties = mergeMaps(schema.Properties, embeddedRef.Value.Properties)
					schema.Required = set.From(append(schema.Required, embeddedRef.Value.Required...)).Slice()
				}
				p.defers.PushFront(func() {
					field := field // shadow
					fk := p.generateTypeKey(field.Type)
					embeddedRef = p.getRealSchemaFromDoc(fk)
					if embeddedRef == nil {
						//log.Warn("embedded schema ref not found by key(%s)", fk)
						return
					}
					schema.Properties = mergeMaps(schema.Properties, embeddedRef.Value.Properties)
					schema.Required = set.From(append(schema.Required, embeddedRef.Value.Required...)).Slice()
				})
			}
			continue
		}

		// type Example struct {
		//    A, B, C int // 这是一个 field, 不做支持，请不要写这样的代码
		//}
		if unicode.IsLower(rune(field.Names[0].Name[0])) {
			continue
		}

		fieldSchema := p.parse(ft, fieldCtx)
		if fieldSchema == nil {
			continue
		}

		if len(tagdocGenerics) > 0 {
			if fieldSchema.Value.Title != "" {
				fieldSchema.Value.Title = fieldSchema.Value.Title + "[" + strings.Join(tagdocGenerics, ",") + "]"
			}
		}
		if isGeneric && len(genericComm) > 0 {
			p.defers.PushFront(func() {
				if strings.Contains(fieldSchema.Value.Title, "[") {
					cv := *fieldSchema.Value
					fieldSchema.Value = &cv
				}
				var titles []string
				for _, v := range genericComm {
					if ref := p.searchSchemaFromDoc(v); ref != nil && !isDefaultSchema(ref.Value) {
						titles = append(titles, v)
						fieldSchema.Value.OneOf = append(fieldSchema.Value.OneOf, NewSchemaRefFromFullKey(ref.Value.Title))
					} else {
						log.Info("searchSchemaFromDoc not found %s", v)
					}
				}
				titles = set.From[string](titles).Slice()
				if fieldSchema.Value.Title != "" {
					fieldSchema.Value.Title = fieldSchema.Value.Title + "[" + strings.Join(titles, ",") + "]"
				}
			})
		}
		schema.Properties[jsonName] = fieldSchema
		// 处理必填字段
		if isRequiredField(field) {
			schema.Required = set.From(append(schema.Required, jsonName)).Slice()
		}
	}

	return schema.NewRef()
}

type ParseContext struct {
	importAlias map[string]string

	// for description
	Doc     *ast.CommentGroup
	Comment *ast.CommentGroup
	Field   *ast.Field

	GenericValue ast.Expr
	GenericTypes []string

	FullKey string
}

// 处理选择器表达式（如pkg.Type）
func (p *TypeParser) parseSelector(sel *ast.SelectorExpr, ctx *ParseContext) *openapi3.SchemaRef {
	pkgAlias, ok := sel.X.(*ast.Ident)
	if !ok {
		fmt.Printf("parseSelector unsupported: %s\n", sel.Sel.Name)
		return defaultSchemaRef() // 无法解析的表达式
	}

	typeName := sel.Sel.Name
	return p.Select(pkgAlias.Name, typeName, ctx)
}

func (p *TypeParser) Select(alias, tn string, ctx *ParseContext) *openapi3.SchemaRef {
	// 根据别名获取导入路径
	importPath := ctx.importAlias[alias]
	if importPath == "" {
		//alias = utils, tn = DateInt
		//alias = time, tn = Time
		importPath = alias
	}

	// 基础类型处理
	fk := fullComponentName(importPath, tn) // override it
	if schema := getBasicTypeSchema(fk); schema != nil {
		return updateDescription(schema.NewRef(), ctx.Doc, ctx.Comment)
	}
	// 已存在则直接返回
	if v := p.getRealSchemaFromDoc(fk); v != nil {
		return updateDescription(v, ctx.Doc, ctx.Comment)
	}

	if !strings.HasPrefix(importPath, ModuleName) {
		if ref := p.findTypeAndParse(fk, tn, ctx); ref != nil {
			return ref
		}
		// 加载外部包并解析
		if externalPkg := p.loadExternalPackage(importPath); externalPkg != nil {
			externalDocComp := openapi3.NewComponents()
			externalDocComp.Schemas = make(openapi3.Schemas)
			externalDoc := *p.doc
			externalDoc.Components = &externalDocComp
			externalParser := NewTypeParser(externalPkg, &externalDoc)
			if v := externalParser.findTypeAndParse(fk, tn, ctx); v != nil {
				//p.occupySchema(fk, v)
				p.updateSchemaInDoc(fk, v)
				// 合并Schemas
				for key, ref := range externalParser.doc.Components.Schemas {
					p.updateSchemaInDoc(key, ref)
				}
				return v
			}
		} else {
			log.Info("loadExternalPackage failed: %s", importPath)
		}
	}

	return NewSchemaRefFromFullKey(fk)
}

func (p *TypeParser) findTypeAndParse(fk, tn string, ctx *ParseContext) *openapi3.SchemaRef {
	// 查找目标类型
	if importAlias, targetType := findTypeRecursive(p.pkg, tn); targetType != nil {
		ctx2 := *ctx
		ctx2.importAlias = importAlias
		//ctx2.parent = ctx
		// targetType: Ident, int,
		return p.parse(targetType, &ctx2)
	} else {
		//log.Info("findTypeRecursive failed: %s, fk:%s", tn, fk)
	}
	return nil
}

// 新增：递归查找类型（支持所有类型）
func findTypeRecursive(pkg *packages.Package, typeName string) (map[string]string, ast.Expr) {
	for _, file := range pkg.Syntax {
		if t := findTypeInFile(file, typeName); t != nil {
			return parseFileImports(file), t
		}
	}
	return nil, nil
}

// 在单个文件中查找类型
func findTypeInFile(file *ast.File, typeName string) ast.Expr {
	for _, decl := range file.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.TYPE {
			for _, spec := range genDecl.Specs {
				if typeSpec, ok := spec.(*ast.TypeSpec); ok && typeSpec.Name.Name == typeName {
					return typeSpec.Type
				}
			}
		}
	}
	return nil
}

func NewSchemaRefFromFullKey(fk string) *openapi3.SchemaRef {
	return NewSchemaRef(NewRefFromFullKey(fk))
}

func NewRefFromFullKey(fk string) string {
	return "#/components/schemas/" + fk
}
func ExtractFullKeyFromRef(ref *openapi3.SchemaRef) string {
	parts := strings.Split(ref.Ref, "/")
	return parts[len(parts)-1]
}

func NewSchemaRef(ref string) *openapi3.SchemaRef {
	return openapi3.NewSchemaRef(ref, defaultSchema())
}

// 默认Schema（字符串类型）
func defaultSchemaRef() *openapi3.SchemaRef {
	return &openapi3.SchemaRef{
		Value: defaultSchema(),
	}
}

// 默认Schema（字符串类型）
func defaultSchema() *openapi3.Schema {
	return &openapi3.Schema{
		Type:        &openapi3.Types{openapi3.TypeObject},
		Description: "default",
	}
}

func isDefaultSchema(v *openapi3.Schema) bool {
	if v == nil {
		return false
	}
	return v.Type.Is(openapi3.TypeObject) && v.Description == "default"
}

// --- 辅助方法 ---
func mergeMaps(m1, m2 map[string]*openapi3.SchemaRef) map[string]*openapi3.SchemaRef {
	for k, v := range m2 {
		if _, exists := m1[k]; !exists && v != nil && v.Value != nil {
			m1[k] = v
		}
	}
	return m1
}

// 示例方法实现（均无锁）
func (p *TypeParser) parsePointer(expr *ast.StarExpr, ctx *ParseContext) *openapi3.SchemaRef {
	return p.parse(expr.X, ctx)
}

// isNullableFromTag 从结构体标签中判断是否允许 null
func isNullableFromField(f *ast.Field) bool {
	if f == nil {
		return true
	}
	jsonTag := getValueFromTag(f, "json")
	// 解析类似 `json:"field,nullable"` 的格式
	parts := strings.Split(jsonTag, ",")
	for _, p := range parts[1:] { // 忽略字段名部分
		if strings.TrimSpace(p) == "nullable" {
			return true
		}
	}
	return false
}
func (p *TypeParser) parseArray(expr *ast.ArrayType, ctx *ParseContext) *openapi3.SchemaRef {
	ctx2 := *ctx
	elementRef := p.parse(expr.Elt, &ctx2)
	if elementRef == nil {
		log.Info("unknown element, use default: %T", expr)
		elementRef = defaultSchemaRef()
	}
	return &openapi3.SchemaRef{
		Value: &openapi3.Schema{
			Type:     &openapi3.Types{openapi3.TypeArray},
			Nullable: isNullableFromField(ctx.Field),
			Items:    elementRef,
		},
	}
}

func (p *TypeParser) parseIdent(ident *ast.Ident, ctx *ParseContext) *openapi3.SchemaRef {
	// 1. 基础类型处理
	if schema := getBasicTypeSchema(ident.Name); schema != nil {
		return updateDescription(schema.NewRef(), ctx.Doc, ctx.Comment)
	}

	fullKey := ctx.FullKey
	if fullKey == "" {
		// 2. 获取完整的类型键名（包含包路径）
		fullKey = p.generateTypeKey(ident)
	}
	if strings.HasSuffix(fullKey, ".T") {
		return nil
	}

	// 3. 检查缓存是否存在
	cached := p.getRealSchemaFromDoc(fullKey)
	if cached != nil {
		return updateDescription(cached, ctx.Doc, ctx.Comment)
	}
	if !strings.HasPrefix(fullKey, ModuleName) {
		vs := strings.Split(fullKey, ".")
		k := vs[len(vs)-1]
		if unicode.IsLower(rune(k[0])) {
			return nil
		}
		//log.Info("go into findTypeAndParse %s %s", fullKey, k)
		return p.findTypeAndParse(fullKey, k, ctx)
	}
	return NewSchemaRefFromFullKey(fullKey)
}

func (p *TypeParser) occupySchema(fk string) {
	p.schemaMu.Lock()
	if p.doc.Components.Schemas[fk] == nil {
		p.doc.Components.Schemas[fk] = defaultSchemaRef()
	}
	p.schemaMu.Unlock()
}
func (p *TypeParser) deleteSchema(key string) {
	p.schemaMu.Lock()
	defer p.schemaMu.Unlock()
	delete(p.doc.Components.Schemas, key)
}

func (p *TypeParser) updateSchemaInDoc(key string, ref *openapi3.SchemaRef) {
	if ref == nil {
		return
	}

	if ref.Value != nil {
		p.schemaMu.Lock()
		defer p.schemaMu.Unlock()
		if existing, ok := p.doc.Components.Schemas[key]; ok && existing != nil {
			if ref.Ref == "" || isDefaultSchema(existing.Value) {
				if ref.Value.Title == "" {
					ref.Value.Title = key
				}
				p.doc.Components.Schemas[key] = ref
			}
		}
	}
}

// 生成唯一类型标识
// 增强版类型键生成（需结合类型信息）
func (p *TypeParser) generateTypeKey(expr ast.Expr) string {
	if expr == nil {
		return ""
	}
	// 检查缓存
	if cached, ok := typeKeyCache.Load(expr); ok {
		return cached.(string)
	}

	// 实际生成逻辑
	key := p.generateTypeKeyUncached(expr)
	typeKeyCache.Store(expr, key)
	keyTypeCache.Store(key, expr)
	return key
}
func (p *TypeParser) generateTypeKeyUncached(expr ast.Expr) (out string) {
	defer func() {
		out = strings.ReplaceAll(out, "/", ".")
		out = strings.ReplaceAll(out, "*", "pointer.")
		out = strings.ReplaceAll(out, "[]", "array.")
	}()
	// 优先使用类型系统信息
	if p.pkg.TypesInfo != nil {
		if typeInfo := p.pkg.TypesInfo.TypeOf(expr); typeInfo != nil {
			switch t := typeInfo.(type) {
			case *types.Named:
				return p.fullyQualifiedName(t)
			case *types.Basic:
				return t.Name()
			case *types.Pointer:
				return p.generateTypeKeyForType(t.Elem())
			case *types.Slice:
				return "array_of_" + p.generateTypeKeyForType(t.Elem())
			case *types.Array:
				return "array_of_" + cast.ToString(t.Len()) + "_" + p.generateTypeKeyForType(t.Elem())
			case *types.Map:
				return fmt.Sprintf("map_%s_%s",
					p.generateTypeKeyForType(t.Key()),
					p.generateTypeKeyForType(t.Elem()),
				)
			case *types.Struct:
				return p.generateAnonymousStructKey(t)
			case *types.Interface, *types.Alias, *types.TypeParam: // TypeParam.Name = "T"

			default:
				log.Error("unknown type: %T", t)
			}
		}
	}

	// 处理AST未关联类型信息的情况
	switch t := expr.(type) {
	case *ast.StarExpr:
		return p.generateTypeKeyUncached(t.X)
	case *ast.ArrayType:
		return "array_of_" + p.generateTypeKeyUncached(t.Elt)
	case *ast.SelectorExpr:
		return p.generateSelectorKey(t)
	case *ast.StructType:
		return p.generateAnonymousStructKey(nil)
	case *ast.Ident:
		return p.generateIdentKey(t)
	case *ast.MapType:
		return fmt.Sprintf("map_%s_%s",
			p.generateTypeKeyUncached(t.Key),
			p.generateTypeKeyUncached(t.Value),
		)
	case *ast.InterfaceType:
		if t.Methods == nil || len(t.Methods.List) == 0 {
			return "object"
		}
		return fmt.Sprintf("array.%d", len(t.Methods.List))
	default:
		log.Error("unknown expr: %T", expr)
		return fmt.Sprintf("unhandled_%T", expr)
	}
}

// 在TypeParser结构体中添加接口类型处理方法
func (p *TypeParser) parseInterface(expr *ast.InterfaceType, ctx *ParseContext) *openapi3.SchemaRef {
	// 处理空接口 interface{}
	if expr.Methods == nil || len(expr.Methods.List) == 0 {
		return &openapi3.SchemaRef{
			Value: &openapi3.Schema{
				Description: "empty interface",
				AnyOf: []*openapi3.SchemaRef{
					openapi3.NewStringSchema().NewRef(),
					openapi3.NewIntegerSchema().NewRef(),
					openapi3.NewObjectSchema().NewRef(),
				},
				Extensions: map[string]interface{}{
					"x-go-interface": "interface{}",
				},
			},
		}
	}
	ref := defaultSchemaRef()
	if v := extractTagValueFromDocComments(ctx.Doc, ctx.Comment, "autowire"); len(v) > 0 && v[0] == "true" {
		for _, impl := range findImplementations(p.pkg, expr) {
			impl := impl
			p.defers.PushFront(func() {
				if ref.Value == nil {
					return
				}
				if v := p.searchSchemaFromDoc(impl); v != nil {
					ref.Value.OneOf = append(ref.Value.OneOf, v)
				} else {
					log.Info("unknown impl: %s", impl)
				}
			})

		}
	}
	return ref
}

// 解析接口方法签名
func (p *TypeParser) parseInterfaceMethods(expr *ast.InterfaceType) []string {
	var methods []string
	for _, method := range expr.Methods.List {
		if len(method.Names) > 0 {
			methodName := method.Names[0].Name
			methods = append(methods, methodName)
		}
	}
	return methods
}

// 辅助方法集
func (p *TypeParser) generateTypeKeyForType(t types.Type) string {
	if named, ok := t.(*types.Named); ok {
		return p.fullyQualifiedName(named)
	}

	v := t.String() // 基本类型直接返回
	if isBasicType(v) {
		return v
	}
	return "object"
}

func (p *TypeParser) fullyQualifiedName(named *types.Named) string {
	pkg := named.Obj().Pkg()
	if pkg == nil {
		return named.Obj().Name() // 内置类型
	}
	return fmt.Sprintf("%s.%s",
		strings.ReplaceAll(pkg.Path(), "/", "."),
		named.Obj().Name(),
	)
}

func (p *TypeParser) generateSelectorKey(sel *ast.SelectorExpr) string {
	if pkgIdent, ok := sel.X.(*ast.Ident); ok {
		if obj := p.pkg.TypesInfo.ObjectOf(pkgIdent); obj != nil {
			if pkgName, ok := obj.(*types.PkgName); ok {
				return fmt.Sprintf("%s.%s",
					strings.ReplaceAll(pkgName.Imported().Path(), "/", "."),
					sel.Sel.Name,
				)
			}
		}
	}
	return fmt.Sprintf("%s.%s", sel.X, sel.Sel.Name) // 降级处理
}

func (p *TypeParser) generateIdentKey(ident *ast.Ident) string {
	//if isBasicType(ident.Name) {
	//	return ident.Name
	//}

	if obj := p.pkg.TypesInfo.ObjectOf(ident); obj != nil {
		if pkg := obj.Pkg(); pkg != nil {
			return fullComponentName(pkg.Path(), obj.Name())
		}
		return obj.Name() // 内置标识符
	}
	return ident.Name // 最后降级方案
}

func (p *TypeParser) generateAnonymousStructKey(t *types.Struct) string {
	if t == nil {
		return "anonStruct@<ast>" // AST未关联类型信息
	}
	// 为类型系统结构体生成唯一哈希
	return fmt.Sprintf("anonStruct@%p", t)
}

// 包路径限定器（生成完整路径）
func (p *TypeParser) qualifier(pkg *types.Package) string {
	if pkg == p.pkg.Types {
		return "" // 当前包省略路径
	}
	return pkg.Path()
}

func (p *TypeParser) resolveImportPath(alias string) string {
	// 获取主模块路径（从go.mod）
	mainModule := getMainModulePath(p.pkg) // 示例实现见下方

	for _, file := range p.pkg.Syntax {
		for _, imp := range file.Imports {
			importPath := strings.Trim(imp.Path.Value, `"`)

			// 处理带别名的导入
			if imp.Name != nil {
				if imp.Name.Name == alias {
					return resolveInternalPath(mainModule, importPath)
				}
			} else {
				// 处理默认导入
				if path.Base(importPath) == alias {
					return resolveInternalPath(mainModule, importPath)
				}
			}
		}
	}
	return ""
}

// 获取主模块路径（示例实现）
func getMainModulePath(pkg *packages.Package) string {
	for _, file := range pkg.Syntax {
		if strings.HasSuffix(file.Name.Name, "_test") {
			continue
		}
		if pkg.Module != nil {
			return pkg.Module.Path
		}
	}
	return ""
}

var (
	// \[([^\]]+)\] 匹配方括号内的任意字符（不包括方括号本身）
	extractGenericTypeRe = regexp.MustCompile(`\[([^\]]+)\]`)
)

func ExtractGenericType(input string) string {
	// 暂时不能用
	//input := "bff.Table[UserDetailBasic]"
	if strings.Contains(input, "[") {
		//fmt.Println(input)
	}
	// 查找匹配项
	matches := extractGenericTypeRe.FindStringSubmatch(input)
	if len(matches) < 2 {
		return ""
	}

	// 提取捕获组中的内容（即方括号内的部分）
	//extracted := matches[1]
	//fmt.Println("Extracted:", extracted) // 输出: UserDetailBasic
	return matches[1]
}

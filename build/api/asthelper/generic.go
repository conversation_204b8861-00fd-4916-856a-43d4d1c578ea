package asthelper

import (
	"fmt"
	"go/ast"
)

// ExtractGenericTypeArg 从AST字段中提取泛型类型参数（如 domain.OrderSummary）
func ExtractGenericTypeArg(field *ast.Field) string {
	if field == nil || field.Type == nil {
		return ""
	}
	switch t := field.Type.(type) {
	case *ast.IndexExpr:
		//检查是否为索引表达式（如 bff.Table[T]）
		// 处理类型参数
		if selector, ok := t.Index.(*ast.SelectorExpr); ok {
			return fmt.Sprintf("%s.%s", selector.X.(*ast.Ident).Name, selector.Sel.Name)
		}
		if ident, ok := t.Index.(*ast.Ident); ok {
			return ident.Name
		}
	case *ast.Ident:
		if t.Name == "T" {
			return t.Name
		}
	case *ast.StarExpr:
		if vv, ok := t.X.(*ast.Ident); ok && vv.Name == "T" {
			return vv.Name
		}
	}
	return ""
}

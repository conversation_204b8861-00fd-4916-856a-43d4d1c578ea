package asthelper

import "testing"

func Test_isStdLibPackage(t *testing.T) {
	type args struct {
		importPath string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "",
			args: args{"hotel/common/i18n"},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isStdLibPackage(tt.args.importPath); got != tt.want {
				t.Errorf("isStdLibPackage() = %v, want %v", got, tt.want)
			}
		})
	}
}

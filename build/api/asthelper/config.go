package asthelper

import (
	"fmt"
	"os"

	"github.com/getkin/kin-openapi/openapi3"
	"golang.org/x/mod/modfile"
)

// 完整组件键名生成
func fullComponentName(pkgPath, typeName string) string {
	if pkgPath == "" {
		return typeName
	}
	return formatComponentKey(fmt.Sprintf("%s.%s", pkgPath, typeName))
}

// 将包路径转换为组件键名前缀
func formatComponentKey(pkgPath string) string {
	// 示例输入: "your-module/hotel/supplier/ctrip/model"
	// 示例输入: "your-module.hotel.supplier.ctrip/model"
	// 输出: "hotel.supplier.ctrip.model"
	// Step 2: 区分自有模块和第三方包
	if isInternalPackage(pkgPath) {
		// 去除Go模块前缀
		//trimmed := strings.TrimPrefix(strings.TrimPrefix(strings.TrimPrefix(pkgPath, ModuleName), "."), "/")
		// 替换路径分隔符为点号
		return replaceSlashes(pkgPath)
	}
	// 第三方包处理（保留完整路径）
	return replaceSlashes(pkgPath)
}

// GetSchemaRef 返回类型对应的OpenAPI组件路径
func GetSchemaRef(desc *TypeDescriptor) *openapi3.SchemaRef {
	// 处理基础类型（int/string等）
	if isBasicType(desc.FullKey) {
		return buildBasicSchema(desc)
	}
	// 处理自定义类型引用
	return buildCustomSchema(desc)
}

// 初始化模块配置（项目启动时调用）
func InitModuleConfig() error {
	data, err := os.ReadFile("go.mod")
	if err != nil {
		return fmt.Errorf("go.mod文件读取失败: %w", err)
	}

	modFile, err := modfile.Parse("go.mod", data, nil)
	if err != nil {
		return fmt.Errorf("go.mod解析失败: %w", err)
	}

	ModuleName = modFile.Module.Mod.Path
	return nil
}

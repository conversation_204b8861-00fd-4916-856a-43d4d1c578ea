package asthelper

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

func buildRequestBody(method *Method, doc *openapi3.T) *openapi3.RequestBodyRef {
	for _, p := range method.Params {
		switch p.In {
		case "body":
			schema := GetSchemaRef(p.Types[0])
			return &openapi3.RequestBodyRef{
				Value: openapi3.NewRequestBody().
					WithDescription(p.Description).
					WithRequired(true).
					WithContent(openapi3.Content{
						"application/json": &openapi3.MediaType{
							Schema: schema, // 直接使用SchemaRef
						},
					}),
			}
		case "header":
			// 处理header参数
			param := openapi3.NewHeaderParameter(p.Name)
			param.Description = p.Description
			param.Required = p.Required
			param.Schema = GetSchemaRef(p.Types[0])
		}
	}
	return nil
}

// 解析单个复杂类型表达式
// 支持格式：[]*[]*pkg.Type, [][]int, ​**​*model.Data 等
func (m *Method) parseSingleType(typeStr string) *TypeDescriptor {
	if v, ok := typeDescCache.Load(typeStr); ok {
		return v.(*TypeDescriptor)
	}

	// 步骤1：提取指针和数组标记
	ptrCount, dims := parseTypeModifiers(typeStr)

	// 步骤2：解析基础类型名称
	baseType := m.parseBaseType(typeStr)

	out := &TypeDescriptor{
		FullKey:    baseType,
		IsPointer:  ptrCount > 0,
		Dimensions: dims,
	}
	typeDescCache.Store(typeStr, out)
	return out
}
func (m *Method) parseBaseType(typeStr string) string {
	// 步骤1：去除所有修饰符（指针和数组）
	base := strings.ReplaceAll(typeStr, "*", "")
	base = strings.ReplaceAll(base, "[]", "")

	// 步骤2：处理导入别名（如 model.User → hotel.supplier.model.User）
	if parts := strings.Split(base, "."); len(parts) > 1 {
		if realPkg, ok := m.ImportAlias[parts[0]]; ok {
			// 转换别名到实际包路径
			formattedPkg := formatComponentKey(realPkg)
			base = fmt.Sprintf("%s.%s", formattedPkg, strings.Join(parts[1:], "."))
		}
	}

	// 步骤3：补全本地类型包路径
	if !strings.Contains(base, ".") {
		base = fmt.Sprintf("%s.%s", m.CurrentPkgPath, base)
	}

	return formatComponentKey(base)
}

// 正则表达式匹配类型修饰符
var (
	typeModifierRe = regexp.MustCompile(`^(\*|$$$$)+`)
)

// 解析指针和数组修饰符
func parseTypeModifiers(typeStr string) (ptrCount, dims int) {
	matches := typeModifierRe.FindAllStringSubmatch(typeStr, -1)
	if len(matches) == 0 {
		return 0, 0
	}

	modifiers := matches[0][0]
	for _, c := range modifiers {
		switch c {
		case '*':
			ptrCount++
		case '[': // 遇到[表示数组维度+1
			dims++
		}
	}
	return
}

func buildParameters(method *Method, doc *openapi3.T) openapi3.Parameters {
	var params openapi3.Parameters

	for _, p := range method.Params {
		// 跳过body参数（由RequestBody处理）
		if p.In == "body" {
			continue
		}

		// 根据参数位置创建对应类型的Parameter
		var param *openapi3.Parameter
		switch p.In {
		case "path":
			param = openapi3.NewPathParameter(p.Name)
		case "query":
			param = openapi3.NewQueryParameter(p.Name)
		case "header":
			param = openapi3.NewHeaderParameter(p.Name)
		case "cookie":
			param = openapi3.NewCookieParameter(p.Name)
		default:
			continue // 忽略未知类型
		}

		// 设置公共属性
		param.Description = p.Description
		param.Required = p.Required
		param.Schema = GetSchemaRef(p.Types[0])

		params = append(params, &openapi3.ParameterRef{
			Value: param,
		})
	}

	return params
}

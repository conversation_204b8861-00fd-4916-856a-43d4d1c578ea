// struct_registry.go
package asthelper

import (
	"go/ast"
	"log"
	"sync"

	"github.com/getkin/kin-openapi/openapi3"
	"golang.org/x/tools/go/packages"
)

type StructRegistry struct {
	sync.RWMutex
	data map[string]*StructMeta // key: pkgPath.TypeName
}

func (s *StructRegistry) Add(k string, v *StructMeta) {
	log.Printf("注册组件: fullKey=%s pkgPath=%v typeName=%s", k, v.PkgPath, v.TypeName)
	s.Lock()
	s.data[k] = v
	s.Unlock()
}
func (s *StructRegistry) Find(k string) (*StructMeta, bool) {
	s.RLock()
	out, ok := s.data[k]
	s.RUnlock()
	return out, ok
}
func (s *StructRegistry) Exists(k string) bool {
	s.RLock()
	_, ok := s.data[k]
	s.RUnlock()
	return ok
}

// 结构体元数据
type StructMeta struct {
	PkgPath      string
	TypeName     string
	AstStruct    *ast.StructType
	AstType      ast.Expr
	IsThirdParty bool
}

type TypeDescriptor struct {
	FullKey     string // 基础类型名（如 "user.domain.Privilege"）
	IsPointer   bool   // 是否是指针
	Dimensions  int    // 数组维度（例如 [][]int → 2）
	Description string
}

// 构建自定义类型Schema
func buildCustomSchema(desc *TypeDescriptor) *openapi3.SchemaRef {
	refPath := "#/components/schemas/" + formatComponentKey(desc.FullKey)
	refSchema := openapi3.NewSchemaRef(refPath, defaultSchema())
	//refSchema.Value.Description = desc.Description

	// 处理数组维度
	for i := 0; i < desc.Dimensions; i++ {
		arraySchema := openapi3.NewArraySchema()
		arraySchema.Items = refSchema
		refSchema = openapi3.NewSchemaRef("", arraySchema)
	}

	return refSchema
}

// 构建基础类型Schema
func buildBasicSchema(desc *TypeDescriptor) *openapi3.SchemaRef {
	schema := cloneSchema(BasicTypeSchemas[desc.FullKey])

	// 处理数组维度
	for i := 0; i < desc.Dimensions; i++ {
		arraySchema := openapi3.NewArraySchema()
		arraySchema.Items = openapi3.NewSchemaRef("", schema)
		schema = arraySchema
	}

	schema.Description = desc.Description
	return openapi3.NewSchemaRef("", schema)
}

// 注册第三方包中的类型
func registerThirdPartyTypes(pkg *packages.Package) {
	ast.Inspect(pkg.Syntax[0], func(n ast.Node) bool {
		typeSpec, ok := n.(*ast.TypeSpec)
		if !ok {
			return true
		}

		key := fullComponentName(pkg.PkgPath, typeSpec.Name.Name)
		structRegistry.Add(key, &StructMeta{
			PkgPath:      pkg.PkgPath,
			TypeName:     typeSpec.Name.Name,
			AstType:      typeSpec.Type,
			IsThirdParty: true,
		})
		return true
	})
}

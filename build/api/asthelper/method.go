package asthelper

import (
	"go/ast"
	"log"
	"regexp"
	"strings"
)

type Method struct {
	Name           string
	APIPath        string // parsed from doc or converted from name
	Doc            string
	RequestType    *TypeDescriptor // 请求参数类型（如 "hotel.user.protocol.RegisterCustomerUserReq"）
	ResponseType   *TypeDescriptor // 响应数据类型（如 "hotel.user.protocol.RegisterCustomerUserResp"）
	Params         []ParamSpec     // 新增：参数描述
	Responses      []ResponseSpec  // 新增：响应描述
	Source         *ast.FuncDecl
	ImportAlias    map[string]string
	CurrentPkgPath string
	ServiceName    string
	// cache
	fieldDescCache map[string]string
	commentCache   map[string]*ParamSpec
	Meta           *MethodMeta
}

func (m *Method) GetResponseBaseType() string {
	if m == nil || m.ResponseType == nil {
		return ""
	}
	return m.ResponseType.FullKey
}
func (m *Method) GetRequestBaseType() string {
	if m == nil || m.RequestType == nil {
		return ""
	}
	return m.RequestType.FullKey
}

func (m *Method) parseParam(i int, field *ast.Field) (ParamSpec, bool) {
	paramType := m.parseTypeExpr(field.Type)

	// 1. 自动解析结构体字段
	if isStructType(field.Type, m.ImportAlias) {
		structParams := m.parseStructParam(field)
		m.Params = append(m.Params, structParams...)
		return ParamSpec{}, false
	}

	// 2. 处理基础类型参数
	param := ParamSpec{
		Name:        generateParamName(i),
		Types:       []*TypeDescriptor{paramType},
		Required:    true, // 默认body参数必填
		Description: extractFieldDescription(field),
		In:          "body",
	}

	// 3. 应用注释覆盖
	if commentSpec := m.parseParamComment(m.Doc, i); commentSpec != nil {
		param = *commentSpec
	}

	return param, true
}

// 解析结构体参数（生产级实现）
func (m *Method) parseStructParam(field *ast.Field) []ParamSpec {
	// 获取字段类型定义的真实包路径
	realPkgPath := m.getFieldPkgPath(field.Type)

	// 创建子解析上下文
	subParser := &Method{
		CurrentPkgPath: realPkgPath,
		ImportAlias:    m.ImportAlias,
	}

	typeDesc := subParser.parseTypeExpr(field.Type)
	if typeDesc == nil {
		return nil
	}
	// 从注册表获取结构体元数据
	meta, exists := structRegistry.Find(typeDesc.FullKey)
	if !exists {
		log.Printf("未注册的结构体类型: %s", typeDesc.FullKey)
		return nil
	}

	var params []ParamSpec
	visited := make(map[string]bool) // 循环引用保护

	var parse func(*StructMeta, string)
	parse = func(sm *StructMeta, prefix string) {
		if visited[sm.PkgPath+"."+sm.TypeName] {
			return
		}
		visited[sm.PkgPath+"."+sm.TypeName] = true

		for _, f := range sm.AstStruct.Fields.List {
			// 获取字段基础信息
			jsonName, skip := parseJSONField(f)
			if skip || jsonName == "" {
				continue
			}

			// 递归处理嵌套结构体
			if isNestedStruct(f.Type, m.ImportAlias) {
				nestedType := m.parseTypeExpr(f.Type)
				if nestedMeta, ok := structRegistry.Find(nestedType.FullKey); ok {
					parse(nestedMeta, prefix+jsonName+".")
				}
				continue
			}

			// 构建参数规范
			param := ParamSpec{
				Name:        prefix + jsonName,
				Types:       []*TypeDescriptor{m.parseTypeExpr(f.Type)},
				Required:    !hasOmitEmpty(f.Tag),
				Description: extractFieldDesc(f),
				In:          "body",
			}

			// 应用方法级注释覆盖
			if comment := m.findParamComment(param.Name); comment != nil {
				m.applyCommentOverride(&param, comment)
			}

			params = append(params, param)
		}
	}

	parse(meta, "")
	return params
}

// 获取字段类型的真实包路径
func (m *Method) getFieldPkgPath(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.SelectorExpr:
		return m.resolveSelectorPkgPath(t)
	case *ast.Ident:
		return m.CurrentPkgPath // 本地类型保持当前路径
	default:
		return "unknown"
	}
}

// 解析选择器表达式的包路径
func (m *Method) resolveSelectorPkgPath(expr *ast.SelectorExpr) string {
	if ident, ok := expr.X.(*ast.Ident); ok {
		if realPath, ok := m.ImportAlias[ident.Name]; ok {
			return formatComponentKey(realPath)
		}
	}
	return m.CurrentPkgPath
}

// 从方法注释查找参数覆盖信息
func (m *Method) findParamComment(paramName string) *ParamSpec {
	// 使用缓存避免重复解析
	if m.commentCache == nil {
		m.commentCache = make(map[string]*ParamSpec)
		// 分割文档注释为多行
		lines := strings.Split(m.Doc, "\n")

		// 解析方法注释中的@param标签
		re := regexp.MustCompile(`@param:(\w+)\s+(\S+)\s+(\S+)\s+"([^"]+)"`)
		for _, line := range lines {
			if !strings.HasPrefix(line, "//") {
				continue
			}
			// 去除注释符号
			line = strings.TrimSpace(strings.TrimPrefix(line, "//"))

			matches := re.FindStringSubmatch(line)
			if len(matches) < 5 {
				continue
			}

			m.commentCache[matches[2]] = &ParamSpec{
				Name:        matches[2],
				In:          matches[1],
				Types:       []*TypeDescriptor{{FullKey: matches[3]}},
				Description: matches[4],
				Required:    !strings.Contains(matches[0], "omitempty"),
			}
		}
	}
	return m.commentCache[paramName]
}

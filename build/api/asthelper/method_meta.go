package asthelper

import (
	"github.com/spf13/cast"
	"reflect"
	"strings"
)

// MethodMeta method的元数据结构
type MethodMeta struct {
	AuthMethod  string
	Permissions []string          // 所需权限
	Validations map[string]string // 字段校验规则
	Path        string
	Sensitive   []string // 敏感字段（需过滤）

	Method      reflect.Method
	MethodValue reflect.Value
	InputType   reflect.Type
	OutputType  reflect.Type
	// 固有信息
	ServiceName string
	MethodName  string
}

func ParseMethodComment(comments []string) *MethodMeta {
	meta := &MethodMeta{
		Validations: make(map[string]string),
		AuthMethod:  "jwt", // default
	}

	for _, c := range comments {
		text := strings.TrimSpace(c)
		switch {
		case strings.HasPrefix(text, "@jwt"):
			meta.AuthMethod = "jwt"
		case strings.HasPrefix(text, "@permission"):
			meta.Permissions = strings.Split(strings.TrimSpace(strings.TrimPrefix(text, "@permission:")), ",")
		case strings.HasPrefix(text, "@auth"):
			tag := strings.TrimSpace(strings.TrimPrefix(text, "@auth:"))
			if !cast.ToBool(tag) && tag != "required" {
				meta.AuthMethod = ""
			}
		case strings.HasPrefix(text, "@validate"):
			parts := strings.SplitN(text, " ", 3)
			if len(parts) == 3 {
				meta.Validations[parts[1]] = parts[2]
			}
		case strings.HasPrefix(text, "@path"):
			meta.Path = strings.TrimSpace(strings.TrimPrefix(text, "@path:"))
		}
	}

	return meta
}

package asthelper

import (
	"fmt"
	"net/http"
	"sort"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/hashicorp/go-set/v3"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

var (
	caser = cases.Title(language.English)
)

func lowerFirst(s string) string {
	if s == "" {
		return ""
	}
	return strings.ToLower(s[0:1]) + s[1:]
}

func GetMethodAPIPath(serviceName, methodName, docPath string) (out string) {
	defer func() {
		out = strings.TrimPrefix(out, "/")
		srvN := strings.Split(serviceName, "/")
		for _, srv := range srvN {
			if strings.HasPrefix(out, srv) { // user, user/tenant
				out = strings.TrimPrefix(out, srv)
				out = strings.TrimPrefix(out, "/")
			}
		}
		out = lowerFirst(out)
	}()
	if v := docPath; v != "" {
		return v
	}
	return methodName
}

// BuildPathItem openapi
func BuildPathItem(doc *openapi3.T, service ServiceInterface, method *Method) {
	method.ServiceName = service.ServiceName
	method.APIPath = GetMethodAPIPath(service.ServiceName, method.Name, ExtractTag(method.Doc, "path"))
	path := fmt.Sprintf("/api/%s/%s",
		service.ServiceName,
		method.APIPath,
	)

	// 获取或创建PathItem对象
	pathItem := doc.Paths.Value(path)
	if pathItem == nil {
		pathItem = &openapi3.PathItem{}
	}

	// 解析HTTP方法（关键新增逻辑）
	httpMethods := parseHTTPMethod(method.Doc)
	operation := buildOperation(method, doc)

	for _, httpMethod := range httpMethods {
		switch strings.ToUpper(httpMethod) {
		case http.MethodPost:
			pathItem.Post = operation
		case http.MethodDelete:
			pathItem.Delete = operation
		case http.MethodGet:
			pathItem.Get = operation
		case http.MethodPut:
			pathItem.Put = operation
		}
	}
	doc.Paths.Set(path, pathItem)
}

func buildOperation(method *Method, doc *openapi3.T) *openapi3.Operation {
	tagsSet := set.New[string](3)
	tagsSet.Insert(strings.Title(method.ServiceName))

	docTags := extractTags(method.Doc)
	for _, t := range docTags {
		tagsSet.Insert(strings.Title(t))
	}
	tags := tagsSet.Slice()
	sort.Strings(tags)
	firstLineDoc := strings.Split(method.Doc, "\n")
	if firstLineDoc[0] == "" {
		firstLineDoc = []string{method.Name}
	}
	op := &openapi3.Operation{
		OperationID:  method.ServiceName + "/" + method.APIPath,
		Summary:      strings.TrimSpace(firstLineDoc[0]),
		Description:  ExtractTag(method.Doc, "desc"),
		Tags:         tags,
		Parameters:   buildParameters(method, doc),
		RequestBody:  buildRequestBody(method, doc),
		Responses:    buildResponses(method, doc),
		ExternalDocs: buildExternalDocs(method, doc),
	}
	return op
}

func buildExternalDocs(method *Method, doc *openapi3.T) *openapi3.ExternalDocs {
	// 解析@method: POST
	if m := ExtractTag(method.Doc, "doc"); m != "" {
		vs := strings.SplitN(m, ":", 2)
		return &openapi3.ExternalDocs{
			Description: strings.TrimSpace(vs[0]),
			URL:         strings.TrimSpace(vs[1]),
		}
	}
	return nil
}

func parseHTTPMethod(doc string) []string {
	// 解析@method: POST
	if m := ExtractTag(doc, "method"); m != "" {
		return strings.Split(m, ",")
	}
	return []string{"POST"}
}

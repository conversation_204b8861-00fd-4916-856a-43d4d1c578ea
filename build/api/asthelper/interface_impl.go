package asthelper

import (
	"go/ast"
	"go/token"
	"go/types"

	"golang.org/x/tools/go/packages"
)

// findImplementations 查找实现了指定接口的结构体

func findImplementations(pkg *packages.Package, iface *ast.InterfaceType) []string {
	var implementations []string

	// 1. 获取接口方法列表
	var ifaceMethods []string
	for _, method := range iface.Methods.List {
		if len(method.Names) > 0 {
			ifaceMethods = append(ifaceMethods, method.Names[0].Name)
		}
	}
	//log.Info("接口方法列表: %v", ifaceMethods)

	// 2. 遍历包中的所有类型
	for _, file := range pkg.Syntax {
		//log.Info("正在分析文件: %s", file.Name.Name)

		for _, decl := range file.Decls {
			genDecl, ok := decl.(*ast.GenDecl)
			if !ok || genDecl.Tok != token.TYPE {
				continue
			}

			for _, spec := range genDecl.Specs {
				typeSpec, ok := spec.(*ast.TypeSpec)
				if !ok {
					continue
				}

				// 3. 只检查结构体类型
				if _, ok := typeSpec.Type.(*ast.StructType); !ok {
					//log.Info("跳过非结构体类型: %s", typeSpec.Name.Name)
					continue
				}

				// 4. 获取类型对象
				obj := pkg.TypesInfo.Defs[typeSpec.Name]
				if obj == nil {
					//log.Warn("无法获取类型对象: %s", typeSpec.Name.Name)
					continue
				}

				// 5. 检查是否实现了所有接口方法
				methodSet := types.NewMethodSet(obj.Type())
				if obj, ok := obj.(*types.TypeName); ok {
					// 同时检查指针类型的方法集
					methodSet = types.NewMethodSet(types.NewPointer(obj.Type()))
				}
				implementsAll := true
				for _, methodName := range ifaceMethods {
					if methodSet.Lookup(pkg.Types, methodName) == nil {
						//log.Info("类型 %s 未实现接口方法 %s", typeSpec.Name.Name, methodName)
						implementsAll = false
						break
					}
				}

				if implementsAll {
					//log.Info("找到实现类型: %s", typeSpec.Name.Name)
					implementations = append(implementations, typeSpec.Name.Name)
				}
			}
		}
	}

	//log.Info("最终找到的实现类型: %v", implementations)
	return implementations
}

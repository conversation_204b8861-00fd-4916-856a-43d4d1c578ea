package asthelper

import (
	"fmt"
	"go/ast"
	"go/token"
	"log"
	"reflect"
	"runtime"
	"slices"
	"strconv"
	"strings"
	"unicode"

	"golang.org/x/tools/go/packages"
)

// 解析内部包路径
func resolveInternalPath(mainModule, importPath string) string {
	// 处理相对路径导入（假设项目使用go modules）
	if strings.HasPrefix(importPath, "./") {
		return mainModule + "/" + strings.TrimPrefix(importPath, "./")
	}
	return importPath
}

func (p *TypeParser) loadExternalPackage(importPath string) *packages.Package {
	// 检查缓存
	if cached, ok := pkgCache.Load(importPath); ok {
		return cached.(*packages.Package)
	}

	if !isInAutoLoadList(importPath) {
		return nil
	}

	cfg := &packages.Config{
		Mode: packages.NeedName |
			packages.NeedTypes |
			packages.NeedSyntax |
			packages.NeedImports |
			packages.NeedDeps |
			packages.NeedTypesInfo | // 关键：获取类型信息
			packages.NeedModule, // 需要模块信息
	}
	// 特殊处理标准库
	//if isStdLibPackage(importPath){
	//	return nil
	//}

	pkgs, err := packages.Load(cfg, importPath)
	if err != nil || len(pkgs) == 0 {
		return nil
	}
	targetPkg := pkgs[0]

	// 二次验证标准库（处理类似 "hotel/common" 的情况）
	if isActuallyStdLib(targetPkg) {
		return nil
	}

	pkgCache.Store(importPath, targetPkg)
	return targetPkg
}

// 二次验证（处理类似 "context" 等实际标准库）
func isActuallyStdLib(pkg *packages.Package) bool {
	if pkg.Module != nil {
		return false
	}

	// 检查文件路径是否在GOROOT中
	if len(pkg.GoFiles) > 0 {
		for _, f := range pkg.GoFiles {
			if !strings.HasPrefix(f, runtime.GOROOT()) {
				return false
			}
		}
		return true
	}
	return false
}

func (m *Method) parseParams(params *ast.FieldList) {
	m.Params = nil // 重置参数列表
	if params == nil || len(params.List) == 0 {
		return
	}

	// 跳过context参数
	startIndex := 0
	if isContextType(params.List[0].Type) {
		startIndex = 1
	}

	// 遍历所有参数（包括请求体）
	for i, field := range params.List[startIndex:] {
		param, ok := m.parseParam(i, field)
		if !ok {
			continue
		}
		m.Params = append(m.Params, param)
	}
	m.Params = append(m.Params, ParamSpec{
		Name:        "Common Headers",
		In:          "header",
		Required:    true,
		Description: "common header",
		Types: []*TypeDescriptor{
			{
				FullKey:    HeaderFullKey, //todo: 自定义扩展
				IsPointer:  false,
				Dimensions: 0,
			},
		},
	})
}

func isStructType(expr ast.Expr, alias map[string]string) bool {
	switch t := expr.(type) {
	case *ast.StructType: // 直接结构体字面量
		return true

	case *ast.StarExpr: // 指针结构体
		return isStructType(t.X, alias)

	case *ast.SelectorExpr: // 其他包的结构体（pkg.User）
		exprType := parseSelectorExpr(t, alias)
		return exprType == "struct"

	case *ast.Ident: // 当前包的结构体
		if obj := t.Obj; obj != nil {
			typeSpec, ok := obj.Decl.(*ast.TypeSpec)
			if ok {
				_, isStruct := typeSpec.Type.(*ast.StructType)
				return isStruct
			}
		}
		return false

	default:
		return false
	}
}

// 辅助函数：生成默认参数名（用于匿名参数）
func generateParamName(index int) string {
	return fmt.Sprintf("param%d", index+1)
}

// 增强类型解析支持以下格式：
// "[]*int|string" → 解析为两个TypeDescriptor
func (m *Method) parseStringType(s string) []*TypeDescriptor {
	types := strings.Split(s, "|")
	descriptors := make([]*TypeDescriptor, 0, len(types))

	for _, t := range types {
		desc := m.parseSingleType(strings.TrimSpace(t))
		if desc != nil {
			descriptors = append(descriptors, desc)
		}
	}

	return descriptors
}

func (m *Method) parseResults(results *ast.FieldList) {
	if results == nil || len(results.List) == 0 {
		return
	}

	// 清空默认值
	m.ResponseType = nil
	m.Responses = nil

	// 从注释提取自定义状态码
	for _, e := range extractResponseCode(m.Doc) {
		resp := ResponseSpec{
			Code: e[0],
			DataType: &TypeDescriptor{
				FullKey:     "hotel.common.bizerr." + e[1], // use code
				Description: e[0],
			},
			Description: e[2],
		}

		m.Responses = append(m.Responses, resp)
	}

	l := []string{"error", "context.Context"}
	// 遍历所有返回参数
	for _, field := range results.List {
		resultType := m.parseTypeExpr(field.Type)

		// 错误处理
		if slices.Contains(l, resultType.FullKey) {
			continue
		}

		m.ResponseType = resultType
		// 成功响应处理
		resp := ResponseSpec{
			Code:        "200", // 默认成功状态码
			DataType:    resultType,
			Description: "Success",
		}
		m.Responses = append(m.Responses, resp)
	}
}

// 增强选择器表达式解析
func parseSelectorExpr(expr *ast.SelectorExpr, aliases map[string]string) string {
	var parts []string

	// 递归解析父级表达式
	if x, ok := expr.X.(*ast.SelectorExpr); ok {
		parent := parseSelectorExpr(x, aliases)
		parts = strings.Split(parent, ".")
	} else if ident, ok := expr.X.(*ast.Ident); ok {
		// 应用别名转换和路径格式化
		if realPath, exists := aliases[ident.Name]; exists {
			parts = strings.Split(formatComponentKey(realPath), ".")
		} else {
			parts = strings.Split(formatComponentKey(ident.Name), ".")
		}
	}

	// 拼接最终组件名
	parts = append(parts, expr.Sel.Name)
	return strings.Join(parts, ".")
}

// 增强类型解析逻辑
func (m *Method) parseTypeExpr(expr ast.Expr) *TypeDescriptor {
	return parseTypeExpr(expr, m.CurrentPkgPath, m.ImportAlias)
}

// 解析文件导入语句，返回别名到完整路径的映射
func parseFileImports(file *ast.File) map[string]string {
	aliases := make(map[string]string)
	for _, imp := range file.Imports {
		// 获取完整导入路径（去除引号）
		fullPath := strings.Trim(imp.Path.Value, `"`)

		// 处理带别名的导入（如 import ud "user.domain"）
		var alias string
		if imp.Name != nil {
			alias = imp.Name.Name
		} else {
			// 默认别名取完整路径的最后一段
			// 例如：user.domain → domain
			parts := strings.Split(fullPath, "/")
			alias = parts[len(parts)-1]
		}

		// 存储完整路径（user.domain）
		aliases[alias] = fullPath
	}
	return aliases
}

// 从注释提取响应码（如 @response:201,UserCreated,LongDescription）
func extractResponseCode(doc string) (out [][3]string) {
	lines := strings.Split(doc, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "@response:") {
			parts := strings.SplitN(line[len("@response:"):], ",", 3)
			if len(parts) == 1 {
				parts = strings.Split(strings.TrimSpace(parts[0]), " ")
			}
			if len(parts) < 2 {
				parts = append(parts, "")
			}
			if len(parts) < 3 {
				parts = append(parts, "")
			}
			out = append(out, [3]string{strings.TrimSpace(parts[0]), strings.TrimSpace(parts[1]), strings.TrimSpace(parts[2])})
		}
	}
	return out
}

// 辅助函数
func isContextType(expr ast.Expr) bool {
	if sel, ok := expr.(*ast.SelectorExpr); ok {
		if x, ok := sel.X.(*ast.Ident); ok && x.Name == "context" && sel.Sel.Name == "Context" {
			return true
		}
	}
	return false
}

// FindServiceImplementations 增强服务检测逻辑
func FindServiceImplementations(pkg *packages.Package) []ServiceInterface {
	var services []ServiceInterface
	structMethods := make(map[string][]*Method) // key: 结构体名称
	// 第一阶段：收集所有结构体方法
	for _, file := range pkg.Syntax {
		pkgAliases := parseFileImports(file)

		ast.Inspect(file, func(n ast.Node) bool {
			// 只处理方法声明
			fn, ok := n.(*ast.FuncDecl)
			if !ok || fn.Recv == nil {
				return true
			}

			// 解析接收器类型（仅处理目标包）
			structName := extractStructName(fn.Recv)
			if structName == "" {
				return true
			}

			// 解析方法元数据
			method := parseMethod(fn, pkgAliases, pkg)
			structMethods[structName] = append(structMethods[structName], method)
			return true
		})
	}

	// 第二阶段：检测符合要求的服务结构体
	for _, file := range pkg.Syntax {
		ast.Inspect(file, func(n ast.Node) bool {
			typeSpec, ok := n.(*ast.TypeSpec)
			if !ok {
				return true
			}

			// 只处理结构体类型
			if _, ok := typeSpec.Type.(*ast.StructType); !ok {
				return true
			}

			structName := typeSpec.Name.Name
			methods := structMethods[structName]

			// 关键检测逻辑：必须包含Name方法且返回string
			serviceName := extractServiceName(methods)
			if serviceName == "" {
				return true
			}

			services = append(services, ServiceInterface{
				ServiceName: serviceName,
				Methods:     filterValidMethods(methods),
			})
			return true
		})
	}

	return services
}

// 过滤有效方法（排除私有方法等）
func filterValidMethods(methods []*Method) []*Method {
	var valid []*Method
	for _, m := range methods {
		// 方法名首字母大写
		if len(m.Name) == 0 || !unicode.IsUpper(rune(m.Name[0])) {
			continue
		}
		if m.Name == "Name" {
			continue
		}
		if v := ExtractTag(m.Doc, "apidoc"); v == "-" {
			continue
		}
		valid = append(valid, m)
	}
	return valid
}

// 提取结构体名称（处理指针和包名）
func extractStructName(recv *ast.FieldList) string {
	if recv == nil || len(recv.List) == 0 {
		return ""
	}

	expr := recv.List[0].Type
	// 处理指针类型
	if star, ok := expr.(*ast.StarExpr); ok {
		expr = star.X
	}
	// 处理选择器表达式（如service.User）
	if sel, ok := expr.(*ast.SelectorExpr); ok {
		return sel.Sel.Name
	}
	// 处理普通标识符（当前包）
	if ident, ok := expr.(*ast.Ident); ok {
		return ident.Name
	}
	return ""
}

// 解析Name()方法返回的实际字符串
func extractServiceName(methods []*Method) string {
	for _, m := range methods {
		if m.Name == "Name" && m.ResponseType != nil && m.ResponseType.FullKey == "string" {
			// 从AST解析方法体中的返回字面量
			return parseNameMethodBody(m.Source)
		}
	}
	return "" // 未找到有效实现
}

// 安全解析Name方法的返回值
func parseNameMethodBody(fn *ast.FuncDecl) string {
	// 1. 校验方法签名
	if fn.Type.Results == nil || len(fn.Type.Results.List) != 1 {
		return ""
	}
	if ident, ok := fn.Type.Results.List[0].Type.(*ast.Ident); !ok || ident.Name != "string" {
		return ""
	}

	// 2. 遍历方法体查找return语句
	var returnValue string
	ast.Inspect(fn.Body, func(n ast.Node) bool {
		retStmt, ok := n.(*ast.ReturnStmt)
		if !ok || len(retStmt.Results) != 1 {
			return true
		}

		// 3. 处理字符串字面量
		if lit, ok := retStmt.Results[0].(*ast.BasicLit); ok && lit.Kind == token.STRING {
			if unquoted, err := strconv.Unquote(lit.Value); err == nil {
				returnValue = unquoted
			}
			return false // 找到有效返回后停止遍历
		}

		// 4. 处理常量表达式（如const定义的字符串）
		if ident, ok := retStmt.Results[0].(*ast.Ident); ok {
			if obj := ident.Obj; obj != nil && obj.Kind == ast.Con {
				if spec, ok := obj.Decl.(*ast.ValueSpec); ok && len(spec.Values) > 0 {
					if lit, ok := spec.Values[0].(*ast.BasicLit); ok && lit.Kind == token.STRING {
						if unquoted, err := strconv.Unquote(lit.Value); err == nil {
							returnValue = unquoted
						}
					}
				}
			}
		}
		return true
	})

	return returnValue
}

// parseMethod for dispatch, not for doc
func parseMethod(fn *ast.FuncDecl, pkgAlias map[string]string, pkg *packages.Package) *Method {
	comments := extractCommentGroupText(fn.Doc)
	method := &Method{
		Name:           fn.Name.Name,
		Doc:            fn.Doc.Text(),
		Source:         fn,
		ImportAlias:    pkgAlias,
		CurrentPkgPath: pkg.PkgPath,
		Meta:           ParseMethodComment(strings.Split(comments, "\n")),
	}

	// 解析参数
	method.parseParams(fn.Type.Params)
	// 解析返回类型
	method.parseResults(fn.Type.Results)

	return method
}

// 新增tags解析
func extractTags(doc string) []string {
	lines := strings.Split(doc, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "@tags:") {
			tagsPart := strings.TrimSpace(strings.TrimPrefix(line, "@tags:"))
			if tagsPart == "" {
				return nil
			}
			tags := strings.Split(tagsPart, ",")
			for i := range tags {
				tags[i] = strings.TrimSpace(tags[i])
			}
			return tags
		}
	}
	return nil
}
func getJSONTag(field *ast.Field) string {
	if field.Tag == nil {
		return ""
	}

	tag := strings.Trim(field.Tag.Value, "`")
	for _, part := range strings.Split(tag, " ") {
		if strings.HasPrefix(part, "json:") {
			value := strings.Trim(strings.TrimPrefix(part, "json:"), `"`)
			return strings.Split(value, ",")[0]
		}
	}
	return ""
}

func getMultiTagValue(field *ast.Field, tagName string) []string {
	if field.Tag == nil {
		return nil
	}

	tag := strings.Trim(field.Tag.Value, "`")
	for _, part := range strings.Split(tag, " ") {
		if strings.HasPrefix(part, tagName+":") {
			value := strings.Trim(strings.TrimPrefix(part, tagName+":"), `"`)
			return strings.Split(value, ",")
		}
	}
	return nil
}

func getTagValue(field *ast.Field, tagName string) string {
	v := getMultiTagValue(field, tagName)
	if len(v) == 0 {
		return ""
	}
	return v[0]
}

// 判断AST类型表达式是否为嵌套结构体
func isNestedStruct(expr ast.Expr, alias map[string]string) bool {
	switch t := expr.(type) {
	case *ast.StructType: // 内联结构体
		return true
	case *ast.StarExpr: // 结构体指针
		return isNestedStruct(t.X, alias)
	case *ast.SelectorExpr: // 外部包结构体
		return isStructType(t, alias)
	case *ast.Ident: // 当前包结构体
		return isStructType(t, alias)
	default:
		return false
	}
}

// 应用注释覆盖规则
func (m *Method) applyCommentOverride(target *ParamSpec, comment *ParamSpec) {
	// 1. 覆盖参数位置（需校验合法性）
	if isValidParamIn(comment.In) {
		target.In = comment.In
	} else {
		log.Printf("非法参数位置: %s 参数: %s", comment.In, target.Name)
	}

	// 2. 覆盖类型信息（需解析类型字符串）
	if len(comment.Types) > 0 {
		if convertedTypes := m.parseCommentTypes(comment.Types); len(convertedTypes) > 0 {
			target.Types = convertedTypes
		} else {
			log.Printf("类型解析失败: %s 原始类型: %s",
				target.Name, comment.Types[0].FullKey)
		}
	}

	// 3. 覆盖描述信息（注释描述优先）
	if comment.Description != "" {
		if strings.Contains(comment.Description, "按钮注释") {
			panic("xxxxx")
		}
		target.Description = comment.Description
	}

	// 4. 覆盖必填逻辑（显式声明优先）
	if comment.Required != target.Required {
		target.Required = comment.Required
	}
}

// 校验参数位置合法性
func isValidParamIn(in string) bool {
	allowed := map[string]bool{
		"path":   true,
		"query":  true,
		"header": true,
		"cookie": true,
		"body":   true,
	}
	return allowed[in]
}

// 解析注释中的类型字符串
func (m *Method) parseCommentTypes(types []*TypeDescriptor) []*TypeDescriptor {
	var result []*TypeDescriptor
	for _, t := range types {
		if desc := m.parseSingleType(t.FullKey); desc != nil {
			result = append(result, desc)
		}
	}
	return result
}

// / getJSONName 解析字段的JSON名称
func getAPIFieldName(field *ast.Field, apidocRestriction string) (string, bool) {
	if v := getTagValue(field, "apidoc"); v != "" {
		if v == "-" {
			return "", true
		}
		if !strings.Contains(apidocRestriction, v) {
			//log.Printf("getAPIFieldName skip by %v with apidocRestriction %s", v, apidocRestriction)
			return "", true
		}
	}
	return getTagValueByOrder(field, "api.header", "json")
}

func getTagValueByOrder(field *ast.Field, tagNames ...string) (string, bool) {
	if field.Tag == nil {
		if len(field.Names) > 0 {
			return field.Names[0].Name, false
		}
		return "", false
	}

	tag := reflect.StructTag(strings.Trim(field.Tag.Value, "`"))
	for _, tagName := range tagNames {
		tagVal := tag.Get(tagName)
		if tagVal == "-" {
			return "", true
		}

		if commaIndex := strings.Index(tagVal, ","); commaIndex != -1 {
			tagVal = tagVal[:commaIndex]
		}

		if tagVal != "" {
			return tagVal, false
		}
	}

	if len(field.Names) > 0 {
		return field.Names[0].Name, false
	}
	return "", false
}

// isRequiredField 判断是否必填字段
func isRequiredField(field *ast.Field) bool {
	return getValueFromTag(field, "validate") == "required" || getValueFromTag(field, "required") == "true"
}

// 带自动加载的选择器解析
func parseSelectorWithAutoLoad(expr *ast.SelectorExpr, importAlias map[string]string) string {
	// 初步解析选择器
	baseKey := parseSelectorExpr(expr, importAlias)

	// 尝试加载未注册的第三方包
	if _, ok := structRegistry.Find(baseKey); !ok {
		if pkgPath := extractRootPackage(baseKey); isThirdPartyComponent(pkgPath) {
			if pkg := tryLoadThirdParty(pkgPath); pkg != nil {
				// 重新解析选择器（可能包含新注册的类型）
				return parseSelectorExpr(expr, importAlias)
			}
		}
	}
	return baseKey
}

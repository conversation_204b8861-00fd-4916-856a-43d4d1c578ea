package asthelper

import (
	"container/list"
	"fmt"
	"github.com/getkin/kin-openapi/openapi3"
	"go/ast"
	"go/token"
	"golang.org/x/tools/go/packages"
	"log"
	"reflect"
	"strings"
)

func ProcessModels(pkg *packages.Package, doc *openapi3.T) *list.List {
	parser := NewTypeParser(pkg, doc)
	parser.ParseAllDecls()
	return parser.defers
}

// 判断是否为自有模块
func isInternalPackage(pkgPath string) bool {
	return strings.HasPrefix(pkgPath, ModuleName) ||
		strings.HasPrefix(pkgPath, "vendor/"+ModuleName)
}

// 安全替换路径分隔符
func replaceSlashes(s string) string {
	return strings.ReplaceAll(s, "/", ".")
}

// 深度克隆SchemaRef
func cloneSchemaRef(ref *openapi3.SchemaRef) *openapi3.SchemaRef {
	if ref == nil {
		return nil
	}

	cloned := &openapi3.SchemaRef{
		Ref:   ref.Ref,
		Value: cloneSchema(ref.Value),
	}
	return cloned
}

// 克隆Schema对象
func cloneSchema(schema *openapi3.Schema) *openapi3.Schema {
	if schema == nil {
		return new(openapi3.Schema)
	}

	cloned := &openapi3.Schema{
		Type:        schema.Type,
		Description: schema.Description,
		Properties:  make(openapi3.Schemas, len(schema.Properties)),
		Required:    append([]string{}, schema.Required...),
	}

	for k, v := range schema.Properties {
		cloned.Properties[k] = cloneSchemaRef(v)
	}
	return cloned
}

func parseTypeExpr(expr ast.Expr, currentPkgPath string, importAlias map[string]string) *TypeDescriptor {
	// 防御性检查
	if expr == nil {
		return &TypeDescriptor{FullKey: "invalid"}
	}

	// 创建基础描述符
	var desc *TypeDescriptor
	switch t := expr.(type) {
	case *ast.StarExpr:
		desc = parseTypeExpr(t.X, currentPkgPath, importAlias)
		desc.IsPointer = true

	case *ast.ArrayType:
		desc = parseTypeExpr(t.Elt, currentPkgPath, importAlias)
		desc.Dimensions++

	case *ast.SelectorExpr:
		fullKey := formatComponentKey(parseSelectorWithAutoLoad(t, importAlias))
		desc = &TypeDescriptor{FullKey: fullKey}

	case *ast.Ident:
		fullKey := fullComponentName(currentPkgPath, t.Name)
		desc = &TypeDescriptor{FullKey: fullKey}

	default:
		desc = &TypeDescriptor{FullKey: "unknown"}
	}

	// 动态处理未注册的第三方类型
	if !isBasicType(desc.FullKey) && !structRegistry.Exists(desc.FullKey) {
		if isThirdPartyComponent(desc.FullKey) {
			if loadedPkg := tryLoadThirdParty(desc.FullKey); loadedPkg != nil {
				// 重建类型描述符（关键修正点）
				return &TypeDescriptor{
					FullKey:    desc.FullKey,
					IsPointer:  desc.IsPointer,  // 保留原始指针状态
					Dimensions: desc.Dimensions, // 保留原始数组维度
				}
			}
		}
	}

	return desc
}

// 从结构体字段提取描述信息
func extractFieldDescription(field *ast.Field) string {
	// 优先级1: 从字段标签的desc属性获取
	if desc := getTagValue(field, "desc"); desc != "" {
		return desc
	}

	// 优先级2: 从字段注释获取（格式：// 用户ID）
	if field.Doc != nil && len(field.Doc.List) > 0 {
		return strings.TrimSpace(field.Doc.List[0].Text[2:])
	}

	return ""
}

// 解析JSON字段名及跳过标记
func parseJSONField(field *ast.Field) (name string, skip bool) {
	// 默认使用字段名
	if len(field.Names) > 0 {
		name = field.Names[0].Name
	}

	// 解析标签（处理 `json:"name,omitempty"`）
	if field.Tag != nil {
		tag := reflect.StructTag(strings.Trim(field.Tag.Value, "`"))
		jsonTag := tag.Get("json")
		if jsonTag == "-" {
			return "", true
		}
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			name = parts[0]
		}
	}

	// 最终校验（防止空名）
	if name == "" {
		log.Printf("警告：结构体字段缺失名称 %+v", field.Pos())
		return "", true
	}
	return name, false
}

// 从导入信息获取真实包路径
func getPkgPathFromImports(pkg *packages.Package, alias string) string {
	for _, file := range pkg.Syntax {
		for _, imp := range file.Imports {
			importPath := strings.Trim(imp.Path.Value, `"`)
			if imp.Name != nil && imp.Name.Name == alias {
				return importPath
			}
			if imp.Name == nil {
				lastSlash := strings.LastIndex(importPath, "/")
				if lastSlash != -1 && importPath[lastSlash+1:] == alias {
					return importPath
				}
			}
		}
	}
	return ""
}

// 枚举类型检测
func isEnumType(pkg *packages.Package, typeSpec *ast.TypeSpec) bool {
	for _, file := range pkg.Syntax {
		for _, decl := range file.Decls {
			genDecl, ok := decl.(*ast.GenDecl)
			if !ok || genDecl.Tok != token.CONST {
				continue
			}

			for _, spec := range genDecl.Specs {
				valueSpec, ok := spec.(*ast.ValueSpec)
				if ok && valueSpec.Type != nil {
					if ident, ok := valueSpec.Type.(*ast.Ident); ok && ident.Name == typeSpec.Name.Name {
						return true
					}
				}
			}
		}
	}
	return false
}

// 生成枚举Schema
func generateEnumSchema(pkg *packages.Package, typeSpec *ast.TypeSpec, underlyingType string) *openapi3.SchemaRef {
	//  typeSpec.Name.Name: EntityType, underlyingType: int
	enumValues := collectEnumEntries(pkg, typeSpec.Name.Name)
	//return openapi3.NewSchemaRef("", &openapi3.Schema{
	//	Type: &openapi3.Types{underlyingType},
	//	Enum: enumValues,
	//})
	return generateEnumSchemaFromEntry(enumValues, underlyingType).NewRef()
}

type EnumEntry struct {
	Name    string // 变量名（如 StatusOK）
	Value   string // 值（如 "OK"）
	Doc     string // 文档注释（声明前的注释）
	Comment string // 注释（如 "状态正常"）
}

func generateEnumSchemaFromEntry(entries []EnumEntry, underlyingType string) *openapi3.Schema {
	// 提取所有枚举值
	enumValues := make([]any, len(entries))
	for i, entry := range entries {
		enumValues[i] = entry.Value
	}

	// 构建 Markdown 描述
	var descBuilder strings.Builder
	descBuilder.WriteString("Enums \n")
	for _, entry := range entries {
		descBuilder.WriteString(fmt.Sprintf(
			"- `%s` (`%s`): %s\n",
			entry.Name,
			entry.Value,
			entry.Comment,
		))
	}

	// 创建 Schema 并填充字段
	ori := getBasicTypeSchema(underlyingType)
	if ori == nil {
		log.Printf("generateEnumSchemaFromEntry <underlyingType> %s", underlyingType)
		ori = BasicTypeSchemas["object"]
	}
	schema := *ori
	schema.Enum = enumValues
	schema.Description = descBuilder.String()
	// 添加自定义扩展字段（可选）
	var varNames []any
	var comments []any
	for _, entry := range entries {
		varNames = append(varNames, entry.Name)
		comments = append(comments, entry.Comment)
	}
	schema.Extensions = map[string]any{
		"x-enum-varnames": varNames,
		"x-enum-comments": comments,
	}

	return &schema
}

// 收集枚举变量名、值和注释
func collectEnumEntries(pkg *packages.Package, typeName string) []EnumEntry {
	var entries []EnumEntry
	for _, file := range pkg.Syntax {
		for _, decl := range file.Decls {
			genDecl, ok := decl.(*ast.GenDecl)
			if !ok || genDecl.Tok != token.CONST {
				continue
			}

			for _, spec := range genDecl.Specs {
				valueSpec, ok := spec.(*ast.ValueSpec)
				if !ok || valueSpec.Type == nil {
					continue
				}

				// 检查类型是否匹配目标枚举类型
				ident, ok := valueSpec.Type.(*ast.Ident)
				if !ok || ident.Name != typeName {
					continue
				}

				// 提取文档注释（声明前的注释）
				docComments := extractComments(valueSpec.Doc)

				// 处理每个常量的名称和值
				for i, name := range valueSpec.Names {
					if i >= len(valueSpec.Values) {
						continue // 防止索引越界
					}

					// 提取值
					value := ""
					if basicLit, ok := valueSpec.Values[i].(*ast.BasicLit); ok {
						value = strings.Trim(basicLit.Value, `"`)
					}

					// 提取行前+行末注释（仅关联到当前 ValueSpec）
					lineComment := strings.TrimPrefix(extractDescription(valueSpec.Doc, valueSpec.Comment), name.Name+" ")

					entries = append(entries, EnumEntry{
						Name:    name.Name,
						Value:   value,
						Doc:     docComments,
						Comment: lineComment,
					})
				}
			}
		}
	}
	return entries
}

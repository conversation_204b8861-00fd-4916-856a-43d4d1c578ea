package asthelper

import (
	"sync"
)

var (
	thirdPartyCache sync.Map // map[string]*packages.Package
	typeDescCache   sync.Map // 类型缓存，防止循环引用, map[string]*TypeDescriptor
	structRegistry  = &StructRegistry{
		data: map[string]*StructMeta{},
	}
	GlobalGenericTitles = []string{
		"hotel.common.bff.ElementContent",
		"hotel.user.protocol.ActionParams",
	}
	TargetModelPkgIDs = []string{
		"common/bff",
		"common/i18n",
		"common/money",
		"common/types",
		"common/httphelper",
		"common/pagehelper",
		"*/domain",
		"*/protocol",
	}
	PublicTargetModelPkgIDs = []string{
		"common/i18n",
		"common/money",
		"common/types",
		"common/httphelper",
		"common/pagehelper",
		"*/domain",
		"*/protocol",
	}
)
var (
	DocTitle_Internal = "HotelCode API"
	DocTitle_Public   = "HotelByte API"
)
var (
	ModuleName = "hotel"
)
var (
	AutoLoadList = []string{
		"github.com/zeromicro/go-zero/core/stores/sqlx",
		"github.com/Danceiny/sentinel-golang/core/flow",
		"github.com/google/uuid",
		"github.com/zeromicro/go-zero/core/stores/redis", // RedisConf
		"github.com/paulmach/orb",                        // Point
		"github.com/aws/aws-sdk-go-v2/service/sesv2",     //SendEmailInput
		"net/http",
	}
)

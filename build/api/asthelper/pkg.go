package asthelper

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"golang.org/x/mod/modfile"
	"golang.org/x/tools/go/packages"
)

// 判断是否第三方组件（生产级实现）
func isThirdPartyComponent(fullKey string) bool {
	// 处理vendor路径
	cleanKey := strings.TrimPrefix(fullKey, "vendor/")

	// 分离包路径和类型名
	parts := strings.Split(cleanKey, ".")
	if len(parts) < 2 {
		return false // 无效格式
	}
	pkgPath := strings.Join(parts[:len(parts)-1], ".")

	// 判断标准库（标准库无域名）
	if isStdLibPackage(pkgPath) {
		return false
	}

	// 判断是否当前项目模块
	return !strings.HasPrefix(pkgPath, ModuleName)
}

// 更精准的标准库判断
func isStdLibPackage(importPath string) bool {
	// 标准库路径检测规则：
	// 1. 不包含域名部分（如没有"/"分隔符）
	// 2. 路径在GOROOT目录下
	if strings.Contains(importPath, ".") {
		return false
	}

	// 获取GOROOT路径
	goroot := runtime.GOROOT()
	if goroot == "" {
		return false
	}

	// 构造完整路径
	fullPath := filepath.Join(goroot, "src", importPath)
	_, err := os.Stat(fullPath)
	return !os.IsNotExist(err)
}

// 动态加载第三方包
func LoadThirdPartyPackage(importPath string) (*packages.Package, error) {
	// 检查缓存
	if pkg, ok := thirdPartyCache.Load(importPath); ok {
		return pkg.(*packages.Package), nil
	}

	// 获取模块版本信息
	modVersion, err := getModuleVersion(importPath)
	if err != nil {
		return nil, fmt.Errorf("无法获取模块版本: %w", err)
	}

	// 构建完整包路径（示例路径，需适配实际环境）
	fullPath := fmt.Sprintf("%s/pkg/mod/%s@%s",
		os.Getenv("GOPATH"),
		importPath,
		modVersion,
	)

	// 加载包配置
	cfg := &packages.Config{
		Mode: packages.NeedName | packages.NeedTypes | packages.NeedSyntax | packages.NeedDeps,
		Dir:  fullPath,
	}

	// 加载包
	pkgs, err := packages.Load(cfg, ".")
	if err != nil || len(pkgs) == 0 {
		return nil, fmt.Errorf("包加载失败: %w", err)
	}

	// 缓存结果
	thirdPartyCache.Store(importPath, pkgs[0])
	return pkgs[0], nil
}

// 尝试加载第三方包
func tryLoadThirdParty(pkgPath string) *packages.Package {
	// 获取原始导入路径（处理版本号）
	rawPath := strings.Split(pkgPath, ".")[0]

	// 检查白名单（生产环境配置）
	if !isInAutoLoadList(rawPath) {
		//log.Printf("第三方包 %s 不在自动加载白名单", rawPath)
		return nil
	}

	log.Printf("tryLoadThirdParty %s", rawPath)
	// 动态加载
	pkg, err := LoadThirdPartyPackage(rawPath)
	if err != nil {
		log.Printf("tryLoadThirdParty failed: %s 原因: %v", rawPath, err)
		return nil
	}

	// 注册该包的所有类型
	registerThirdPartyTypes(pkg)
	return pkg
}

// 从go.mod获取模块版本
func getModuleVersion(importPath string) (string, error) {
	goModData, err := os.ReadFile("go.mod")
	if err != nil {
		return "", err
	}

	modFile, err := modfile.Parse("go.mod", goModData, nil)
	if err != nil {
		return "", err
	}

	for _, req := range modFile.Require {
		if req.Mod.Path == importPath {
			return req.Mod.Version, nil
		}
	}

	return "", fmt.Errorf("未找到模块: %s", importPath)
}
func isInAutoLoadList(pkgPath string) bool {
	for _, allowed := range AutoLoadList {
		if strings.HasPrefix(pkgPath, allowed) {
			return true
		}
	}
	return false
}

// 安全提取根包路径
func extractRootPackage(fullKey string) string {
	parts := strings.Split(fullKey, ".")
	for i, part := range parts {
		if strings.Contains(part, "/") {
			return strings.Join(parts[:i+1], ".")
		}
	}
	return fullKey
}

services:
  mysql:
    image: mysql:8.0
    container_name: mysql
    ports: ["3306:3306"]
    environment:
      MYSQL_ROOT_PASSWORD: "123456"
      MYSQL_DATABASE: "hoteldev"
    volumes:
      - ${DATA_DIR}/mysql:/var/lib/mysql
      - ${PROJECT_PATH}/build/dev/init.sql:/docker-entrypoint-initdb.d/init.sql # 挂载初始化脚本
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-uroot", "-p123456"]
      interval: 10s
    restart: on-failure:3

  redis:
    image: redis:7-alpine
    container_name: redis
    ports: ["6379:6379"]
    command: redis-server --appendonly yes --save 60 1 --maxmemory 256mb
    volumes:
      - ${DATA_DIR}/redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]

  nsqlookupd:
    image: nsqio/nsq:latest
    container_name: nsqlookupd
    ports:
      - "4160:4160"
      - "4161:4161"
    command: /nsqlookupd
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O- http://localhost:4161/ping || exit 1"]

  nsqd:
    image: nsqio/nsq:latest
    container_name: nsqd
    depends_on:
      nsqlookupd:
        condition: service_healthy
    ports:
      - "4150:4150"
      - "4151:4151"
    command: /nsqd --data-path=/data --lookupd-tcp-address=nsqlookupd:4160
    volumes:
      - ${DATA_DIR}/nsq:/data

  nsqadmin:
    image: nsqio/nsq:latest
    container_name: nsqadmin
    ports:
      - "4171:4171"
    command: /nsqadmin --lookupd-http-address=nsqlookupd:4161


  # 新增 Prometheus 服务
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ${PROJECT_PATH}/build/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml  # 相对路径配置
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--web.listen-address=0.0.0.0:9090'  # 指定监听地址和端口
    # 关键配置：允许访问宿主机网络
#    network_mode: "host"
    restart: always

  # 新增 Grafana 服务
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - ${DATA_DIR}/grafana/data:/var/lib/grafana
      - ${DATA_DIR}/grafana/provisioning:/etc/grafana/provisioning
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    networks:
      - ${NETWORK_NAME}
    depends_on:
      - prometheus

#  # 添加 NSQ Exporter（如果需要）
#  nsq-exporter:
#    image: justwatchcom/nsq_exporter:latest
#    ports:
#      - "9117:9117"
#    command:
#      - "--nsqd.addr=nsqd:4150"
#      - "--nsqlookupd.addr=nsqlookupd:4161"
#    networks:
#      - ${NETWORK_NAME}
#    depends_on:
#      - nsqd
#      - nsqlookupd

networks:
  nsq-net:
    name: ${NETWORK_NAME}
    driver: bridge
    attachable: true
    labels:
      com.docker.compose.network: ${NETWORK_NAME}

volumes:
  prometheus_data:
  grafana_data:
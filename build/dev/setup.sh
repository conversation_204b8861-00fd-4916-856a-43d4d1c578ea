#!/bin/bash
set -eo pipefail

# 加载环境变量
source ./build/dev/.env
CFG_FILE=./build/dev/docker-compose.yml

init_data_dir() {
  echo "初始化数据目录 -> ${DATA_DIR}"
  mkdir -p "${DATA_DIR}/mysql" \
           "${DATA_DIR}/redis" \
           "${DATA_DIR}/nsq" \
           "${DATA_DIR}/prometheus"

  # 设置目录权限（生产环境建议更严格的权限）
  find "${DATA_DIR}" -type d -exec chmod 775 {} \;
  echo "数据目录准备就绪"
}

start_services() {
  echo "启动容器服务..."
  docker-compose -f ${CFG_FILE} up -d --build

  echo -e "\n等待服务初始化（约15秒）..."
  for i in {1..5}; do
    sleep 3
    printf "."
  done
}

verify_services() {
  echo -e "\n\n验证服务状态："
  docker-compose -f ${CFG_FILE} ps | awk '
    NR==1 {print "容器状态概览:"; next}
    {printf "%-12s %-12s %s\n", $1, $4, $5}
  '

  echo -e "\n详细健康检查："
  for service in mysql redis nsqlookupd prometheus grafana; do
    container_id=$(docker-compose -f ${CFG_FILE} ps -q ${service})
    # 获取健康状态并处理未配置情况
    health_status=$(docker inspect --format '{{if .State.Health}}{{.State.Health.Status}}{{else}}N/A{{end}}' ${container_id} 2>/dev/null || echo "检查失败")
    printf "%-12s %s\n" "${service}" "${health_status}"
  done
}

show_access_info() {
  echo -e "\n访问信息："
  echo "MySQL:        mysql://localhost:3306 (root/123456)"
  echo "Redis:        redis://localhost:6379"
  echo "NSQ Admin:    http://localhost:4171"
  echo "NSQLookupd:   http://localhost:4161"
  echo "Grafana:      http://localhost:3000"
  echo "Prometheus:   http://localhost:9090"
}

main() {
  init_data_dir
  start_services
  verify_services
  show_access_info
}

main
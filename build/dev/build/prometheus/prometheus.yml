global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # 收集 Prometheus 自身的指标
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 收集 GoZero 应用的指标
  - job_name: 'gozero_app'
    static_configs:
      - targets: ['host.docker.internal:9091']
    metrics_path: '/metrics'

  # 收集多个 MySQL 的指标
  - job_name: 'mysql'
    static_configs:
      - targets:
          - 'localhost:3306'
        # 可以根据需要添加更多 MySQL Exporter 地址
    metrics_path: '/metrics'

  # 收集多个 Redis 的指标
  - job_name: 'redis'
    static_configs:
      - targets:
          - 'localhost:6379'
#          - 'localhost:6380'
        # 可以根据需要添加更多 Redis Exporter 地址
    metrics_path: '/metrics'

  # 收集 NSQ 的指标
  - job_name: 'nsq'
    static_configs:
      - targets: ['localhost:4151']
    metrics_path: '/metrics'
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: localhost:9117

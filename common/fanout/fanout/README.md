# common/fanout/fanout

本目录为fanout核心实现目录，包含分发/广播核心逻辑、工具等。

## 主要内容

- fanout核心Go代码

## 迭代开发约定

- 核心逻辑需关注高性能、可扩展、可测试
- 变更需同步更新README

## 注意事项

- 禁止硬编码敏感信息
- 变更需评估对主流程的影响

功能:

- 支持定义Worker 数量的goroutine，进行消费

示例:

```golang
// 名称为cache 执行线程为1 buffer长度为1024
cache := fanout.New("cache", fanout.Worker(1), fanout.Buffer(1024))
cache.Do(c, func (c context.Context) { SomeFunc(c, args...) })
cache.Close()
```

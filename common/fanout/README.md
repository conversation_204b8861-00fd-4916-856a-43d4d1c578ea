# common/fanout

本目录为全局fanout分发/广播相关目录，包含fanout实现、测试、文档等。

## 主要内容

- fanout实现Go代码
- 测试用例、文档

## 迭代开发约定

- fanout需关注解耦、可扩展、可测试
- 变更需同步更新README

## 注意事项

- 禁止硬编码敏感信息
- fanout变更需评估对主流程的影响

功能:

- 支持定义Worker 数量的goroutine，进行消费

示例:

```golang
// 名称为cache 执行线程为1 buffer长度为1024
cache := fanout.New("cache", fanout.Worker(1), fanout.Buffer(1024))
cache.Do(c, func (c context.Context) { SomeFunc(c, args...) })
cache.Close()
```

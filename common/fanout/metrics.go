package fanout

import (
	"hotel/common/metrics"
)

var (
	_metricChanSize      = metrics.New().WithState("fanout_chan_size", []string{"name"})
	_metricChanCap       = metrics.New().WithState("fanout_chan_cap", []string{"name"})
	_metricCount         = metrics.New().WithCounter("fanout_count", []string{"name"})
	_metricChanFullCount = metrics.New().WithCounter("fanout_chan_full_count", []string{"name"})
)

package i18n

import (
	"runtime"
	"testing"

	"github.com/bytedance/sonic"
)

type i18nTest struct {
	En string `json:"en"`
	Zh string `json:"zh"`
}

func BenchmarkI18N_Marshal(b *testing.B) {

	// 测试空结构体场景
	b.Run("EmptyStruct-i18nTest", func(b *testing.B) {
		benchMarshal(b, i18nTest{})
	})
	// 测试空结构体场景
	b.Run("EmptyStruct-i18n", func(b *testing.B) {
		benchMarshal(b, I18N{})
	})

	// 测试带数据场景
	b.Run("WithData-i18nTest", func(b *testing.B) {
		data := i18nTest{En: "Hello", Zh: "你好"}
		benchMarshal(b, data)
	})
	b.Run("WithData-i18n", func(b *testing.B) {
		data2 := I18N{En: "Hello", Zh: "你好"}
		benchMarshal(b, data2)
	})
}

// 通用测试逻辑
func benchMarshal(b *testing.B, v interface{}) {
	// 避免编译器优化干扰
	var result []byte
	var err error

	// 重置计时器，排除初始化影响
	b.StopTimer()
	b.ResetTimer()
	b.StartTimer()

	// 并行测试（更接近真实场景）
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			result, err = sonic.Marshal(v)
			if err != nil {
				b.Fatalf("Marshal failed: %v", err)
			}
			// 确保编译器不会优化掉结果
			runtime.KeepAlive(result)
		}
	})
}

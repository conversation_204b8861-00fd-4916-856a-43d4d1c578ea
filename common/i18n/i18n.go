package i18n

import (
	"golang.org/x/text/language"
	"slices"

	"hotel/common/utils"
)

var (
	ZhCn_ISO3166 = language.Make("zh-CN").String()
	EnUs_ISO3166 = language.Make("en-US").String()
)
var (
	ZhCn = []string{"zh-CN", "zh_CN", "zh", "cn", "ZH_CN"}
	EnUs = []string{"en-US", "en_US", "en", "EN_US"}
)

func IsCN(v string) bool {
	return slices.Contains(ZhCn, v)
}

type I18N struct {
	En string `json:"en,omitempty"` // English,default system language
	Zh string `json:"zh,omitempty"` // Chinese,中文
	Ar string `json:"ar,omitempty"` // Arabian,اللغة العربية
}

func (i I18N) Range(f func(lang string, value string)) {
	if i.En != "" {
		f("en", i.En)
	}
	if i.Zh != "" {
		f("zh", i.Zh)
	}
	if i.Ar != "" {
		f("ar", i.Ar)
	}
}

func (i I18N) IsEmpty() bool {
	return utils.IsZero(i)
}
func (i I18N) MergeUpdate(in I18N) I18N {
	if in.En != "" {
		i.En = in.En
	}
	if in.Zh != "" {
		i.Zh = in.Zh
	}
	if in.Ar != "" {
		i.Ar = in.Ar
	}
	return i
}
func (i I18N) String() string {
	return utils.ToJSON(i)
}

func New(en, zh string) I18N {
	return I18N{
		Zh: zh, En: en,
	}
}

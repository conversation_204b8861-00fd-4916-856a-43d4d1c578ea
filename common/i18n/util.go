package i18n

// ContainsChinese checks if the string contains Chinese characters
func ContainsChinese(s string) bool {
	for _, r := range s {
		// Check for common Chinese character ranges
		if (r >= 0x4e00 && r <= 0x9fff) || // Main CJK Unified Ideographs
			(r >= 0x3400 && r <= 0x4dbf) || // CJK Extension A
			(r >= 0x20000 && r <= 0x2a6df) || // CJK Extension B
			(r >= 0x2a700 && r <= 0x2b73f) || // CJK Extension C
			(r >= 0x2b740 && r <= 0x2b81f) || // CJK Extension D
			(r >= 0x2b820 && r <= 0x2ceaf) { // CJK Extension E
			return true
		}
	}
	return false
}

// AllChineseOrJapanese checks if the string are all Chinese characters
func AllChineseOrJapanese(s string) bool {
	for _, r := range s {
		// Check for common Chinese character ranges and Japanese ranges
		if !((r >= 0x4e00 && r <= 0x9fff) || // Main CJK Unified Ideographs
			(r >= 0x3400 && r <= 0x4dbf) || // CJK Extension A
			(r >= 0x20000 && r <= 0x2a6df) || // CJK Extension B
			(r >= 0x2a700 && r <= 0x2b73f) || // CJK Extension C
			(r >= 0x2b740 && r <= 0x2b81f) || // CJK Extension D
			(r >= 0x2b820 && r <= 0x2ceaf) || // CJK Extension E
			(r >= 0x3040 && r <= 0x309F) || // Hiragana
			(r >= 0x30A0 && r <= 0x30FF) || // Katakana
			(r >= 0xFF66 && r <= 0xFF9F)) { // Half-width Katakana
			return false
		}
	}
	return true
}

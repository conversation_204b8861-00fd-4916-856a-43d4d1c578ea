# common/gray

本目录为全局灰度发布/流量分配相关目录，包含灰度策略、配置、工具等。

## 主要内容

- 灰度策略实现Go代码
- 配置、测试用例、文档

## 迭代开发约定

- 灰度策略需关注可扩展、可测试
- 变更需同步更新README

## 注意事项

- 禁止硬编码敏感信息
- 灰度策略变更需评估对主流程的影响

# Why not feature-gate

1. 业务匹配度存疑
2. fg无本地缓存，性能不满足要求

# Target

3. 本地缓存支持

# Usage

> 支持按照人员和部门灰度，灰度到的人群，提出差申请时可以收到天气提醒卡片。
>
>1. 具体灰度计划（全球平均每天出差申请数3410, 中国区平均每天出差申请数2170）
>2. 1月25日：产品上线，将产品、研发、业务所在地的业务部门加到灰度
>3. 1月29日：灰度1万人，预计每天将产生170个出差申请，产生40次天气推送
>4. 2月4日：灰度4万人（20%员工）
>5. 2月7日：全量

package gray

import (
	"context"
	"math"
	"testing"

	"github.com/smartystreets/goconvey/convey"
)

func TestConfig_HitByDepartment(t *testing.T) {
	type fields struct {
		ConfigByDepartment ConfigByDepartment
		Global             ConfigItem
	}
	type arg struct {
		departmentCode string
		employeeNumber int64
	}
	type args []arg
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []bool
	}{
		{
			name: "hit by whitelist",
			fields: fields{
				Global: ConfigItem{ConfigList: ConfigList{WhitelistEmployeeNumber: []int64{11}}},
				ConfigByDepartment: ConfigByDepartment{
					DepartmentCodes: []string{"10"},
					DepartmentShare: ConfigItem{
						Disable: false,
						ConfigList: ConfigList{
							WhitelistEmployeeNumber: []int64{1},
						},
						ConfigRatio: ConfigRatio{
							Ratio: 0.0,
						},
					},
				},
			},
			args: []arg{
				{
					departmentCode: "10",
					employeeNumber: 1,
				},
				{
					departmentCode: "101",
					employeeNumber: 1,
				},
				{
					departmentCode: "1012",
					employeeNumber: 11,
				},
			},
			want: []bool{true, false, true},
		},
		{
			name: "hit by department",
			fields: fields{
				ConfigByDepartment: ConfigByDepartment{
					DepartmentCodes: []string{"10"},
					DepartmentShare: ConfigItem{
						Disable: false,
						ConfigRatio: ConfigRatio{
							Ratio: 1,
						},
					},
				},
			},
			args: []arg{
				{
					departmentCode: "10",
					employeeNumber: 1,
				},
				{
					departmentCode: "101",
					employeeNumber: 1,
				},
			},
			want: []bool{true, false},
		},
		{
			name: "hit by ratio",
			fields: fields{
				ConfigByDepartment: ConfigByDepartment{
					DepartmentCodes: []string{"10"},
					DepartmentShare: ConfigItem{
						Disable: false,
						ConfigRatio: ConfigRatio{
							Ratio: 1,
						},
					},
				},
			},
			args: []arg{
				{
					departmentCode: "10",
				},
				{
					departmentCode: "101",
				},
			},
			want: []bool{true, false},
		},
		{
			name: "hit by common & share",
			fields: fields{
				ConfigByDepartment: ConfigByDepartment{
					DepartmentCodes: []string{"10", "101"},
					DepartmentShare: ConfigItem{
						Disable: false,
						ConfigRatio: ConfigRatio{
							Ratio: 1,
						},
					},
					Departments: map[string]ConfigItem{
						"101": {
							ConfigList: ConfigList{
								WhitelistEmployeeNumber: []int64{101},
							},
						},
					},
				},
			},
			args: []arg{
				{
					departmentCode: "10",
				},
				{
					departmentCode: "101",
					employeeNumber: 101,
				},
			},
			want: []bool{true, true},
		},
		{
			name: "disabled",
			fields: fields{
				Global: ConfigItem{
					Disable: true,
				},
				ConfigByDepartment: ConfigByDepartment{
					DepartmentCodes: []string{"10"},
					DepartmentShare: ConfigItem{
						Disable: false,
						ConfigRatio: ConfigRatio{
							Ratio: 1,
						},
					},
				},
			},
			args: []arg{
				{
					departmentCode: "10",
				},
				{
					departmentCode: "101",
				},
			},
			want: []bool{false, false},
		},
		{
			name: "window",
			fields: fields{
				Global: ConfigItem{
					ConfigWindow: ConfigWindow{
						Hash:  "mod",
						Start: 0,
						End:   50,
						Mod:   100,
					},
				},
			},
			args: []arg{
				{
					employeeNumber: 99,
				},
				{
					employeeNumber: 10,
				},
			},
			want: []bool{false, true},
		},
	}
	for i, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				ConfigByDepartment: tt.fields.ConfigByDepartment,
				Global:             tt.fields.Global,
			}
			for j, arg := range tt.args {
				if got := c.HitByDepartment(context.Background(), arg.departmentCode, arg.employeeNumber); got != tt.want[j] {
					t.Errorf("HitByDepartment() = %v, want %v index %d-%d", got, tt.want, i, j)
				}
			}
		})
	}
}

func Test_hitIntID(t *testing.T) {
	convey.Convey("hitIntID", t, func() {
		ratio := 0.2
		cnt := 1
		const tryTimes = 10000000
		for i := int64(0); i < tryTimes; i++ {
			if hitBizIDWithRatio(i, ratio) {
				cnt++
			}
		}
		convey.So(math.Abs(float64(cnt)/float64(tryTimes)-ratio), convey.ShouldBeLessThan, 0.1)
	})
}

func TestConfig_HitByCity(t *testing.T) {
	convey.Convey("HitByCity", t, func() {
		c := &Config{Global: ConfigItem{Disable: true}, Key: "bizScene1"}
		convey.So(c.HitByCity(context.Background(), "10", 1), convey.ShouldBeFalse)
		c = &Config{Global: ConfigItem{ConfigList: ConfigList{WhitelistEmployeeNumber: []int64{1}}}, Key: "bizScene1"}
		convey.So(c.HitByCity(context.Background(), "10", 1), convey.ShouldBeTrue)
		c = &Config{Key: "bizScene1", ConfigByCity: ConfigByCity{CityCodes: []string{"10"}, CityShare: ConfigItem{ConfigRatio: ConfigRatio{Ratio: 1}}}}
		convey.So(c.HitByCity(context.Background(), "10", 1), convey.ShouldBeTrue)
		convey.So(c.HitByCity(context.Background(), "11", 1), convey.ShouldBeFalse)
	})
}

func TestConfig_HitByBizID(t *testing.T) {
	convey.Convey("HitByBizID", t, func() {
		c := &Config{Global: ConfigItem{Disable: true}, Key: "bizScene1"}
		convey.So(c.HitByBizID(context.Background(), "10"), convey.ShouldBeFalse)
		c = &Config{Global: ConfigItem{ConfigList: ConfigList{WhitelistBizID: []string{"10"}}}, Key: "bizScene1"}
		convey.So(c.HitByBizID(context.Background(), "10"), convey.ShouldBeTrue)
		convey.So(c.HitByBizID(context.Background(), "11"), convey.ShouldBeFalse)
		c = &Config{Global: ConfigItem{ConfigRatio: ConfigRatio{Ratio: 0.5}}, Key: "bizScene1"}
		convey.So(c.HitByBizID(context.Background(), "10"), convey.ShouldBeFalse)
		convey.So(c.HitByBizID(context.Background(), "14"), convey.ShouldBeTrue)
	})
}

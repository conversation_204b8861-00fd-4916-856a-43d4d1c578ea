package gray

import (
	"context"
	"hash/crc32"
	"math/rand"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cast"

	"hotel/common/log"
)

const (
	ratioPrecision = 10000.0
)

var (
	localRand = rand.New(rand.NewSource(time.Now().UnixNano()))
)

func crc(a interface{}) int64 {
	return int64(crc32.ChecksumIEEE([]byte(cast.ToString(a))))
}

func hitBizIDWithRatio(id interface{}, ratio float64) bool {
	return float64(crc(id)%int64(ratioPrecision)) < ratio*ratioPrecision
}

func (c ConfigItem) hitBizID(ctx context.Context, bizID string) (res bool) {
	defer func() {
		log.Infoc(ctx, "%s hitBizIDWithRatio %v bizID(%v)", c.getKey(ctx), res, bizID)
	}()

	if lo.Contains(c.BlacklistBizID, bizID) {
		return false
	}

	if lo.Contains(c.ConfigList.WhitelistBizID, bizID) {
		log.Infoc(ctx, "%s hitBizIDWithRatio true, bizID(%v)", c.getKey(ctx), bizID)
		return true
	}

	if bizID == "" { // 不传employeeNumber的情况，直接取概率
		return localRand.Float64() < c.ConfigRatio.Ratio
	}

	if hitBizIDWithRatio(bizID, c.ConfigRatio.Ratio) {
		log.Infoc(ctx, "%s hitBizID true by hitBizIDWithRatio, bizid(%v) ratio(%.4f)", c.getKey(ctx), bizID, c.ConfigRatio.Ratio)
		return true
	}

	intid := func() int64 {
		intid, err := cast.ToInt64E(bizID)
		if err != nil {
			return crc(intid)
		}
		return intid
	}()

	if c.ConfigWindow.HitInt64(intid) {
		log.Infoc(ctx, "%s hitBizID true by ConfigWindow.HitInt64, bizid(%d) window(%#v)", c.getKey(ctx), intid, c.ConfigWindow)
		return true
	}

	log.Infoc(ctx, "%s hitBizID false, bizID(%v)(%d) ratio(%v) list(%v) window(%v)", c.getKey(ctx), bizID, intid, c.ConfigRatio, c.ConfigList, c.ConfigWindow)
	return false
}

func (c ConfigItem) getKey(ctx context.Context) string {
	globalKey := retrieveKeyFromCtx(ctx)
	if globalKey == "" {
		return c.Key
	}
	return globalKey + ":" + c.Key
}

func (c ConfigItem) hitEmployeeNumber(ctx context.Context, employeeNumber int64) (res bool) {
	defer func() {
		log.Infoc(ctx, "%s hitEmployeeNumber %v employeeNumber(%d)", c.getKey(ctx), res, employeeNumber)
	}()

	if lo.Contains(c.BlacklistEmployeeNumber, employeeNumber) {
		return false
	}

	if lo.Contains(c.ConfigList.WhitelistEmployeeNumber, employeeNumber) {
		log.Infoc(ctx, "%s hitEmployeeNumber true, employeeNumber(%d)", c.getKey(ctx), employeeNumber)
		return true
	}

	if employeeNumber <= 0 { // 不传employeeNumber的情况，直接取概率
		return localRand.Float64() < c.ConfigRatio.Ratio
	}

	if hitBizIDWithRatio(employeeNumber, c.ConfigRatio.Ratio) {
		log.Infoc(ctx, "%s hitEmployeeNumber true by hitBizIDWithRatio, employeeNumber(%d) ratio(%.4f)", c.getKey(ctx), employeeNumber, c.ConfigRatio.Ratio)
		return true
	}
	if c.ConfigWindow.HitInt64(employeeNumber) {
		log.Infoc(ctx, "%s hitEmployeeNumber true by ConfigWindow.HitInt64, employeeNumber(%d) window(%#v)", c.getKey(ctx), employeeNumber, c.ConfigWindow)
		return true
	}
	log.Infoc(ctx, "%s hitEmployeeNumber false, employeeNumber(%d) ratio(%v) list(%v) window(%v)", c.getKey(ctx), employeeNumber, c.ConfigRatio, c.ConfigList.String(), c.ConfigWindow)
	return false
}

func (c *Config) hitGlobalByEmployeeNumber(ctx context.Context, employeeNumber int64) bool {
	if c.Global.hitEmployeeNumber(ctx, employeeNumber) {
		return true
	}
	// more ...
	return false
}

func (c *Config) hitGlobalByBizID(ctx context.Context, bizID string) bool {
	if c.Global.hitBizID(ctx, bizID) {
		return true
	}
	// more ...
	return false
}

func (c *Config) Enabled(ctx context.Context) bool {
	return !c.Disabled(ctx)
}

func (c *Config) Disabled(ctx context.Context) bool {
	if c == nil {
		return true
	}
	return c.Global.Disable
}

func (c *Config) HitByBizID(ctx context.Context, bizID string) bool {
	if c.Disabled(ctx) {
		log.Infoc(ctx, "%s HitByBizID false disabled, bizID(%v)", c.getKey(), bizID)
		return false
	}
	ctx = newCtxWithKey(ctx, c.Key)
	if c.hitGlobalByBizID(ctx, bizID) {
		log.Infoc(ctx, "%s HitByBizID true hitGlobalByBizID bizID(%v)", c.Key, bizID)
		return true
	}
	return false
}

func (c *Config) HitByDepartment(ctx context.Context, departmentCode string, employeeNumber int64) bool {
	if c.Disabled(ctx) {
		log.Infoc(ctx, "HitByDepartment false disabled, departmentCode(%s) employeeNumber(%d)", departmentCode, employeeNumber)
		return false
	}
	if c.hitGlobalByEmployeeNumber(ctx, employeeNumber) {
		log.Infoc(ctx, "HitByDepartment true hitGlobalByEmployeeNumber, departmentCode(%s) employeeNumber(%d)", departmentCode, employeeNumber)
		return true
	}
	if !lo.Contains(c.ConfigByDepartment.DepartmentCodes, departmentCode) {
		log.Infoc(ctx, "HitByDepartment false by departmentCode, departmentCode(%s) employeeNumber(%d)", departmentCode, employeeNumber)
		return false
	}

	ctx = newCtxWithKey(ctx, c.Key)
	if depart, ok := c.ConfigByDepartment.Departments[departmentCode]; ok {
		return depart.hitEmployeeNumber(ctx, employeeNumber)
	}
	return c.DepartmentShare.hitEmployeeNumber(ctx, employeeNumber)
}

func (c *Config) HitByCity(ctx context.Context, cityCode string, employeeNumber int64) bool {
	if c.Disabled(ctx) {
		log.Infoc(ctx, "%s HitByCity false disabled, cityCode(%s) employeeNumber(%d)", c.getKey(), cityCode, employeeNumber)
		return false
	}
	if c.hitGlobalByEmployeeNumber(ctx, employeeNumber) {
		log.Infoc(ctx, "%s HitByCity true hitGlobalByEmployeeNumber, cityCode(%s) employeeNumber(%d)", c.Key, cityCode, employeeNumber)
		return true
	}
	if !lo.Contains(c.ConfigByCity.CityCodes, cityCode) {
		log.Infoc(ctx, "%s HitByCity false by departmentCode, cityCode(%s) employeeNumber(%d)", c.Key, cityCode, employeeNumber)
		return false
	}

	ctx = newCtxWithKey(ctx, c.Key)
	if city, ok := c.ConfigByCity.Cities[cityCode]; ok {
		return city.hitEmployeeNumber(ctx, employeeNumber)
	}
	return c.CityShare.hitEmployeeNumber(ctx, employeeNumber)
}

func (c *Config) getKey() string {
	if c == nil {
		return ""
	}
	return c.Key
}

type keyKey struct{}

func newCtxWithKey(ctx context.Context, k string) context.Context {
	return context.WithValue(ctx, keyKey{}, k)
}
func retrieveKeyFromCtx(ctx context.Context) string {
	k := ctx.Value(keyKey{})
	if v, ok := k.(string); ok {
		return v
	}
	return ""
}

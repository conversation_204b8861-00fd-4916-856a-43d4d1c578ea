package gray

import (
	"testing"
)

func TestConfigList_String(t *testing.T) {
	type fields struct {
		WhitelistEmployeeNumber []int64
		BlacklistEmployeeNumber []int64
		WhitelistBizID          []string
		BlacklistBizID          []string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "test1",
			fields: fields{
				WhitelistEmployeeNumber: []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25},
				BlacklistEmployeeNumber: []int64{4, 5, 6},
				WhitelistBizID:          []string{"1", "2", "3"},
				BlacklistBizID:          []string{"4", "5", "6"},
			},
			want: `whitelist_employee_number: [1,2,3,4,5,6,7,8,9,10] blacklist_employee_number: [4,5,6] whitelist_biz_id: ["1","2","3"] blacklist_biz_id: ["4","5","6"]`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ConfigList{
				WhitelistEmployeeNumber: tt.fields.WhitelistEmployeeNumber,
				BlacklistEmployeeNumber: tt.fields.BlacklistEmployeeNumber,
				WhitelistBizID:          tt.fields.WhitelistBizID,
				BlacklistBizID:          tt.fields.BlacklistBizID,
			}
			if got := c.String(); got != tt.want {
				t.Errorf("String() = \n%v, want \n%v", got, tt.want)
			}
		})
	}
}

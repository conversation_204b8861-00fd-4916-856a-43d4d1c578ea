package gray

import (
	"context"
	"hash/crc32"
	"math/rand"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	ratioPrecision = 10000.0
)

var (
	localRand = rand.New(rand.NewSource(time.Now().UnixNano()))
)

func crc(a interface{}) int64 {
	return int64(crc32.ChecksumIEEE([]byte(cast.ToString(a))))
}

func hitBizIDWithRatio(id interface{}, ratio float64) bool {
	return float64(crc(id)%int64(ratioPrecision)) < ratio*ratioPrecision
}

func (c ConfigItem) hitBizID(ctx context.Context, bizID string) (res bool) {
	defer func() {
		logx.WithContext(ctx).Infof("hitBizIDWithRatio %v bizID(%v)", res, bizID)
	}()

	if lo.Contains(c.BlacklistBizID, bizID) {
		return false
	}

	if lo.Contains(c.ConfigList.WhitelistBizID, bizID) {
		logx.WithContext(ctx).Infof("hitBizIDWithRatio true, bizID(%v)", bizID)
		return true
	}

	if bizID == "" { // 不传UserID的情况，直接取概率
		return localRand.Float64() < c.ConfigRatio.Ratio
	}

	if hitBizIDWithRatio(bizID, c.ConfigRatio.Ratio) {
		logx.WithContext(ctx).Infof("hitBizID true by hitBizIDWithRatio, bizid(%v) ratio(%.4f)", bizID, c.ConfigRatio.Ratio)
		return true
	}

	intid := func() int64 {
		intid, err := cast.ToInt64E(bizID)
		if err != nil {
			return crc(intid)
		}
		return intid
	}()

	if c.ConfigWindow.HitInt64(intid) {
		logx.WithContext(ctx).Infof("hitBizID true by ConfigWindow.HitInt64, bizid(%d) window(%#v)", intid, c.ConfigWindow)
		return true
	}

	logx.WithContext(ctx).Infof("hitBizID false, bizID(%v)(%d) ratio(%v) list(%v) window(%v)", bizID, intid, c.ConfigRatio, c.ConfigList, c.ConfigWindow)
	return false
}

func (c ConfigItem) hitUserID(ctx context.Context, UserID int64) (res bool) {
	defer func() {
		logx.WithContext(ctx).Infof("hitUserID %v UserID(%d)", res, UserID)
	}()

	if lo.Contains(c.BlacklistUserID, UserID) {
		return false
	}

	if lo.Contains(c.ConfigList.WhitelistUserID, UserID) {
		logx.WithContext(ctx).Infof("hitUserID true, UserID(%d)", UserID)
		return true
	}

	if UserID <= 0 { // 不传UserID的情况，直接取概率
		return localRand.Float64() < c.ConfigRatio.Ratio
	}

	if hitBizIDWithRatio(UserID, c.ConfigRatio.Ratio) {
		logx.WithContext(ctx).Infof("hitUserID true by hitBizIDWithRatio, UserID(%d) ratio(%.4f)", UserID, c.ConfigRatio.Ratio)
		return true
	}
	if c.ConfigWindow.HitInt64(UserID) {
		logx.WithContext(ctx).Infof("hitUserID true by ConfigWindow.HitInt64, UserID(%d) window(%#v)", UserID, c.ConfigWindow)
		return true
	}
	logx.WithContext(ctx).Infof("hitUserID false, UserID(%d) ratio(%v) list(%v) window(%v)", UserID, c.ConfigRatio, c.ConfigList.String(), c.ConfigWindow)
	return false
}

func (c *Config) hitGlobalByUserID(ctx context.Context, UserID int64) bool {
	if c.Global.hitUserID(ctx, UserID) {
		return true
	}
	// more ...
	return false
}

func (c *Config) hitGlobalByBizID(ctx context.Context, bizID string) bool {
	if c.Global.hitBizID(ctx, bizID) {
		return true
	}
	// more ...
	return false
}

func (c *Config) Enabled(ctx context.Context) bool {
	return !c.Disabled(ctx)
}

func (c *Config) Disabled(ctx context.Context) bool {
	if c == nil {
		return true
	}
	return c.Global.Disable
}

func (c *Config) HitByBizID(ctx context.Context, bizID string) bool {
	if c.Disabled(ctx) {
		logx.WithContext(ctx).Infof("HitByBizID false disabled, bizID(%v)", bizID)
		return false
	}
	if c.hitGlobalByBizID(ctx, bizID) {
		logx.WithContext(ctx).Infof("HitByBizID true hitGlobalByBizID bizID(%v)", bizID)
		return true
	}
	return false
}

func (c *Config) HitByDepartment(ctx context.Context, departmentCode string, UserID int64) bool {
	if c.Disabled(ctx) {
		logx.WithContext(ctx).Infof("HitByDepartment false disabled, departmentCode(%s) UserID(%d)", departmentCode, UserID)
		return false
	}
	if c.hitGlobalByUserID(ctx, UserID) {
		logx.WithContext(ctx).Infof("HitByDepartment true hitGlobalByUserID, departmentCode(%s) UserID(%d)", departmentCode, UserID)
		return true
	}
	if !lo.Contains(c.ConfigByDepartment.DepartmentCodes, departmentCode) {
		logx.WithContext(ctx).Infof("HitByDepartment false by departmentCode, departmentCode(%s) UserID(%d)", departmentCode, UserID)
		return false
	}

	if depart, ok := c.ConfigByDepartment.Departments[departmentCode]; ok {
		return depart.hitUserID(ctx, UserID)
	}
	return c.DepartmentShare.hitUserID(ctx, UserID)
}

func (c *Config) HitByCity(ctx context.Context, cityCode string, UserID int64) bool {
	if c.Disabled(ctx) {
		logx.WithContext(ctx).Infof("HitByCity false disabled, cityCode(%s) UserID(%d)", cityCode, UserID)
		return false
	}
	if c.hitGlobalByUserID(ctx, UserID) {
		logx.WithContext(ctx).Infof("HitByCity true hitGlobalByUserID, cityCode(%s) UserID(%d)", cityCode, UserID)
		return true
	}
	if !lo.Contains(c.ConfigByCity.CityCodes, cityCode) {
		logx.WithContext(ctx).Infof("HitByCity false by departmentCode, cityCode(%s) UserID(%d)", cityCode, UserID)
		return false
	}

	if city, ok := c.ConfigByCity.Cities[cityCode]; ok {
		return city.hitUserID(ctx, UserID)
	}
	return c.CityShare.hitUserID(ctx, UserID)
}

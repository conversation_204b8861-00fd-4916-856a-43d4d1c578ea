package gray

import (
	"strings"

	"hotel/common/utils"
)

type ConfigByDepartment struct {
	// DepartmentCodes 开启灰度的部门code列表
	DepartmentCodes []string `mapstructure:"department_codes" default:"[]" yaml:"department_codes" json:"department_codes"`
	// Departments 分部门特殊灰度
	Departments map[string]ConfigItem `mapstructure:"departments" default:"{}" yaml:"departments" json:"departments"`
	// DepartmentShare 未命中Departments，则走共享的灰度逻辑
	DepartmentShare ConfigItem `mapstructure:"department_share" default:"{}" yaml:"department_share" json:"department_share"`
}
type ConfigByCity struct {
	// CityCodes 开启灰度的城市regionCode列表
	CityCodes []string `mapstructure:"city_codes" default:"[]" yaml:"city_codes" json:"city_codes"`
	// Cities 分城市特殊灰度
	Cities map[string]ConfigItem `mapstructure:"cities" default:"{}" yaml:"cities" json:"cities"`
	// CityShare 未命中Cities，则走共享的灰度逻辑
	CityShare ConfigItem `mapstructure:"city_share" default:"{}" yaml:"city_share" json:"city_share"`
}

type ConfigRatio struct {
	Ratio float64 `mapstructure:"ratio" default:"0" yaml:"ratio" json:"ratio"`
}

type ConfigWindow struct {
	Hash  string `mapstructure:"hash" default:"" yaml:"hash" json:"hash"`
	Start int64  `mapstructure:"start" default:"-1" yaml:"start" json:"start"`
	End   int64  `mapstructure:"end" default:"-1" yaml:"end" json:"end"`
	Mod   int64  `mapstructure:"mod" default:"100" yaml:"mod" json:"mod"`
}

func (c ConfigWindow) HitInt64(id int64) bool {
	if c.Start < 0 || c.End < 0 {
		return false
	}
	var v int64
	switch c.Hash {
	case "crc32":
		v = crc(id) % c.Mod
	case "mod":
		v = id % c.Mod
	default:
		return false
	}
	return v >= c.Start && v < c.End
}

type ConfigList struct {
	WhitelistUserID []int64  `mapstructure:"whitelist_employee_number" default:"[]" yaml:"whitelist_employee_number" json:"whitelist_employee_number"`
	BlacklistUserID []int64  `mapstructure:"blacklist_employee_number" default:"[]" yaml:"blacklist_employee_number" json:"blacklist_employee_number"` // 默认情况下，黑名单优先级最高
	WhitelistBizID  []string `mapstructure:"whitelist_biz_id" default:"[]" yaml:"whitelist_biz_id" json:"whitelist_biz_id"`                            // 默认情况下，黑名单优先级最高
	BlacklistBizID  []string `mapstructure:"blacklist_biz_id" default:"[]" yaml:"blacklist_biz_id" json:"blacklist_biz_id"`                            // 默认情况下，黑名单优先级最高
}

func (c ConfigList) String() string {
	sb := strings.Builder{}
	if v := sampleInt64Slice(c.WhitelistUserID, logSampleSize); len(v) > 0 {
		sb.WriteString("whitelist_employee_number: ")
		sb.WriteString(utils.ToLogString(v))
		sb.WriteString(" ")
	}
	if v := sampleInt64Slice(c.BlacklistUserID, logSampleSize); len(v) > 0 {
		sb.WriteString("blacklist_employee_number: ")
		sb.WriteString(utils.ToLogString(v))
		sb.WriteString(" ")
	}
	if v := sampleStringSlice(c.WhitelistBizID, logSampleSize); len(v) > 0 {
		sb.WriteString("whitelist_biz_id: ")
		sb.WriteString(utils.ToLogString(v))
		sb.WriteString(" ")
	}
	if v := sampleStringSlice(c.BlacklistBizID, logSampleSize); len(v) > 0 {
		sb.WriteString("blacklist_biz_id: ")
		sb.WriteString(utils.ToLogString(v))
	}
	return sb.String()
}

type Config struct {
	// ConfigByDepartment 分部门的灰度
	ConfigByDepartment
	// ConfigByCity 分城市的灰度
	ConfigByCity
	// Global 全局配置，优先级最高
	Global ConfigItem `yaml:"global" json:"global"`
}

type ConfigItem struct {
	Disable bool `mapstructure:"disable" default:"false" yaml:"disable" json:"disable"`
	ConfigRatio
	ConfigList
	ConfigWindow
}

func sampleInt64Slice(ids []int64, size int) []int64 {
	if len(ids) <= size {
		return ids
	}
	return ids[:size]
}
func sampleStringSlice(ids []string, size int) []string {
	if len(ids) <= size {
		return ids
	}
	return ids[:size]
}

const (
	logSampleSize = 10
)

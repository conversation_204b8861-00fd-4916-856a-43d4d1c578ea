package httphelper

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

var (
	ErrInvalidJSON    = errors.New("invalid JSON response")
	ErrRequestFailed  = errors.New("request failed")
	ErrInvalidRequest = errors.New("invalid request")
)

// HttpResponse 封装 HTTP 响应
type HttpResponse struct {
	StatusCode int
	Headers    http.Header
	Body       []byte
}

// Client HTTP 客户端结构
type Client struct {
	baseURL    string
	headers    map[string]string
	httpClient *http.Client
	retryCount int
}

// New 创建新的客户端实例
func New(options ...Option) *Client {
	c := &Client{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			// 添加连接池和传输配置
			Transport: &http.Transport{
				MaxIdleConns:        100,
				IdleConnTimeout:     90 * time.Second,
				TLSHandshakeTimeout: 10 * time.Second,
			},
		},
		headers: make(map[string]string),
	}

	for _, opt := range options {
		opt(c)
	}

	return c
}

// Option 客户端配置选项
type Option func(*Client)

// WithBaseURL 设置基础URL
func WithBaseURL(url string) Option {
	return func(c *Client) {
		c.baseURL = strings.TrimRight(url, "/")
	}
}

// WithTimeout 设置超时时间
func WithTimeout(d time.Duration) Option {
	return func(c *Client) {
		c.httpClient.Timeout = d
	}
}

// WithDefaultHeader 设置默认请求头
func WithDefaultHeader(key, value string) Option {
	return func(c *Client) {
		c.headers[key] = value
	}
}

// WithRetry 设置重试次数
func WithRetry(count int) Option {
	return func(c *Client) {
		c.retryCount = count
	}
}

// WithCustomTransport 自定义传输层
func WithCustomTransport(transport http.RoundTripper) Option {
	return func(c *Client) {
		c.httpClient.Transport = transport
	}
}

// Get 发送 GET 请求
func (c *Client) Get(endpoint string, params map[string]string, headers ...map[string]string) (*HttpResponse, error) {
	return c.Request(context.Background(), "GET", endpoint, params, nil, headers...)
}

// Post 发送 POST 请求
func (c *Client) Post(endpoint string, body interface{}, headers ...map[string]string) (*HttpResponse, error) {
	return c.Request(context.Background(), "POST", endpoint, nil, body, headers...)
}

// PostJSON 发送 JSON POST 请求
func (c *Client) PostJSON(endpoint string, payload interface{}, headers ...map[string]string) (*HttpResponse, error) {
	headers = append(headers, map[string]string{"Content-Type": "application/json"})
	return c.Post(endpoint, payload, headers...)
}

// Put 发送 PUT 请求
func (c *Client) Put(endpoint string, body interface{}, headers ...map[string]string) (*HttpResponse, error) {
	return c.Request(context.Background(), "PUT", endpoint, nil, body, headers...)
}

// Delete 发送 DELETE 请求
func (c *Client) Delete(endpoint string, params map[string]string, headers ...map[string]string) (*HttpResponse, error) {
	return c.Request(context.Background(), "DELETE", endpoint, params, nil, headers...)
}

// Request 通用请求方法
func (c *Client) Request(ctx context.Context, method, endpoint string, params map[string]string, body interface{}, headers ...map[string]string) (*HttpResponse, error) {
	// 处理查询参数
	fullURL := c.baseURL + endpoint
	if params != nil {
		query := url.Values{}
		for k, v := range params {
			query.Add(k, v)
		}

		if strings.Contains(fullURL, "?") {
			fullURL += "&" + query.Encode()
		} else {
			fullURL += "?" + query.Encode()
		}
	}

	// 创建请求体
	var reqBody io.Reader
	if body != nil {
		switch v := body.(type) {
		case []byte:
			reqBody = bytes.NewReader(v)
		case string:
			reqBody = strings.NewReader(v)
		case io.Reader:
			reqBody = v
		default:
			if method != "GET" && method != "HEAD" {
				jsonData, err := json.Marshal(body)
				if err != nil {
					return nil, err
				}
				reqBody = bytes.NewReader(jsonData)
			}
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, fullURL, reqBody)
	if err != nil {
		return nil, err
	}

	// 设置默认头
	for k, v := range c.headers {
		req.Header.Set(k, v)
	}

	// 设置自定义头
	for _, h := range headers {
		for k, v := range h {
			req.Header.Set(k, v)
		}
	}

	// 自动设置内容类型（如果未指定）
	if reqBody != nil && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/octet-stream")
	}

	var resp *HttpResponse
	var lastErr error

	// 重试逻辑
	for attempt := 0; attempt <= c.retryCount; attempt++ {
		if attempt > 0 {
			// 指数退避
			backoff := time.Duration(attempt*attempt) * 500 * time.Millisecond
			select {
			case <-time.After(backoff):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}

		resp, lastErr = c.execute(req)
		if lastErr == nil && resp.StatusCode < 500 {
			return resp, nil
		}
	}

	return resp, lastErr
}

// execute 执行请求并处理响应
func (c *Client) execute(req *http.Request) (*HttpResponse, error) {
	res, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	response := &HttpResponse{
		StatusCode: res.StatusCode,
		Headers:    res.Header,
		Body:       resBody,
	}

	if res.StatusCode >= 400 {
		return response, ErrRequestFailed
	}

	return response, nil
}

// JSON 将响应体解析到指定结构
func (r *HttpResponse) JSON(v interface{}) error {
	return json.Unmarshal(r.Body, v)
}

// Text 获取响应文本
func (r *HttpResponse) Text() string {
	return string(r.Body)
}

// IsSuccess 是否成功响应 (2xx)
func (r *HttpResponse) IsSuccess() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}

// Bytes 获取原始响应字节
func (r *HttpResponse) Bytes() []byte {
	return r.Body
}

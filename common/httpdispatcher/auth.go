package httpdispatcher

import (
	"errors"
	"hotel/common/bizerr"
	"net/http"
	"strings"
)

// 权限校验中间件
func (s *ServiceDispatcher) authMiddleware(next HandlerFunc) HandlerFunc {
	return func(ctx *Context) error {
		// 获取方法元数据
		meta := ctx.Metadata

		// 无需认证的接口直接放行
		if meta.AuthMethod == "" && len(meta.Permissions) == 0 {
			return next(ctx)
		}

		// RBAC权限校验
		if len(meta.Permissions) > 0 {
			if ctx.UserInfo == nil {
				return bizerr.NewHTTP(0, CodePermissionDenied, 401)
			}

			//// 检查用户是否拥有任一所需权限
			//hasPermission := false
			//for _, required := range meta.Permissions {
			//	if s.permissionChecker.HasPermission(ctx.UserInfo.GetRoles(), required) {
			//		hasPermission = true
			//		break
			//	}
			//}
			/*
				if !hasPermission {
					return bizerr.PermissionDeniedErr.WithMessage(
						fmtMissingPermissions(meta.Permissions))
				}*/
		}

		return next(ctx)
	}
}

// 辅助函数：从Header提取Token
func extractBearerToken(r *http.Request) (string, error) {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		return "", errors.New("authorization header missing")
	}

	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return "", errors.New("invalid authorization format")
	}

	return parts[1], nil
}

// 错误转换逻辑
func convertJWTError(err error) error {
	return bizerr.AuthenticationErr.Wrap(err)
}

// 默认通配符
const DefaultWildcard = "*"

// 错误类型定义
const (
	CodeTokenInvalid     = "AUTH_TOKEN_INVALID"
	CodeTokenExpired     = "AUTH_TOKEN_EXPIRED"
	CodeAuthFailed       = "AUTH_FAILED"
	CodePermissionDenied = "PERMISSION_DENIED"
)

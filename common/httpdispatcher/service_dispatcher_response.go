package httpdispatcher

import (
	"net/http"
	"reflect"
	"time"

	"github.com/spf13/cast"

	"hotel/common/httphelper"
	"hotel/common/log"
	common "hotel/common/protocol"

	"hotel/common/bizerr"
	"hotel/common/metrics"

	"github.com/bytedance/sonic"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func (s *ServiceDispatcher) renderError(ctx *Context, err error) error {
	if be, ok := bizerr.CastBizErr(err); ok {
		metrics.APICallBizErrCount.Incr(ctx.Request.Context(),
			ctx.RouteInfo.Service, ctx.RouteInfo.Service,
			ctx.TenantGroup.GetName(), ctx.Customer.GetName(),
			cast.ToString(be.StatusCode()),
		)
		ctx.Response.Header().Set("Content-Type", "application/json")
		if hc := be.HTTPCode(); hc != 0 && hc != http.StatusOK {
			ctx.Response.WriteHeader(int(hc))
		} else {
			ctx.Response.WriteHeader(http.StatusOK)
		}
		return ctx.OkJson(httphelper.Response{
			BaseResp: httphelper.BaseResp{
				Code: int(be.StatusCode()),
				Msg:  be.StatusMessage(),
			},
		})
	}
	metrics.APICallBizErrCount.Incr(ctx.Request.Context(),
		ctx.RouteInfo.Service, ctx.RouteInfo.Service,
		ctx.TenantGroup.GetName(), ctx.Customer.GetName(),
		cast.ToString(http.StatusInternalServerError),
	)
	return ctx.InternalUnknownError(err)

}

func (s *ServiceDispatcher) responseMiddleware(next HandlerFunc) HandlerFunc {
	return func(ctx *Context) error {
		metrics.APICallCount.Incr(ctx.Request.Context(),
			ctx.Metadata.ServiceName, ctx.Metadata.MethodName,
			ctx.TenantGroup.GetName(), ctx.Customer.GetName(),
		)

		err := next(ctx)
		if err != nil {
			return err
		}

		// 敏感字段过滤
		filterSensitiveFields(ctx.Output, ctx.Metadata.Sensitive)

		// 业务 header 和通用 header 合并
		headers := extractHttpHeaders(ctx.Output)
		if ctx.End.IsZero() {
			ctx.End = time.Now()
		}
		commonHeaders := common.ResponseHeader{
			ServerCostMilliseconds: ctx.End.Sub(ctx.Start).Milliseconds(),
			TraceOption: common.TraceOption{
				TraceId: log.GetLogidFromContext(ctx.Request.Context()),
			},
		}

		renderHeaders(ctx.Response, headers, extractHttpHeaders(commonHeaders))
		// 统一响应格式
		return ctx.OkJson(httphelper.Response{
			Data: ctx.Output,
		})
	}
}
func renderHeaders(rw http.ResponseWriter, headers ...http.Header) {
	for _, header := range headers {
		for k, v := range header {
			for _, vv := range v {
				if vv == "" {
					continue
				}
				rw.Header().Add(k, vv)
			}
		}
	}
}

func (ctx *Context) OkJson(v interface{}) error {
	bs, err := sonic.Marshal(v)
	if err != nil {
		return err
	}
	ctx.Response.Header().Set(httpx.ContentType, httpx.JsonContentType)
	ctx.Response.WriteHeader(http.StatusOK)
	_, err = ctx.Response.Write(bs)
	return err
}

func (ctx *Context) InternalUnknownError(err error) error {
	resp := httphelper.Response{
		BaseResp: httphelper.BaseResp{
			Code: http.StatusInternalServerError,
			Msg:  err.Error(),
		},
	}
	bs, err := sonic.Marshal(resp)
	if err != nil {
		return err
	}
	ctx.Response.Header().Set(httpx.ContentType, httpx.JsonContentType)
	ctx.Response.WriteHeader(http.StatusInternalServerError)
	_, err = ctx.Response.Write(bs)
	return err
}

// 动态字段过滤; todo: 嵌套过滤；通过注释，ast 找到定制化序列化方法过滤
func filterSensitiveFields(data interface{}, fields []string) {
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	for _, f := range fields {
		field := v.FieldByName(f)
		if field.IsValid() && field.CanSet() {
			field.Set(reflect.Zero(field.Type()))
		}
	}
}

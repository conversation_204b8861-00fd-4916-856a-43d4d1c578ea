package httpdispatcher

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"strings"

	"github.com/bytedance/sonic/decoder"
	"go.opentelemetry.io/otel/trace"

	"hotel/common/bizerr"
	"hotel/common/log"
	common "hotel/common/protocol"
)

// 全局 ParamMapper 实例
var globalParamMapper = NewParamMapper()

func (s *ServiceDispatcher) parseArgs(c *Context, method reflect.Value) (header common.Header, args []reflect.Value, err error) {
	r := c.Request
	ctx := r.Context()
	header = parseHttpHeaders(r.<PERSON>, r)
	if header.TraceId == "" {
		header.TraceId = log.GetLogidFromContext(r.Context())
	} else {
		// 将自定义TraceID注入OpenTelemetry标准上下文
		sc := trace.SpanContextFromContext(ctx)
		if sc.IsValid() {
			// 使用header中的TraceID替换现有TraceID
			traceID, _ := trace.TraceIDFromHex(header.TraceId)
			ctx = trace.ContextWithSpanContext(ctx, sc.WithTraceID(traceID))
		} else {
			// 创建新的TraceID
			traceID, _ := trace.TraceIDFromHex(header.TraceId)
			spanCtx := trace.NewSpanContext(trace.SpanContextConfig{
				TraceID: traceID,
			})
			ctx = trace.ContextWithSpanContext(ctx, spanCtx)
		}
	}

	// 第一个参数永远是ctx, header注入
	ctx = CtxBindHeader(ctx, header)
	if c.UserInfo != nil {
		ctx = CtxBindUser(ctx, c.UserInfo.LoginUser)
	}
	args = append(args, reflect.ValueOf(ctx))

	// 处理函数参数解析
	if method.Type().NumIn() > 1 {
		// 检查第二个参数是否为结构体类型（包括指针）
		paramType := method.Type().In(1)
		// 处理指针类型，获取实际类型
		actualType := paramType
		if paramType.Kind() == reflect.Ptr {
			actualType = paramType.Elem()
		}

		if actualType.Kind() == reflect.Struct {
			// 原有的结构体参数解析逻辑
			inputPtr := reflect.New(actualType)
			input := inputPtr.Elem()

			if r.Method == http.MethodPost || r.Method == http.MethodPut {
				err = decoder.NewStreamDecoder(r.Body).Decode(inputPtr.Interface())
				if errors.Is(err, io.EOF) { // Body为空
					err = ParseQueryURLString(r.URL.RawQuery, inputPtr.Interface())
				} else if err != nil { // 有效Body但格式错误
					err = bizerr.NewHTTP(bizerr.ParamErr.StatusCode(), fmt.Sprintf("body请求体解析失败: %v", err), http.StatusBadRequest)
				}
			} else if r.Method == http.MethodGet {
				err = ParseQueryURLString(r.URL.RawQuery, inputPtr.Interface())
			}
			if err != nil {
				return header, nil, bizerr.NewHTTP(bizerr.SystemErr.StatusCode(), fmt.Sprintf("请求体解析失败: %v", err), http.StatusBadRequest)
			}

			if input.IsValid() { // !input.IsZero?
				// 通用参数，防止被覆盖，延后解析
				CopyFields(&header, inputPtr.Interface())
				// 注入用户字段从 JWT claims
				if c.UserInfo != nil && c.UserInfo.LoginUser != nil {
					InjectFieldByTag(inputPtr.Interface(), "operator", c.UserInfo.LoginUser)
				}

				if method.Type().NumIn() == 2 && input.Kind() == reflect.Ptr && input.IsNil() {
					return header, nil, bizerr.NewHTTP(bizerr.ParamErr.StatusCode(), "空参数", http.StatusBadRequest)
				}
				// 如果原参数是指针类型，返回指针
				if paramType.Kind() == reflect.Ptr {
					args = append(args, input.Addr())
				} else {
					args = append(args, input)
				}
			}
		} else {
			// 新的基础类型参数解析逻辑（支持注释参数名）
			// 使用全局 ParamMapper 实例

			// 获取所有参数（跳过第一个context参数）
			for i := 1; i < method.Type().NumIn(); i++ {
				paramType := method.Type().In(i)
				paramValue, err := s.parseSingleParamWithMapper(r, paramType, i, method, globalParamMapper)
				if err != nil {
					return header, nil, err
				}
				args = append(args, paramValue)
			}
		}
	}

	return header, args, nil
}

// parseSingleParamWithMapper 使用参数映射器解析单个函数参数
func (s *ServiceDispatcher) parseSingleParamWithMapper(r *http.Request, paramType reflect.Type, paramIndex int, method reflect.Value, paramMapper *ParamMapper) (reflect.Value, error) {
	// 创建参数实例
	paramValue := reflect.New(paramType).Elem()

	// 获取真实的参数名称
	realParamName := paramMapper.GetParamNameByIndex(method, paramIndex-1) // 减1因为跳过了context参数

	// 获取参数映射名称
	paramNames := s.getParamNamesWithRealName(paramType, paramIndex, realParamName)

	// 策略1: 尝试从query参数中解析（使用真实的参数名）
	for _, paramName := range paramNames {
		if err := s.parseParamFromQueryByName(r, paramValue, paramName); err == nil {
			return paramValue, nil
		}
	}

	// 策略2: 尝试从body中解析（如果是结构体类型）
	actualType := paramType
	if paramType.Kind() == reflect.Ptr {
		actualType = paramType.Elem()
	}
	if actualType.Kind() == reflect.Struct {
		if err := s.parseStructFromBody(r, paramValue); err == nil {
			return paramValue, nil
		}
	}

	// 策略3: 尝试从body中解析单个值
	if r.Method == http.MethodPost || r.Method == http.MethodPut {
		if err := s.parseSingleValueFromBody(r, paramValue); err == nil {
			return paramValue, nil
		}
	}

	// 策略4: 尝试从query参数中解析（使用参数索引作为key）
	if err := s.parseParamFromQuery(r, paramValue, paramIndex); err == nil {
		return paramValue, nil
	}

	// 策略5: 尝试从query参数中解析（使用类型名作为key）
	if err := s.parseParamFromQueryByType(r, paramValue, paramType); err == nil {
		return paramValue, nil
	}

	// 如果都失败了，返回零值
	return paramValue, nil
}

// getParamNamesWithRealName 获取参数的可能名称列表（包含真实参数名）
func (s *ServiceDispatcher) getParamNamesWithRealName(paramType reflect.Type, paramIndex int, realParamName string) []string {
	var names []string

	// 1. 使用真实的参数名（最高优先级）
	if realParamName != "" {
		names = append(names, realParamName)
	}

	// 2. 使用类型名（小写）
	typeName := strings.ToLower(paramType.Name())
	if typeName != "" {
		names = append(names, typeName)
	}

	// 3. 使用基本类型名
	basicTypeName := strings.ToLower(paramType.Kind().String())
	if basicTypeName != "" && basicTypeName != typeName {
		names = append(names, basicTypeName)
	}

	// 4. 使用参数索引
	names = append(names, fmt.Sprintf("param%d", paramIndex))

	return names
}

// parseParamFromQueryByName 根据参数名从query参数解析
func (s *ServiceDispatcher) parseParamFromQueryByName(r *http.Request, paramValue reflect.Value, paramName string) error {
	queryParams, err := url.ParseQuery(r.URL.RawQuery)
	if err != nil {
		return err
	}

	// 尝试从query中获取值
	valueStr := queryParams.Get(paramName)
	if valueStr == "" {
		return fmt.Errorf("parameter %s not found in query", paramName)
	}

	// 根据参数类型转换值
	return s.setValueFromString(paramValue, valueStr)
}

// parseParamFromQuery 从query参数解析单个参数
func (s *ServiceDispatcher) parseParamFromQuery(r *http.Request, paramValue reflect.Value, paramIndex int) error {
	queryParams, err := url.ParseQuery(r.URL.RawQuery)
	if err != nil {
		return err
	}

	// 生成参数名（基于索引）
	paramName := fmt.Sprintf("param%d", paramIndex)

	// 尝试从query中获取值
	valueStr := queryParams.Get(paramName)
	if valueStr == "" {
		return fmt.Errorf("parameter %s not found in query", paramName)
	}

	// 根据参数类型转换值
	return s.setValueFromString(paramValue, valueStr)
}

// parseParamFromQueryByType 根据类型名从query参数解析
func (s *ServiceDispatcher) parseParamFromQueryByType(r *http.Request, paramValue reflect.Value, paramType reflect.Type) error {
	queryParams, err := url.ParseQuery(r.URL.RawQuery)
	if err != nil {
		return err
	}

	// 使用类型名作为key
	typeName := strings.ToLower(paramType.Name())
	if typeName == "" {
		// 如果是基本类型，使用类型名
		typeName = strings.ToLower(paramType.Kind().String())
	}

	// 尝试从query中获取值
	valueStr := queryParams.Get(typeName)
	if valueStr == "" {
		return fmt.Errorf("parameter %s not found in query", typeName)
	}

	// 根据参数类型转换值
	return s.setValueFromString(paramValue, valueStr)
}

// parseStructFromBody 从body解析结构体参数
func (s *ServiceDispatcher) parseStructFromBody(r *http.Request, paramValue reflect.Value) error {
	// 直接解析到结构体
	err := decoder.NewStreamDecoder(r.Body).Decode(paramValue.Addr().Interface())
	if err != nil {
		return err
	}
	return nil
}

// parseSingleValueFromBody 从body解析单个值参数
func (s *ServiceDispatcher) parseSingleValueFromBody(r *http.Request, paramValue reflect.Value) error {
	// 创建临时结构体来解析body
	tempStruct := reflect.New(reflect.StructOf([]reflect.StructField{
		{
			Name: "Value",
			Type: paramValue.Type(),
			Tag:  reflect.StructTag(`json:"value"`),
		},
	})).Elem()

	// 尝试解析body
	err := decoder.NewStreamDecoder(r.Body).Decode(tempStruct.Addr().Interface())
	if err != nil {
		return err
	}

	// 获取解析出的值
	value := tempStruct.Field(0)
	paramValue.Set(value)
	return nil
}

// setValueFromString 根据类型将字符串值设置到reflect.Value中
func (s *ServiceDispatcher) setValueFromString(v reflect.Value, str string) error {
	switch v.Kind() {
	case reflect.String:
		v.SetString(str)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		val, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			return fmt.Errorf("failed to parse int: %v", err)
		}
		v.SetInt(val)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		val, err := strconv.ParseUint(str, 10, 64)
		if err != nil {
			return fmt.Errorf("failed to parse uint: %v", err)
		}
		v.SetUint(val)
	case reflect.Float32, reflect.Float64:
		val, err := strconv.ParseFloat(str, 64)
		if err != nil {
			return fmt.Errorf("failed to parse float: %v", err)
		}
		v.SetFloat(val)
	case reflect.Bool:
		val, err := strconv.ParseBool(str)
		if err != nil {
			return fmt.Errorf("failed to parse bool: %v", err)
		}
		v.SetBool(val)
	default:
		return fmt.Errorf("unsupported type: %v", v.Kind())
	}
	return nil
}

func ParseQueryURLString(u string, obj interface{}) error {
	// 从query params中解析对象
	queryParams, err := url.ParseQuery(u)
	if err != nil {
		return err
	}
	return ParseQueryParams(queryParams, obj)
}

// ParseQueryParams 通用函数，将查询参数解析到结构体中
func ParseQueryParams(query url.Values, obj interface{}) error {
	if obj == nil {
		return fmt.Errorf("目标对象不能为nil")
	}

	root := reflect.ValueOf(obj)
	if root.Kind() != reflect.Ptr {
		return fmt.Errorf("必须传入结构体指针，传入类型: %s", root.Kind())
	}

	// 处理多级指针并自动创建实例
	current := root
	for current.Kind() == reflect.Ptr {
		if current.IsNil() {
			// 自动创建指针指向的新实例
			current.Set(reflect.New(current.Type().Elem()))
		}
		current = current.Elem()
	}

	if current.Kind() != reflect.Struct {
		return fmt.Errorf("目标必须是结构体，当前类型: %s", current.Kind())
	}

	typ := current.Type()
	fields := getTypeCacheOrStore(typ, current, "json")

	loadStringValue(current, fields, func(k string) string {
		return query.Get(k)
	})
	return nil
}

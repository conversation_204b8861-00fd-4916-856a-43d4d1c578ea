package httpdispatcher

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/cast"

	"hotel/common/bizerr"
	"hotel/common/log"
	"hotel/common/utils"
	"hotel/user/domain"

	"github.com/golang-jwt/jwt/v5"
)

// context key类型定义，避免魔法字符串

type ctxUserKey struct{}
type ctxJwtClaimsKey struct{}

func CtxBindUser(ctx context.Context, user *domain.User) context.Context {
	return context.WithValue(ctx, ctxUserKey{}, user)
}
func CtxGetUser(ctx context.Context) *domain.User {
	v := ctx.Value(ctxUserKey{})
	if user, ok := v.(*domain.User); ok {
		return user
	}
	return nil
}

func BindJwtClaims(ctx context.Context, claims jwt.Claims) context.Context {
	return context.WithValue(ctx, ctxJwtClaimsKey{}, claims)
}

type JWTConfig struct {
	Secret        string        `json:"secret" yaml:"secret"`                       // 签名密钥
	ExpiresIn     time.Duration `json:"expiresIn" yaml:"expiresIn" default:"7200s"` // 令牌有效期
	SigningMethod string        `json:"signingMethod" yaml:"signingMethod"`         // 签名算法
	Issuer        string        `json:"issuer" yaml:"issuer"`                       // 签发者
	Audience      string        `json:"audience" yaml:"audience"`                   // 接收方
}

func (c *JWTConfig) GetExpireIn() time.Duration {
	return c.ExpiresIn
}

// JWT解析器实现
type JwtPlugin struct {
	config        *JWTConfig
	signingMethod jwt.SigningMethod
	updater       func(ctx context.Context, e *domain.UserEntityConnection) error
}

func NewJWTPlugin(config *JWTConfig) (*JwtPlugin, error) {
	// 初始化签名方法
	method := jwt.GetSigningMethod(config.SigningMethod)
	if method == nil {
		return nil, fmt.Errorf("不支持的签名方法: %s", config.SigningMethod)
	}

	return &JwtPlugin{
		config:        config,
		signingMethod: method,
	}, nil
}

func (p *JwtPlugin) WithUpdater(f func(ctx context.Context, e *domain.UserEntityConnection) error) *JwtPlugin {
	p.updater = f
	return p
}

// Generate 生成Token
func (p *JwtPlugin) Generate(user *domain.User, ttl int64) (string, error) {
	token := jwt.NewWithClaims(p.signingMethod, p.GenerateClaims(user, ttl))
	return token.SignedString([]byte(p.config.Secret))
}

func (p *JwtPlugin) GenerateClaims(user *domain.User, ttl int64) *JWTClaims {
	now := time.Now()
	var expireAt time.Time
	if ttl > 0 {
		expireAt = now.Add(time.Duration(ttl) * time.Second)
	} else {
		expireAt = now.Add(p.config.GetExpireIn()) // 从配置获取有效期

	}

	return &JWTClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    p.config.Issuer,                     // 签发者
			Subject:   "user-auth",                         // 主题
			Audience:  jwt.ClaimStrings{p.config.Audience}, // 接收方
			ExpiresAt: jwt.NewNumericDate(expireAt),        // 过期时间
			NotBefore: jwt.NewNumericDate(now),             // 生效时间（可选）
			IssuedAt:  jwt.NewNumericDate(now),             // 签发时间
			ID:        cast.ToString(user.ID),              // 唯一标识（可选）
		},
		LoginUser: user, // 注入用户信息
	}
}

// 解析验证Token
func (p *JwtPlugin) Parse(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if token.Method.Alg() != p.signingMethod.Alg() {
			return nil, bizerr.AuthenticationErr.WithMessage(fmt.Sprintf("意外的签名方法: %v", token.Header["alg"]))
		}
		return []byte(p.config.Secret), nil
	}, jwt.WithAudience(p.config.Audience), jwt.WithIssuer(p.config.Issuer))

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, bizerr.AuthenticationErr.WithMessage("无效的令牌声明")
}

func (p *JwtPlugin) GetConfig() *JWTConfig {
	return p.config
}

// JWT声明扩展结构
type JWTClaims struct {
	jwt.RegisteredClaims
	LoginUser *domain.User `json:"loginUser"`
}

func (s *ServiceDispatcher) jwtMiddleware(next HandlerFunc) HandlerFunc {
	return func(ctx *Context) error {
		if ctx.Metadata.AuthMethod != "jwt" {
			return next(ctx)
		}

		tokenStr, err := extractBearerToken(ctx.Request)
		if err != nil {
			return bizerr.NewHTTP(0, "invalid token", 401)
		}
		claims, err := s.jwtParser.Parse(tokenStr)
		if err != nil {
			return bizerr.NewHTTP(0, "broken token", 401)
		}

		ctx.UserInfo = claims
		if claims.LoginUser == nil {
			log.Errorc(ctx.Request.Context(), "fatal: login user is nil, token=%s", tokenStr)
			return bizerr.NewHTTP(0, "invalid login user", 401)
		}
		// subscribe entity update!
		if s.jwtParser.updater != nil {
			for _, ce := range claims.LoginUser.UserEntityConnections {
				if err = s.jwtParser.updater(ctx.Request.Context(), ce); err != nil {
					log.Errorc(ctx.Request.Context(), "update user entity connection failed, err=%s", err)
				}
			}
		}

		te := claims.LoginUser.GetTenantBrandEntityOrAbove()
		ces := claims.LoginUser.GetCustomerEntities()
		if te == nil { // customer 登录场景，无 tenant 权限
			if len(ces) != 1 {
				log.Errorc(ctx.Request.Context(), "fatal: unexpected login user %s", utils.ToJSON(claims.LoginUser))
				return next(ctx)
			}

			ce := ces[0]
			ctx.TenantGroup = ce.GetTenantGroupEntity()
			ctx.TenantBrand = ce.GetTenantBrandEntity()
			ctx.Customer = ce
		} else {
			ctx.TenantGroup = te.GetTenantGroupEntity()
			ctx.TenantBrand = te
			if len(ces) == 1 {
				ctx.Customer = ces[0]
			} // 一个 tenant 用户有多个 customer 实体的权限，那么就不标记了
		}

		return next(ctx)
	}
}

package httpdispatcher

import (
	"context"
	"net/http"
	"reflect"
	"strings"

	"github.com/spf13/cast"

	common "hotel/common/protocol"
)

type ctxHeader struct{}

func CtxBindHeader(ctx context.Context, header common.Header) context.Context {
	return context.WithValue(ctx, ctxHeader{}, header)
}
func CtxGetHeader(ctx context.Context) common.Header {
	v, _ := ctx.Value(ctxHeader{}).(common.Header)
	return v
}

func parseHttpHeaders(h http.Header, req *http.Request) common.Header {
	header := &common.Header{}
	headerValue := reflect.ValueOf(header).Elem()
	headerType := headerValue.Type()

	fields := getTypeCacheOrStore(headerType, headerValue, "api.header")
	loadStringValue(headerValue, fields, func(k string) string {
		return h.Get(k)
	})

	// 特殊处理RemoteAddr - 修复IP地址提取逻辑
	if clientIP := h.Get("X-Forwarded-For"); clientIP != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		if commaIdx := strings.Index(clientIP, ","); commaIdx > 0 {
			clientIP = strings.TrimSpace(clientIP[:commaIdx])
		}
		header.ClientOption.IPv4 = clientIP
	} else if req != nil {
		// 从RemoteAddr中提取IP地址（去除端口号）
		remoteAddr := req.RemoteAddr
		if colonIdx := strings.LastIndex(remoteAddr, ":"); colonIdx > 0 {
			remoteAddr = remoteAddr[:colonIdx]
		}
		header.ClientOption.IPv4 = remoteAddr
	}

	return *header
}

func extractHttpHeaders(from interface{}) http.Header {
	if from == nil {
		return nil
	}
	root := reflect.ValueOf(from)
	// 处理多级指针并自动创建实例
	current := root
	for current.Kind() == reflect.Ptr {
		if current.IsNil() {
			return nil
		}
		current = current.Elem()
	}

	if current.Kind() != reflect.Struct {
		return nil
	}

	typ := current.Type()
	fields := getTypeCacheOrStore(typ, current, "api.header")
	headers := http.Header{}
	for _, field := range fields {
		v := current.FieldByIndex(field.fieldIndex)
		if v.IsZero() {
			continue // 0值可能未初始化，需要跳过，避免多个 header 重复
		}
		headers.Add(field.fieldName, cast.ToString(v.Interface()))
	}
	return headers
}

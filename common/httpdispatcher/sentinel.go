package httpdispatcher

import (
	"errors"
	"fmt"

	"github.com/Danceiny/sentinel-golang/api"
	"github.com/Danceiny/sentinel-golang/core/base"
	"github.com/Danceiny/sentinel-golang/core/flow"

	"hotel/common/log"

	"hotel/common/bizerr"
)

var ErrSentinelBlocked = bizerr.RateLimitErr

// sentinelAPIGatewayMiddleware 实现 Sentinel 限流逻辑 (无需修改)
func (s *ServiceDispatcher) sentinelAPIGatewayMiddleware(next HandlerFunc) HandlerFunc {
	return func(c *Context) error {
		resourceName := c.Request.URL.Path
		if !flow.HasRule(resourceName) {
			resourceName = "DEFAULT"
		}
		entry, err := api.Entry(resourceName, api.WithResourceType(base.ResTypeAPIGateway), api.WithTrafficType(base.Inbound))
		if err != nil {
			var blockError *base.BlockError
			if errors.As(err, &blockError) {
				log.Warnc(c.Request.Context(), "Sentinel blocked request for resource: %s, type: %s", resourceName, blockError.BlockType().String())
				return ErrSentinelBlocked
			} else {
				log.Errorc(c.Request.Context(), "Sentinel entry error: %v", err)
				return fmt.Errorf("sentinel internal error: %w", err)
			}
		}
		defer entry.Exit()

		return next(c)
	}
}

func (s *ServiceDispatcher) sentinelWebMiddleware(next HandlerFunc) HandlerFunc {
	return func(c *Context) error {
		if c.UserInfo == nil {
			return next(c)
		}
		tenantEntityId := c.UserInfo.LoginUser.GetTenantBrandEntityOrAbove().GetIDStr()
		customerEntityId := c.UserInfo.LoginUser.GetCustomerEntityOrPlatform().GetIDStr()
		resourceNames := []string{
			"customerEntity:" + customerEntityId + "@" + tenantEntityId + "@" + c.Request.URL.Path,
			"customerEntity:" + customerEntityId + "@" + c.Request.URL.Path,
			"customerEntity:" + customerEntityId,
			"customerEntity",
		}
		for _, resourceName := range resourceNames {
			if !flow.HasRule(resourceName) {
				continue
			}
			entry, err := api.Entry(resourceName, api.WithResourceType(base.ResTypeWeb), api.WithTrafficType(base.Inbound))
			if err != nil {
				var blockError *base.BlockError
				if errors.As(err, &blockError) {
					log.Warnc(c.Request.Context(), "Sentinel blocked request for resource: %s, type: %s", resourceName, blockError.BlockType().String())
					return ErrSentinelBlocked
				} else {
					log.Errorc(c.Request.Context(), "Sentinel entry error: %v", err)
					return fmt.Errorf("sentinel internal error: %w", err)
				}
			}
			defer entry.Exit()
		}
		return next(c)
	}
}

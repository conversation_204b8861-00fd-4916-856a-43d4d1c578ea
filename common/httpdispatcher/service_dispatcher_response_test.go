package httpdispatcher

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	common "hotel/common/protocol"
	"hotel/search/protocol"
)

func Test_Header(t *testing.T) {
	mockey.PatchConvey("", t, func() {
		ctx := context.Background()
		req := &protocol.HotelRatesReq{
			Header: common.Header{
				SessionOption: common.SessionOption{
					SessionId: "session-id",
				},
			},
		}
		t.Log(CtxGetHeader(ctx))
		parsedHeaders := parseHttpHeaders(extractHttpHeaders(req), nil)
		out := &protocol.HotelRatesResp{
			ResponseHeader: common.ResponseHeader{
				TraceOption: common.TraceOption{
					TraceId: "generated trace id",
				},
				SessionOption: common.SessionOption{
					SessionId: parsedHeaders.SessionId,
				},
			},
		}
		ctx = CtxBindHeader(ctx, parseHttpHeaders(extractHttpHeaders(out), nil))
		t.Log(CtxGetHeader(ctx))
		convey.So(CtxGetHeader(ctx).SessionId, convey.ShouldEqual, "session-id")
		convey.So(CtxGetHeader(ctx).TraceId, convey.ShouldEqual, "generated trace id")
	})
}

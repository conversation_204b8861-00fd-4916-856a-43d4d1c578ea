package httpdispatcher

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"reflect"
	"runtime"
	"strings"
	"sync"
)

// 全局缓存
var (
	globalParamCache = make(map[string][]string)
	globalCacheMutex sync.RWMutex
)

// ParamMapper 参数映射器
type ParamMapper struct {
	// 移除实例级别的缓存，使用全局缓存
}

// NewParamMapper 创建新的参数映射器
func NewParamMapper() *ParamMapper {
	return &ParamMapper{}
}

// GetParamNames 获取函数的参数名称列表
func (pm *ParamMapper) GetParamNames(method reflect.Value) []string {
	// 生成函数签名
	signature := pm.generateSignature(method)

	// 检查全局缓存
	globalCacheMutex.RLock()
	if names, ok := globalParamCache[signature]; ok {
		globalCacheMutex.RUnlock()
		return names
	}
	globalCacheMutex.RUnlock()

	// 从AST解析参数名（优先注释@param）
	names := pm.parseParamNamesFromASTWithComment(method)

	// 缓存结果到全局缓存
	globalCacheMutex.Lock()
	globalParamCache[signature] = names
	globalCacheMutex.Unlock()

	return names
}

// generateSignature 生成函数签名
func (pm *ParamMapper) generateSignature(method reflect.Value) string {
	methodType := method.Type()
	var signature strings.Builder

	// 添加参数类型
	signature.WriteString("(")
	for i := 0; i < methodType.NumIn(); i++ {
		if i > 0 {
			signature.WriteString(",")
		}
		signature.WriteString(methodType.In(i).String())
	}
	signature.WriteString(")")

	return signature.String()
}

// parseParamNamesFromASTWithComment 优先解析 @param 注释参数名
func (pm *ParamMapper) parseParamNamesFromASTWithComment(method reflect.Value) []string {
	funcPtr := method.Pointer()
	if funcPtr == 0 {
		return pm.generateDefaultParamNames(method)
	}
	fn := runtime.FuncForPC(funcPtr)
	if fn == nil {
		return pm.generateDefaultParamNames(method)
	}
	file, line := fn.FileLine(funcPtr)
	if file == "<autogenerated>" {
		return pm.generateDefaultParamNames(method)
	}
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, file, nil, parser.ParseComments)
	if err != nil {
		return pm.generateDefaultParamNames(method)
	}
	var paramNames []string
	ast.Inspect(node, func(n ast.Node) bool {
		funcDecl, ok := n.(*ast.FuncDecl)
		if !ok {
			return true
		}
		if fset.Position(funcDecl.Pos()).Line != line {
			return true
		}
		// 解析注释中的 @param
		commentParamNames := parseParamNamesFromComment(funcDecl.Doc)
		if len(commentParamNames) > 0 {
			paramNames = commentParamNames
		} else {
			// 没有注释时 fallback 到变量名
			if funcDecl.Type.Params != nil {
				for _, field := range funcDecl.Type.Params.List {
					if len(paramNames) == 0 && pm.isContextType(field.Type) {
						continue
					}
					if len(field.Names) > 0 {
						paramNames = append(paramNames, field.Names[0].Name)
					} else {
						paramNames = append(paramNames, fmt.Sprintf("param%d", len(paramNames)+1))
					}
				}
			}
		}
		return false
	})
	if len(paramNames) == 0 {
		return pm.generateDefaultParamNames(method)
	}
	return paramNames
}

// parseParamNamesFromComment 解析 @param 注释参数名
func parseParamNamesFromComment(doc *ast.CommentGroup) []string {
	if doc == nil {
		return nil
	}
	var names []string
	for _, comment := range doc.List {
		text := strings.TrimSpace(strings.TrimPrefix(comment.Text, "//"))
		if strings.HasPrefix(text, "@param") {
			// 支持格式: @param user_id int64 "desc"
			parts := strings.Fields(text)
			if len(parts) >= 3 {
				names = append(names, parts[1])
			}
		}
	}
	return names
}

// isContextType 检查是否为context类型
func (pm *ParamMapper) isContextType(expr ast.Expr) bool {
	switch t := expr.(type) {
	case *ast.SelectorExpr:
		return t.Sel.Name == "Context"
	case *ast.Ident:
		return t.Name == "Context"
	default:
		return false
	}
}

// generateDefaultParamNames 生成默认参数名称
func (pm *ParamMapper) generateDefaultParamNames(method reflect.Value) []string {
	methodType := method.Type()
	var names []string

	for i := 0; i < methodType.NumIn(); i++ {
		// 跳过第一个参数（通常是context）
		if i == 0 {
			continue
		}

		// 生成默认参数名
		paramType := methodType.In(i)
		names = append(names, pm.generateParamName(paramType, i))
	}

	return names
}

// generateParamName 根据类型生成参数名
func (pm *ParamMapper) generateParamName(paramType reflect.Type, index int) string {
	// 尝试使用类型名
	typeName := paramType.Name()
	if typeName != "" {
		return strings.ToLower(typeName)
	}

	// 使用基本类型名
	kindName := strings.ToLower(paramType.Kind().String())
	if kindName != "" {
		return kindName
	}

	// 使用索引
	return fmt.Sprintf("param%d", index)
}

// GetParamNameByIndex 根据索引获取参数名
func (pm *ParamMapper) GetParamNameByIndex(method reflect.Value, index int) string {
	names := pm.GetParamNames(method)
	if index >= 0 && index < len(names) {
		return names[index]
	}
	return fmt.Sprintf("param%d", index+1)
}

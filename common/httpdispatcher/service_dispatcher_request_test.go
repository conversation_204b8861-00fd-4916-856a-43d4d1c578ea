package httpdispatcher

import (
	"context"
	"net/http"
	"reflect"
	"testing"
)

// 测试用的服务结构体
type TestService struct{}

// 测试函数：func(ctx context.Context, userID int64, name string) error
func (s *TestService) TestMethod1(ctx context.Context, userID int64, name string) error {
	return nil
}

// 测试函数：func(ctx context.Context, orderID int64, enabled bool) error
func (s *TestService) TestMethod2(ctx context.Context, orderID int64, enabled bool) error {
	return nil
}

// 测试函数：func(ctx context.Context, hotelID int64) error
func (s *TestService) TestMethod3(ctx context.Context, hotelID int64) error {
	return nil
}

func TestParseArgs(t *testing.T) {
	dispatcher := &ServiceDispatcher{}
	service := &TestService{}

	tests := []struct {
		name           string
		methodName     string
		queryParams    string
		expectedUserID int64
		expectedName   string
	}{
		{
			name:           "使用真实参数名解析",
			methodName:     "TestMethod1",
			queryParams:    "userID=123&name=test",
			expectedUserID: 123,
			expectedName:   "test",
		},
		{
			name:           "使用类型名解析",
			methodName:     "TestMethod1",
			queryParams:    "int64=456&string=test2",
			expectedUserID: 456,
			expectedName:   "test2",
		},
		{
			name:           "使用参数索引解析",
			methodName:     "TestMethod1",
			queryParams:    "param1=789&param2=test3",
			expectedUserID: 789,
			expectedName:   "test3",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 获取方法
			methodValue := reflect.ValueOf(service).MethodByName(tt.methodName)
			if !methodValue.IsValid() {
				t.Fatalf("方法 %s 不存在", tt.methodName)
			}

			// 创建请求
			req, err := http.NewRequest("GET", "/test?"+tt.queryParams, nil)
			if err != nil {
				t.Fatalf("创建请求失败: %v", err)
			}

			// 创建上下文
			ctx := &Context{
				Request: req,
			}

			// 解析参数
			_, args, err := dispatcher.parseArgs(ctx, methodValue)
			if err != nil {
				t.Fatalf("解析参数失败: %v", err)
			}

			// 验证参数数量
			if len(args) != 3 { // context + userID + name
				t.Fatalf("期望3个参数，实际得到%d个", len(args))
			}

			// 验证userID参数
			userID := args[1].Int()
			if userID != tt.expectedUserID {
				t.Errorf("期望userID=%d，实际得到%d", tt.expectedUserID, userID)
			}

			// 验证name参数
			name := args[2].String()
			if name != tt.expectedName {
				t.Errorf("期望name=%s，实际得到%s", tt.expectedName, name)
			}
		})
	}
}

func TestParamMapper(t *testing.T) {
	service := &TestService{}
	mapper := NewParamMapper()

	tests := []struct {
		methodName string
		expected   []string
	}{
		{
			methodName: "TestMethod1",
			expected:   []string{"userID", "name"},
		},
		{
			methodName: "TestMethod2",
			expected:   []string{"orderID", "enabled"},
		},
		{
			methodName: "TestMethod3",
			expected:   []string{"hotelID"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.methodName, func(t *testing.T) {
			methodValue := reflect.ValueOf(service).MethodByName(tt.methodName)
			if !methodValue.IsValid() {
				t.Fatalf("方法 %s 不存在", tt.methodName)
			}

			names := mapper.GetParamNames(methodValue)
			if len(names) != len(tt.expected) {
				t.Errorf("期望%d个参数名，实际得到%d个", len(tt.expected), len(names))
				return
			}

			for i, expected := range tt.expected {
				if names[i] != expected {
					t.Errorf("参数%d: 期望%s，实际得到%s", i, expected, names[i])
				}
			}
		})
	}
}

func TestGetParamNamesWithRealName(t *testing.T) {
	dispatcher := &ServiceDispatcher{}

	tests := []struct {
		name          string
		paramType     reflect.Type
		paramIndex    int
		realParamName string
		expectedNames []string
	}{
		{
			name:          "用户ID参数",
			paramType:     reflect.TypeOf(int64(0)),
			paramIndex:    1,
			realParamName: "userID",
			expectedNames: []string{"userID", "int64", "param1"},
		},
		{
			name:          "名称参数",
			paramType:     reflect.TypeOf(""),
			paramIndex:    2,
			realParamName: "name",
			expectedNames: []string{"name", "string", "param2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			names := dispatcher.getParamNamesWithRealName(tt.paramType, tt.paramIndex, tt.realParamName)

			// 验证真实参数名在第一位
			if len(names) == 0 || names[0] != tt.realParamName {
				t.Errorf("期望真实参数名%s在第一位，实际得到%s", tt.realParamName, names[0])
			}

			// 验证包含所有期望的名称
			for _, expected := range tt.expectedNames {
				found := false
				for _, name := range names {
					if name == expected {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("期望包含参数名%s，但未找到", expected)
				}
			}
		})
	}
}

func TestSetValueFromString(t *testing.T) {
	dispatcher := &ServiceDispatcher{}

	tests := []struct {
		name     string
		value    interface{}
		str      string
		expected interface{}
		hasError bool
	}{
		{
			name:     "解析int64",
			value:    int64(0),
			str:      "123",
			expected: int64(123),
			hasError: false,
		},
		{
			name:     "解析string",
			value:    "",
			str:      "test",
			expected: "test",
			hasError: false,
		},
		{
			name:     "解析bool",
			value:    false,
			str:      "true",
			expected: true,
			hasError: false,
		},
		{
			name:     "解析无效int",
			value:    int64(0),
			str:      "invalid",
			expected: int64(0),
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			value := reflect.New(reflect.TypeOf(tt.value)).Elem()
			err := dispatcher.setValueFromString(value, tt.str)

			if tt.hasError {
				if err == nil {
					t.Errorf("期望错误，但没有得到错误")
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误，但得到错误: %v", err)
				return
			}

			actual := value.Interface()
			if actual != tt.expected {
				t.Errorf("期望%s，实际得到%s", tt.expected, actual)
			}
		})
	}
}

package httpdispatcher

import (
	"fmt"
	"net/http"
	"reflect"
	"runtime/debug"
	"time"

	"hotel/common/log"

	"hotel/build/api/asthelper"
	"hotel/common/bizerr"
	common "hotel/common/protocol"
	"hotel/user/domain"

	"github.com/zeromicro/go-zero/core/logx"
)

// ServiceDispatcher 服务调度器结构体
type ServiceDispatcher struct {
	prefix    string
	apis      map[string]*asthelper.MethodMeta
	jwtParser *JwtPlugin
}

func (s *ServiceDispatcher) WithJwt(jwtParser *JwtPlugin) *ServiceDispatcher {
	s.jwtParser = jwtParser
	return s
}

func NewServiceDispatcherBuilder() *ServiceDispatcher {
	return &ServiceDispatcher{}
}

func (s *ServiceDispatcher) Build() (*ServiceDispatcher, error) {
	if s.prefix == "" {
		s.prefix = "/api"
	}
	s.apis = make(map[string]*asthelper.MethodMeta)
	return s, nil
}

type Service interface {
	Name() string
}

type ParentService interface {
	GetChildren() []Service
}

// RegisterService 注册服务到调度器
func (s *ServiceDispatcher) RegisterService(service Service) {
	serviceName := service.Name()
	vs := s.analyzeService(serviceName, service)
	for k, v := range vs {
		s.apis[k] = v
	}
	if v, ok := service.(ParentService); ok {
		for _, c := range v.GetChildren() {
			s.RegisterService(c)
		}
	}
}

// 深度分析服务结构
func (s *ServiceDispatcher) analyzeService(srvName string, service interface{}) map[string]*asthelper.MethodMeta {
	t := reflect.TypeOf(service)
	serviceValue := reflect.ValueOf(service)
	cache := make(map[string]*asthelper.MethodMeta)
	for i := 0; i < serviceValue.NumMethod(); i++ {
		method := t.Method(i)

		// 获取方法AST注释（需结合runtime信息）
		file, line := asthelper.GetMethodSourceLocation(method)
		var comments []string
		var meta *asthelper.MethodMeta

		if file == "<autogenerated>" {
			log.Info("getMethodSourceLocation %s %s %s %v", srvName, method.Name, file, line)
			// 对于 autogenerated 的方法，使用默认的 API 路径
			meta = &asthelper.MethodMeta{}
		} else {
			comments = asthelper.ExtractMethodCommentsByFineAndLine(file, line)
			meta = asthelper.ParseMethodComment(comments)
		}

		methodAPIPath := asthelper.GetMethodAPIPath(srvName, method.Name, meta.Path)

		k := s.prefix + "/" + srvName + "/" + methodAPIPath
		outputType, err := getOutputType(method)
		if err != nil {
			logx.Errorf("register api: %s failed due to output parameters， method: %s", k, method.Name)
			continue
		}

		meta.ServiceName = srvName
		meta.MethodName = method.Name
		meta.Method = method
		meta.MethodValue = serviceValue.Method(i) // ✅ 已绑定接收器
		meta.InputType = getInputType(method)
		meta.OutputType = outputType

		log.Info("register api(%s) service(%s) method(%s)", k, meta.ServiceName, meta.MethodName)
		cache[k] = meta
	}
	return cache
}

// 获取输入参数类型
func getInputType(method reflect.Method) reflect.Type {
	if method.Type.NumIn() < 2 {
		return nil
	}
	return method.Type.In(1)
}

// 判断类型是否为error接口
func isErrorType(t reflect.Type) bool {
	if t == nil {
		return false
	}
	return t.Implements(errorType)
}

// 预定义error接口反射类型
var errorType = reflect.TypeOf((*error)(nil)).Elem()

// 增强版输出类型判断
func getOutputType(method reflect.Method) (outputType reflect.Type, err error) {
	numOut := method.Type.NumOut()

	switch numOut {
	case 1:
		if isErrorType(method.Type.Out(0)) {
			return nil, nil
		}
		return method.Type.Out(0), nil

	case 2:
		if !isErrorType(method.Type.Out(1)) {
			return nil, fmt.Errorf("方法 %s 的第二个返回值必须为error类型", method.Name)
		}
		return method.Type.Out(0), nil
	default:
		if numOut > 2 {
			return nil, fmt.Errorf("方法 %s 的返回值数量超过2个", method.Name)
		}
		return nil, nil
	}
}

type HandlerFunc func(*Context) error
type Middleware func(HandlerFunc) HandlerFunc

type RouteInfo struct {
	Service string
	Method  string
}
type Context struct {
	RouteInfo    RouteInfo
	Request      *http.Request
	Response     http.ResponseWriter
	Metadata     *asthelper.MethodMeta
	Input        interface{}
	InputHeaders common.Header
	Output       interface{}
	Errors       []error

	UserInfo    *JWTClaims // JWT解析结果
	TenantGroup *domain.Entity
	TenantBrand *domain.Entity
	Customer    *domain.Entity

	Start time.Time
	End   time.Time
}

// 确保 recovery 是第一个中间件
func (s *ServiceDispatcher) buildMiddlewareChain(ctx *Context, mw ...Middleware) HandlerFunc {
	chain := func(c *Context) error { return nil }
	for i := len(mw) - 1; i >= 0; i-- {
		chain = mw[i](chain)
	}
	// 在最外层包裹 recovery
	return s.recoveryMiddleware(chain)
}

// Dispatch 处理请求并调度到相应的服务方法
func (s *ServiceDispatcher) routeHandler(next HandlerFunc) HandlerFunc {
	return func(ctx *Context) error {
		k := ctx.Request.URL.Path
		// 从缓存获取方法元数据
		methodMeta := s.apis[k]
		if methodMeta == nil {
			return bizerr.NewHTTP(0, fmt.Sprintf("path(%s) not found", k), 404)
		}
		ctx.RouteInfo = RouteInfo{
			Service: methodMeta.ServiceName,
			Method:  methodMeta.MethodName,
		}
		ctx.Metadata = methodMeta
		return next(ctx)
	}
}
func (s *ServiceDispatcher) coreHandler(next HandlerFunc) HandlerFunc {
	return func(ctx *Context) error {
		methodMeta := ctx.Metadata

		// 构建调用参数
		headers, args, err := s.parseArgs(ctx, methodMeta.MethodValue)
		if err != nil {
			return err
		}
		ctx.Input = args
		ctx.InputHeaders = headers

		// 反射调用方法
		results := methodMeta.MethodValue.Call(args)

		// 处理返回值
		output, err := s.parseResp(results)
		// 处理错误
		if err != nil {
			return err
		}
		// 存储输出到上下文
		ctx.Output = output
		return next(ctx)
	}
}
func (s *ServiceDispatcher) parseResp(results []reflect.Value) (out interface{}, err error) {
	switch len(results) {
	case 1:
		if errVal := results[0]; !errVal.IsNil() {
			return nil, errVal.Interface().(error)
		}
	case 2:
		out = results[0].Interface()
		if errVal := results[1]; !errVal.IsNil() {
			err = errVal.Interface().(error)
		}
		return out, err
	}
	return nil, nil
}

// recoveryMiddleware.go
func (s *ServiceDispatcher) recoveryMiddleware(next HandlerFunc) HandlerFunc {
	return func(ctx *Context) (err error) {
		defer func() {
			if r := recover(); r != nil {
				// 记录堆栈信息（生产环境建议异步记录）
				stack := debug.Stack()
				// 捕获 panic 并转换为 error
				err = fmt.Errorf("internal server panic: %v stack: %v", r, string(debug.Stack()))
				log.Errorc(ctx.Request.Context(), "[PANIC] %s route(%v)\n%s", r, ctx.RouteInfo, string(stack))
			}
		}()

		// 执行后续中间件链
		return next(ctx)
	}
}

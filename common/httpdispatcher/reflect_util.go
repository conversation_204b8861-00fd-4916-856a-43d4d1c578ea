package httpdispatcher

import (
	"reflect"
	"strings"
	"sync"

	"github.com/spf13/cast"
)

// 添加全局缓存
var (
	typeCache    = make(map[reflect.Type]map[string][]reflectFieldInfo)
	typeCacheMux sync.RWMutex
)

func getTypeCacheOrStore(t reflect.Type, tv reflect.Value, tagName string) []reflectFieldInfo {
	typeCacheMux.RLock()
	v := typeCache[t][tagName]
	typeCacheMux.RUnlock()
	if len(v) > 0 {
		return v
	}
	// 首次访问，构建缓存
	fields := make([]reflectFieldInfo, 0, tv.NumField())
	var processFields func(reflect.Type, []int)
	processFields = func(typ reflect.Type, parentIndex []int) {
		for i := 0; i < typ.NumField(); i++ {
			field := typ.Field(i)
			// 处理匿名结构体字段
			if field.Anonymous && field.Type.Kind() == reflect.Struct {
				processFields(field.Type, append(parentIndex, i))
				continue
			}
			tag := strings.Split(field.Tag.Get(tagName), ",")[0]
			if tag == "" || tag == "-" {
				continue
			}
			currentIndex := append(parentIndex, i)
			fields = append(fields, reflectFieldInfo{
				fieldIndex: currentIndex,
				fieldName:  tag,
				fieldType:  field.Type.Kind(),
			})
		}
	}

	processFields(t, []int{})

	typeCacheMux.Lock()
	if typeCache[t] == nil {
		typeCache[t] = make(map[string][]reflectFieldInfo)
	}
	typeCache[t][tagName] = fields
	typeCacheMux.Unlock()
	return fields
}

type reflectFieldInfo struct {
	fieldIndex []int
	fieldName  string
	fieldType  reflect.Kind
}

func CopyFields(from interface{}, to interface{}) {
	fromVal := reflect.ValueOf(from).Elem()
	toVal := reflect.ValueOf(to).Elem()
	// 处理指针类型
	if fromVal.Kind() == reflect.Ptr {
		fromVal = fromVal.Elem()
	}
	if toVal.Kind() == reflect.Ptr {
		toVal = toVal.Elem()
	}
	if fromVal.Kind() != reflect.Struct || toVal.Kind() != reflect.Struct {
		return
	}

	fromFields := getTypeCacheOrStore(fromVal.Type(), fromVal, "json")
	toFields := getTypeCacheOrStore(toVal.Type(), toVal, "json")

	toFieldMap := make(map[string]reflectFieldInfo)
	for _, field := range toFields {
		toFieldMap[field.fieldName] = field
	}
	for _, fromField := range fromFields {
		if toField, ok := toFieldMap[fromField.fieldName]; ok {
			fromFieldVal := fromVal.FieldByIndex(fromField.fieldIndex)
			toFieldVal := toVal.FieldByIndex(toField.fieldIndex)

			if fromFieldVal.Type() == toFieldVal.Type() {
				toFieldVal.Set(fromFieldVal)
			}
		}
	}
}

func loadStringValue(h reflect.Value, fields []reflectFieldInfo, read func(k string) string) {
	// 使用缓存快速处理
	for _, info := range fields {
		hv := read(info.fieldName)
		if hv == "" {
			continue
		}

		fieldValue := h.FieldByIndex(info.fieldIndex)
		// 根据字段类型设置值
		switch info.fieldType {
		case reflect.String:
			fieldValue.SetString(hv)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			fieldValue.SetInt(cast.ToInt64(hv))
		case reflect.Bool:
			fieldValue.SetBool(cast.ToBool(hv))
		case reflect.Float64, reflect.Float32:
			fieldValue.SetFloat(cast.ToFloat64(hv))
		default:
		}
	}
}

// InjectFieldByTag 通用字段注入方法，根据指定 tag 注入字段值
func InjectFieldByTag(inputPtr interface{}, tagName string, value interface{}) {
	inputValue := reflect.ValueOf(inputPtr)
	if inputValue.Kind() != reflect.Ptr {
		return
	}

	inputElem := inputValue.Elem()
	if inputElem.Kind() != reflect.Struct {
		return
	}

	// 使用缓存获取字段信息
	typ := inputElem.Type()
	fields := getTypeCacheOrStore(typ, inputElem, "json")

	// 查找指定的字段（json tag）
	for _, field := range fields {
		if field.fieldName == tagName {
			targetField := inputElem.FieldByIndex(field.fieldIndex)
			if targetField.IsValid() && targetField.CanSet() {
				valueReflect := reflect.ValueOf(value)
				// 检查类型是否匹配
				if valueReflect.Type().AssignableTo(targetField.Type()) {
					// 设置字段值
					targetField.Set(valueReflect)
					return
				}
			}
		}
	}
}

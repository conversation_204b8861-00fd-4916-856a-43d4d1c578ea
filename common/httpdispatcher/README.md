# HTTP Dispatcher 参数解析功能

## 概述

HTTP Dispatcher 现在支持智能的函数参数解析，可以根据函数签名自动解析 HTTP 请求中的参数。

## 功能特性

### 1. 智能参数名称映射

支持多种参数名称解析策略：

- **注释参数名**：使用 `@param` 注释指定的参数名（最高优先级）
- **真实参数名**：使用函数定义中的实际参数名
- **类型名**：使用参数类型的名称
- **参数索引**：使用 `param1`, `param2` 等索引名称

### 2. 多种数据源支持

- **Query 参数**：从 URL 查询字符串解析
- **Body 参数**：从请求体解析（支持 JSON）
- **结构体解析**：自动处理结构体类型的参数

### 3. 类型转换

支持以下类型的自动转换：

- `int`, `int8`, `int16`, `int32`, `int64`
- `uint`, `uint8`, `uint16`, `uint32`, `uint64`
- `float32`, `float64`
- `string`
- `bool`
- 结构体类型

## 使用示例

### 基本用法

```go
type UserService struct{}

// @param user_id int64 "用户ID"
// @param name string "用户名"
func (s *UserService) GetUser(ctx context.Context, userID int64, name string) error {
    // 参数会自动从以下来源解析：
    // 1. Query: ?user_id=123&name=test (使用注释参数名)
    // 2. Query: ?userID=123&name=test (使用变量名)
    // 3. Query: ?int64=123&string=test (使用类型名)
    // 4. Query: ?param1=123&param2=test (使用参数索引)
    // 5. Body: {"user_id": 123, "name": "test"}
    return nil
}
```

### 支持的请求格式

#### 1. Query 参数（推荐）

```bash
# 使用注释参数名（最高优先级）
GET /api/user?user_id=123&name=test

# 使用变量名
GET /api/user?userID=123&name=test

# 使用类型名
GET /api/user?int64=123&string=test

# 使用参数索引
GET /api/user?param1=123&param2=test
```

#### 2. Body 参数

```bash
# JSON 格式
POST /api/user
Content-Type: application/json

{
    "user_id": 123,
    "name": "test"
}
```

#### 3. 混合模式

```bash
# Query + Body
POST /api/user?user_id=123
Content-Type: application/json

{
    "name": "test"
}
```

### 参数名称映射规则

#### 1. 注释参数名（最高优先级）

使用 `@param` 注释指定参数名：

```go
// @param user_id int64 "用户ID"
// @param name string "用户名"
func (s *Service) Method(ctx context.Context, userID int64, name string) error
// HTTP 参数名：user_id, name
```

#### 2. 变量名（第二优先级）

函数定义中的参数名：

```go
func (s *Service) Method(ctx context.Context, userID int64, name string) error
// HTTP 参数名：userID, name
```

#### 3. 类型名（第三优先级）

根据参数类型自动映射：

```go
int64   -> "int64"
string  -> "string"
bool    -> "bool"
```

#### 4. 参数索引（最后优先级）

使用 `param1`, `param2` 等索引名称。

### 解析策略

系统会按以下顺序尝试解析参数：

1. **Query 参数（注释参数名）**：`?user_id=123`
2. **Query 参数（变量名）**：`?userID=123`
3. **Query 参数（类型名）**：`?int64=123`
4. **Query 参数（索引）**：`?param1=123`
5. **Body 结构体解析**：`{"user_id": 123}`
6. **Body 单个值解析**：`{"value": 123}`

### 错误处理

- 如果参数解析失败，会返回零值而不是错误
- 支持参数验证和错误返回
- 提供详细的错误信息

### 性能优化

- 使用缓存避免重复的 AST 解析
- 参数名称映射缓存
- 类型转换优化

## 最佳实践

### 1. 参数命名

- 使用 `@param` 注释明确指定 HTTP 参数名
- 使用有意义的变量名
- 遵循 Go 命名约定

### 2. 类型选择

- 优先使用基本类型
- 对于复杂数据，使用结构体
- 避免使用指针类型（除非必要）

### 3. 错误处理

- 在服务方法中进行参数验证
- 返回有意义的错误信息
- 使用适当的 HTTP 状态码

### 4. 性能考虑

- 对于高频调用，考虑缓存参数映射
- 避免在热路径中进行复杂的 AST 解析
- 使用适当的参数类型

## 测试

运行测试：

```bash
go test ./common/httpdispatcher -gcflags="all=-N -l" -v
```

测试覆盖了以下场景：

- 注释参数名解析
- 变量名解析
- 类型转换
- 多种数据源
- 错误处理
- 边界情况

## 注意事项

1. **AST 解析限制**：AST 解析需要关闭编译优化（`-gcflags="all=-N -l"`）
2. **性能开销**：首次解析会有一定的性能开销
3. **类型安全**：确保参数类型匹配，避免运行时错误
4. **向后兼容**：新功能不会影响现有的结构体参数解析
5. **注释格式**：`@param` 注释必须按参数顺序编写 
package envhelper

import "os"

type ENV = string

const (
	ENV_DEV = "DEV" // 本地或远程开发环境
	ENV_UAT = "UAT" // 标准测试环境；支持客户连接该测试环境，尽力保障可用性
	ENV_PRE = "PRE" // 预发布环境，数据库直连线上
	ENV_PRD = "PRD" // 生产环境
)

func IsDev() bool {
	return GetENV() == ENV_DEV
}

func IsUAT() bool {
	return GetENV() == ENV_UAT
}

func IsPre() bool {
	return GetENV() == ENV_PRE
}

func IsPrd() bool {
	return GetENV() == ENV_PRD
}

func GetENV() ENV {
	v := os.Getenv("ENV")
	if v != "" {
		return v
	}
	return ENV_DEV
}

func GetServiceName() string {
	if v := os.Getenv("SERVICE_NAME"); v != "" {
		return v
	}
	return "hotel-be"
}

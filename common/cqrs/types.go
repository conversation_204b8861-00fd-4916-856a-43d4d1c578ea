package cqrs

import (
	"context"
	"time"
)

// Producer 消息生产者接口
type Producer interface {
	// Publish 发布消息到指定主题 (支持任意类型，内部序列化)
	Publish(ctx context.Context, topic string, message interface{}) error

	// PublishWithOptions 带选项发布消息 (支持任意类型，内部序列化)
	PublishWithOptions(ctx context.Context, topic string, message interface{}, opts *PublishOptions) error

	// PublishRaw 发布原始字节消息 (不序列化)
	PublishRaw(ctx context.Context, topic string, message []byte) error

	// PublishRawWithOptions 带选项发布原始字节消息 (不序列化)
	PublishRawWithOptions(ctx context.Context, topic string, message []byte, opts *PublishOptions) error

	// Close 关闭生产者
	Close() error
}

// Consumer 消息消费者接口
type Consumer interface {
	// Start 开始消费消息
	Start() error

	// Stop 停止消费者
	Stop()

	// IsRunning 检查消费者是否正在运行
	IsRunning() bool
}

// Config 消息队列配置
type Config struct {
	Type  string      `yaml:"type" json:"type"` // "redis_stream" 或 "nsq"
	Redis RedisConfig `yaml:"redis" json:"redis"`
	NSQ   NSQConfig   `yaml:"nsq" json:"nsq"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `yaml:"host" json:"host"`
	Port         int    `yaml:"port" json:"port"`
	Password     string `yaml:"password" json:"password"`
	DB           int    `yaml:"db" json:"db"`
	PoolSize     int    `yaml:"pool_size" json:"pool_size"`
	MinIdleConns int    `yaml:"min_idle_conns" json:"min_idle_conns"`
}

// NSQConfig NSQ配置
type NSQConfig struct {
	NSQDAddress      string        `yaml:"nsqd_address" json:"nsqd_address"`
	LookupdAddresses []string      `yaml:"lookupd_addresses" json:"lookupd_addresses"`
	MaxInFlight      int           `yaml:"max_in_flight" json:"max_in_flight"`
	MaxAttempts      uint16        `yaml:"max_attempts" json:"max_attempts"`
	RequeueDelay     time.Duration `yaml:"requeue_delay" json:"requeue_delay"`
}

// ProducerConfig 生产者配置
type ProducerConfig struct {
	MaxLen           int64    `yaml:"max_len" json:"max_len"`                     // Redis Stream最大长度
	NSQDAddress      string   `yaml:"nsqd_address" json:"nsqd_address"`           // NSQ守护进程地址
	LookupdAddresses []string `yaml:"lookupd_addresses" json:"lookupd_addresses"` // NSQ查找服务地址列表
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	TopicName       string        `yaml:"topic_name" json:"topic_name"`               // 主题名称
	ChannelName     string        `yaml:"channel_name" json:"channel_name"`           // 通道名称（NSQ）或消费者组名称（Redis Stream）
	ConsumerName    string        `yaml:"consumer_name" json:"consumer_name"`         // 消费者名称
	MaxInFlight     int           `yaml:"max_in_flight" json:"max_in_flight"`         // 最大处理中的消息数
	MaxAttempts     uint16        `yaml:"max_attempts" json:"max_attempts"`           // 最大重试次数
	RetryDelay      time.Duration `yaml:"retry_delay" json:"retry_delay"`             // 重试延迟
	DeadLetterTopic string        `yaml:"dead_letter_topic" json:"dead_letter_topic"` // 死信队列主题名称
	BlockTime       time.Duration `yaml:"block_time" json:"block_time"`               // 阻塞读取时间（Redis Stream）
	BatchSize       int64         `yaml:"batch_size" json:"batch_size"`               // 批量读取消息数量
}

// PublishOptions 发布选项
type PublishOptions struct {
	Delay    time.Duration     `json:"delay"`    // 延迟发送时间
	Priority int               `json:"priority"` // 消息优先级
	Headers  map[string]string `json:"headers"`  // 消息头
	TTL      time.Duration     `json:"ttl"`      // 消息生存时间
}

// MessageHandler 消息处理器函数类型
type MessageHandler func(message []byte) error

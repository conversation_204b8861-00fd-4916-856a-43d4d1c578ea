package dispatch

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

type testItem struct {
	val        string
	groupByVal string
}

func (t testItem) GetUniqueID() string {
	return t.val
}

func (t testItem) GroupBy() string {
	return t.groupByVal
}
func TestChunk(t *testing.T) {
	mockey.PatchConvey("TestChunk", t, func() {
		mockey.PatchConvey("empty slice", func() {
			chunks := Chunk([]Item{}, 10)
			So(chunks, ShouldHaveLength, 0)
		})

		mockey.PatchConvey("chunk size 1", func() {
			chunks := Chunk([]Item{testItem{val: "1"}, testItem{val: "2"}, testItem{val: "3"}}, 1)
			So(chunks, ShouldHaveLength, 3)
			So(chunks[0], ShouldHaveLength, 1)
			So(chunks[1], ShouldHaveLength, 1)
			So(chunks[2], ShouldHaveLength, 1)
		})

		mockey.PatchConvey("chunk size 2", func() {
			chunks := Chunk([]Item{testItem{val: "1"}, testItem{val: "2"}, testItem{val: "3"}}, 2)
			So(chunks, ShouldHaveLength, 2)
			So(chunks[0], ShouldHaveLength, 2)
			So(chunks[1], ShouldHaveLength, 1)
		})

		mockey.PatchConvey("chunk size 3", func() {
			chunks := Chunk([]Item{testItem{val: "1"}, testItem{val: "2"}, testItem{val: "3"}}, 3)
			So(chunks, ShouldHaveLength, 1)
			So(chunks[0], ShouldHaveLength, 3)
		})
	})
}

func TestDispatcher_Consume(t *testing.T) {
	mockey.PatchConvey("happy path", t, func() {
		count := 0
		err := NewDispatcher([]Item{
			testItem{val: "1", groupByVal: "a"},
			testItem{val: "2", groupByVal: "a"},
			testItem{val: "3", groupByVal: "b"},
			testItem{val: "3", groupByVal: "b"},
			testItem{val: "3", groupByVal: "b"},
		}, Context(context.Background()), Name("test"), ChunkSize(2), IgnoreError(false), Consume(func(chunk []Item) error {
			count++
			return nil
		})).Run()

		So(err, ShouldBeNil)
		So(count, ShouldEqual, 2)
	})
	mockey.PatchConvey("consume error", t, func() {
		d := NewDispatcher([]Item{
			testItem{val: "1", groupByVal: "a"},
			testItem{val: "2", groupByVal: "a"},
			testItem{val: "3", groupByVal: "b"},
		}, ChunkSize(2), IgnoreError(false))
		count := 0
		err := d.Consume(func(chunk []Item) error {
			count++
			if count == 1 {
				return fmt.Errorf("mock error")
			}
			return nil
		})
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldEqual, "mock error")
		So(count, ShouldEqual, 1)
	})
	mockey.PatchConvey("consume ignore error", t, func() {
		d := NewDispatcher([]Item{
			testItem{val: "1", groupByVal: "a"},
			testItem{val: "2", groupByVal: "a"},
			testItem{val: "3", groupByVal: "b"},
		}, ChunkSize(2), IgnoreError(true))
		count := 0
		err := d.Consume(func(chunk []Item) error {
			count++
			if count == 2 {
				return fmt.Errorf("mock error")
			}
			return nil
		})
		So(err, ShouldBeNil)
		So(count, ShouldEqual, 2)
	})
}

package mq_redis_stream

import (
"context"
"fmt"
"sync"

"hotel/common/log"

"github.com/zeromicro/go-zero/core/stores/redis"
)

// MessageHandler 消息处理器函数类型
type MessageHandler func(message []byte) error

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
TopicName    string `yaml:"topic_name" json:"topic_name"`
ChannelName  string `yaml:"channel_name" json:"channel_name"`
ConsumerName string `yaml:"consumer_name" json:"consumer_name"`
}

// Consumer Redis Stream消费者实现
type Consumer struct {
redis   *redis.Redis
config  *ConsumerConfig
handler MessageHandler
ctx     context.Context
cancel  context.CancelFunc
running bool
mu      sync.RWMutex
}

// NewConsumer 创建Redis Stream消费者
func NewConsumer(redisConfig *RedisConfig, consumerConfig *ConsumerConfig, handler MessageHandler) (*Consumer, error) {
if consumerConfig.TopicName == "" {
return nil, fmt.Errorf("topic name cannot be empty")
}
if consumerConfig.ChannelName == "" {
return nil, fmt.Errorf("channel name cannot be empty")
}
if handler == nil {
return nil, fmt.Errorf("handler cannot be nil")
}

// 构建Redis配置
redisConf := redis.RedisConf{
Host: fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
Pass: redisConfig.Password,
Type: "node",
Tls:  false,
}

// 创建Redis客户端
redisClient, err := redis.NewRedis(redisConf)
if err != nil {
return nil, fmt.Errorf("failed to create redis client: %w", err)
}

ctx, cancel := context.WithCancel(context.Background())

return &Consumer{
redis:   redisClient,
config:  consumerConfig,
handler: handler,
ctx:     ctx,
cancel:  cancel,
running: false,
}, nil
}

// Start 开始消费消息
func (c *Consumer) Start() error {
c.mu.Lock()
c.running = true
c.mu.Unlock()

log.Info("Redis Stream consumer started for stream: %s", c.config.TopicName)
return nil
}

// Stop 停止消费者
func (c *Consumer) Stop() {
if c.cancel != nil {
c.cancel()
c.mu.Lock()
c.running = false
c.mu.Unlock()
log.Info("Redis Stream consumer stopped for stream: %s", c.config.TopicName)
}
}

// IsRunning 检查消费者是否正在运行
func (c *Consumer) IsRunning() bool {
c.mu.RLock()
defer c.mu.RUnlock()
return c.running
}

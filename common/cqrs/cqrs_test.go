package cqrs

import (
	"context"
	"testing"
	"time"
)

func TestNewProducer(t *testing.T) {
	config := &Config{
		Type: "redis_stream",
		Redis: RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	producer, err := NewProducer(config)
	if err != nil {
		t.Fatalf("Failed to create producer: %v", err)
	}

	if producer == nil {
		t.<PERSON><PERSON>("Producer is nil")
	}

	// 测试发布不同类型的消息
	ctx := context.Background()

	// 测试字符串消息
	err = producer.Publish(ctx, "test-topic", "hello world")
	if err != nil {
		t.Logf("Failed to publish string message (expected if Redis not running): %v", err)
	}

	// 测试结构体消息 (JSON序列化)
	type TestMessage struct {
		ID      int    `json:"id"`
		Content string `json:"content"`
	}
	testMsg := TestMessage{ID: 1, Content: "test message"}
	err = producer.Publish(ctx, "test-topic", testMsg)
	if err != nil {
		t.Logf("Failed to publish struct message (expected if Redis not running): %v", err)
	}

	// 测试原始字节消息
	err = producer.PublishRaw(ctx, "test-topic", []byte("raw bytes"))
	if err != nil {
		t.Logf("Failed to publish raw message (expected if Redis not running): %v", err)
	}

	// 测试带选项发布消息
	opts := &PublishOptions{
		Headers: map[string]string{
			"event_type": "test",
			"version":    "1.0",
		},
	}
	err = producer.PublishWithOptions(ctx, "test-topic", "hello with options", opts)
	if err != nil {
		t.Logf("Failed to publish message with options (expected if Redis not running): %v", err)
	}

	// 关闭生产者
	err = producer.Close()
	if err != nil {
		t.Fatalf("Failed to close producer: %v", err)
	}
}

func TestNewConsumer(t *testing.T) {
	config := &Config{
		Type: "redis_stream",
		Redis: RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	consumerConfig := &ConsumerConfig{
		TopicName:    "test-topic",
		ChannelName:  "test-group",
		ConsumerName: "test-consumer",
	}

	handler := func(message []byte) error {
		t.Logf("Received message: %s", string(message))
		return nil
	}

	consumer, err := NewConsumer(config, consumerConfig, handler)
	if err != nil {
		t.Fatalf("Failed to create consumer: %v", err)
	}

	if consumer == nil {
		t.Fatal("Consumer is nil")
	}

	// 测试消费者状态
	if consumer.IsRunning() {
		t.Fatal("Consumer should not be running initially")
	}

	// 启动消费者
	err = consumer.Start()
	if err != nil {
		t.Logf("Failed to start consumer (expected if Redis not running): %v", err)
		return
	}

	// 检查运行状态
	if !consumer.IsRunning() {
		t.Fatal("Consumer should be running after start")
	}

	// 停止消费者
	consumer.Stop()

	// 等待一下让停止操作完成
	time.Sleep(100 * time.Millisecond)

	if consumer.IsRunning() {
		t.Fatal("Consumer should not be running after stop")
	}
}

// TestNewProducerUnsupportedType 测试不支持的消息队列类型
func TestNewProducerUnsupportedType(t *testing.T) {
	config := &Config{
		Type: "unsupported",
	}

	producer, err := NewProducer(config)
	if err == nil {
		t.Fatal("Expected error for unsupported type")
	}
	if producer != nil {
		t.Fatal("Producer should be nil for unsupported type")
	}
}

// TestNewConsumerUnsupportedType 测试不支持的消息队列类型
func TestNewConsumerUnsupportedType(t *testing.T) {
	config := &Config{
		Type: "unsupported",
	}

	consumerConfig := &ConsumerConfig{
		TopicName:    "test-topic",
		ChannelName:  "test-group",
		ConsumerName: "test-consumer",
	}

	handler := func(message []byte) error {
		return nil
	}

	consumer, err := NewConsumer(config, consumerConfig, handler)
	if err == nil {
		t.Fatal("Expected error for unsupported type")
	}
	if consumer != nil {
		t.Fatal("Consumer should be nil for unsupported type")
	}
}

// TestNewConsumerNilHandler 测试空处理器
func TestNewConsumerNilHandler(t *testing.T) {
	config := &Config{
		Type: "redis_stream",
		Redis: RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	consumerConfig := &ConsumerConfig{
		TopicName:    "test-topic",
		ChannelName:  "test-group",
		ConsumerName: "test-consumer",
	}

	consumer, err := NewConsumer(config, consumerConfig, nil)
	if err == nil {
		t.Fatal("Expected error for nil handler")
	}
	if consumer != nil {
		t.Fatal("Consumer should be nil for nil handler")
	}
}

// TestProducerPublishEmptyTopic 测试空主题名称
func TestProducerPublishEmptyTopic(t *testing.T) {
	config := &Config{
		Type: "redis_stream",
		Redis: RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	producer, err := NewProducer(config)
	if err != nil {
		t.Fatalf("Failed to create producer: %v", err)
	}
	defer producer.Close()

	ctx := context.Background()
	err = producer.Publish(ctx, "", []byte("test message"))
	if err == nil {
		t.Fatal("Expected error for empty topic")
	}
}

// TestConsumerEmptyConfig 测试空配置
func TestConsumerEmptyConfig(t *testing.T) {
	config := &Config{
		Type: "redis_stream",
		Redis: RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	handler := func(message []byte) error {
		return nil
	}

	// 测试空主题名称
	consumerConfig := &ConsumerConfig{
		TopicName:    "",
		ChannelName:  "test-group",
		ConsumerName: "test-consumer",
	}

	consumer, err := NewConsumer(config, consumerConfig, handler)
	if err == nil {
		t.Fatal("Expected error for empty topic name")
	}
	if consumer != nil {
		t.Fatal("Consumer should be nil for empty topic name")
	}

	// 测试空通道名称
	consumerConfig = &ConsumerConfig{
		TopicName:    "test-topic",
		ChannelName:  "",
		ConsumerName: "test-consumer",
	}

	consumer, err = NewConsumer(config, consumerConfig, handler)
	if err == nil {
		t.Fatal("Expected error for empty channel name")
	}
	if consumer != nil {
		t.Fatal("Consumer should be nil for empty channel name")
	}
}

func TestProducerSerialization(t *testing.T) {
	config := &Config{
		Type: "redis_stream",
		Redis: RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	producer, err := NewProducer(config)
	if err != nil {
		t.Fatalf("Failed to create producer: %v", err)
	}
	defer producer.Close()

	ctx := context.Background()

	// 测试不同类型的序列化
	testCases := []struct {
		name    string
		message interface{}
	}{
		{"string", "hello world"},
		{"bytes", []byte("raw bytes")},
		{"int", 42},
		{"struct", map[string]interface{}{"id": 1, "name": "test"}},
		{"slice", []string{"a", "b", "c"}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := producer.Publish(ctx, "test-serialization", tc.message)
			if err != nil {
				t.Logf("Failed to publish %s message (expected if Redis not running): %v", tc.name, err)
			}
		})
	}
}

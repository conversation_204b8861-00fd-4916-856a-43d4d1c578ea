package nsq

import (
	"fmt"
	"sync"

	"hotel/common/log"

	"github.com/nsqio/go-nsq"
)

// NSQConfig NSQ配置
type NSQConfig struct {
	NSQDAddress      string   `yaml:"nsqd_address" json:"nsqd_address"`
	LookupdAddresses []string `yaml:"lookupd_addresses" json:"lookupd_addresses"`
	MaxInFlight      int      `yaml:"max_in_flight" json:"max_in_flight"`
	MaxAttempts      uint16   `yaml:"max_attempts" json:"max_attempts"`
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	TopicName    string `yaml:"topic_name" json:"topic_name"`
	ChannelName  string `yaml:"channel_name" json:"channel_name"`
	ConsumerName string `yaml:"consumer_name" json:"consumer_name"`
}

// MessageHandler 消息处理器函数类型
type MessageHandler func(message []byte) error

// Consumer NSQ消费者实现
type Consumer struct {
	consumer *nsq.Consumer
	config   *ConsumerConfig
	handler  MessageHandler
	running  bool
	mu       sync.RWMutex
}

// NewConsumer 创建NSQ消费者
func NewConsumer(nsqConfig *NSQConfig, consumerConfig *ConsumerConfig, handler MessageHandler) (*Consumer, error) {
	if consumerConfig.TopicName == "" {
		return nil, fmt.Errorf("topic name cannot be empty")
	}
	if consumerConfig.ChannelName == "" {
		return nil, fmt.Errorf("channel name cannot be empty")
	}
	if handler == nil {
		return nil, fmt.Errorf("handler cannot be nil")
	}

	config := nsq.NewConfig()

	// 创建NSQ消费者
	consumer, err := nsq.NewConsumer(consumerConfig.TopicName, consumerConfig.ChannelName, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create NSQ consumer: %w", err)
	}

	c := &Consumer{
		consumer: consumer,
		config:   consumerConfig,
		handler:  handler,
		running:  false,
	}

	// 设置消息处理器
	consumer.AddHandler(c)

	return c, nil
}

// HandleMessage 实现nsq.Handler接口
func (c *Consumer) HandleMessage(message *nsq.Message) error {
	// 调用用户定义的处理器
	err := c.handler(message.Body)
	if err != nil {
		log.Error("Error handling NSQ message: %v", err)
		// 简单的错误处理：记录错误但完成消息
		message.Finish()
		return nil
	}

	// 成功处理，完成消息
	message.Finish()
	return nil
}

// Start 开始消费消息
func (c *Consumer) Start() error {
	// 连接到NSQ
	err := c.consumer.ConnectToNSQD("127.0.0.1:4150") // 这里应该从配置中获取
	if err != nil {
		return fmt.Errorf("failed to connect to NSQ: %w", err)
	}

	c.mu.Lock()
	c.running = true
	c.mu.Unlock()

	log.Info("NSQ consumer started for topic: %s, channel: %s", c.config.TopicName, c.config.ChannelName)

	return nil
}

// Stop 停止消费者
func (c *Consumer) Stop() {
	if c.consumer != nil {
		c.consumer.Stop()
		c.mu.Lock()
		c.running = false
		c.mu.Unlock()
		log.Info("NSQ consumer stopped for topic: %s", c.config.TopicName)
	}
}

// IsRunning 检查消费者是否正在运行
func (c *Consumer) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}

package domain

import (
	"context"
	"sync"

	"hotel/common/log"
)

type Session struct {
	Id     string            `json:"id"`
	Params map[string]string `json:"params"`
	mu     sync.Mutex
}

func (s *Session) UpdateParams(ctx context.Context, k, v string) {
	ov := s.Params[k]
	if s.Params == nil {
		s.Params = make(map[string]string)
	}
	s.Params[k] = v
	log.Infoc(ctx, "update session(%v) set params %s=%s(from %v)", s.Id, k, v, ov)
}
func (s *Session) GetParams(ctx context.Context, k string) string {
	ov := s.Params[k]
	log.Infoc(ctx, "get session(%v) params %s=%s", s.Id, k, ov)
	return ov
}
func (s *Session) SetSellerPayloads(ctx context.Context, v string) {
	s.UpdateParams(ctx, SessionKey_SellerPayloads, v)
}
func (s *Session) GetSellerPayloads(ctx context.Context) string {
	return s.GetParams(ctx, SessionKey_SellerPayloads)
}
func (s *Session) SetCheckAvailResp(ctx context.Context, v string) {
	s.UpdateParams(ctx, SessionKey_CheckAvailResp, v)
}
func (s *Session) GetCheckAvailResp(ctx context.Context) string {
	return s.GetParams(ctx, SessionKey_CheckAvailResp)
}

// func (s *Session) SetBookeq(ctx context.Context, v string) {
// 	s.UpdateParams(ctx, SessionKey_BookReq, v)
// }
// func (s *Session) GetBookReq(ctx context.Context) string {
// 	return s.GetParams(ctx, SessionKey_BookReq)
// }

const (
	SessionKey_SellerPayloads = "SellerPayloads"
	SessionKey_CheckAvailResp = "CheckAvailResp"
	// SessionKey_BookReq        = "BookReq"
)

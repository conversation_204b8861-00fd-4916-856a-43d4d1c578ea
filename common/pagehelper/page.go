package pagehelper

import (
	"context"
)

type PageReq struct {
	PageNum  int64 `json:"pageNum,default=1"`
	PageSize int64 `json:"pageSize"`
	Cursor   int64 `json:"cursor"`
}

func (p PageReq) GetOffset() int64 {
	if p.PageNum == 0 {
		return 0
	}
	return (p.PageNum - 1) * p.PageSize
}

func (p PageReq) GetNextPageOffset() int64 {
	return p.PageNum * p.PageSize
}

type PageResp struct {
	Total   int64 `json:"total"`
	HasMore bool  `json:"hasMore"`
}

const _pageReqCtxKey = "pageReq"

func BindPageReqToContext(ctx context.Context, req *PageReq) context.Context {
	return context.WithValue(ctx, _pageReqCtxKey, req)
}
func RetrievePageReqFromContext(ctx context.Context) *PageReq {
	v, ok := ctx.Value(_pageReqCtxKey).(*PageReq)
	if !ok {
		return nil
	}
	return v
}

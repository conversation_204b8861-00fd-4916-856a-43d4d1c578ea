package bizerr

// 错误码规范
// [错误码标识] [保留位(或扩展为业务标识)] [子业务/模块标识] [具体错误]
// [x]		   [xx]		     		   [xx]		       [xxxx]
//
// 错误码标识位
// 1: 系统错误或组件错误
// 2: 公用 RPC 错误
// 3: 业务内部逻辑错误

var (
	// Success 请求成功
	Success  = NewHTTP(0, "Success", 200)
	ParamErr = NewHTTP(1_00_00_0400, "param error", 400).WithName("ParamErr")
	// RateLimitErr 触发限流
	RateLimitErr = NewHTTP(1_00_00_0429, "quota limit exceed", 429).WithName("RateLimitErr")
	// TimeoutErr retryable
	TimeoutErr = NewHTTP(1_00_00_0504, "timeout", 504).WithName("TimeoutErr")
	// NotFoundErr not found
	NotFoundErr = NewHTTP(1_00_00_0404, "not found", 404).WithName("NotFoundErr")
	// PermissionDeniedErr denied
	PermissionDeniedErr = NewHTTP(1_00_00_0403, "permission denied", 403).WithName("PermissionDeniedErr")
	// AuthenticationErr invalid password
	AuthenticationErr = NewHTTP(1_00_00_0401, "authentication denied", 401).WithName("AuthenticationErr")

	// SystemErr 系统异常
	SystemErr = New(1_00_00_0500, "system error").WithName("SystemErr")
	// CrossTenantErr 跨租户异常
	CrossTenantErr = New(1_00_00_1001, "cross tenant error").WithName("CrossTenantErr")
	// EmptyCtxTenantErr ctx 无租户信息
	EmptyCtxTenantErr = New(1_00_00_1002, "empty ctx tenant error").WithName("EmptyCtxTenantErr")
	// DuplicateErr 重复异常
	DuplicateErr = New(1_00_00_1003, "duplicate error").WithName("DuplicateErr")
	// DependencyErr dependency error
	DependencyErr = New(1_00_00_1004, "dependency error").WithName("DependencyErr")
	// PageSizeExceedLimitErr page size exceed limit
	PageSizeExceedLimitErr = NewHTTP(1_00_00_1005, "page size exceed limit", 400).WithName("PageSizeExceedLimitErr")
	NotImplementedErr      = NewHTTP(1_00_00_1006, "not implemented err", 400).WithName("NotImplementedErr")
	ExpiredErr             = NewHTTP(1_00_00_1007, "expired", 400).WithName("ExpiredErr")
	NotMatchedErr          = NewHTTP(1_00_00_1008, "not match", 400).WithName("NotMatchedErr")
	NeedHelpErr            = NewHTTP(1_00_00_0911, "need help", 911).WithName("NeedHelpErr")
)

package bizerr

import (
	"errors"
	"fmt"
	"reflect"
	"sync"
)

// BizError 为一个不可变的对象, 调用 WithMessage WithMessagef 以及 Wrap 函数时, 注意获取返回的新对象
// 能够避免修改已经定义的 BizError 并且避免 Wrap 时产生死循环
type BizError struct {
	statusCode int32
	// httpCode http 状态码, 仅当 interceptor 为 http 模式时, 会返回给上游接口. 默认值为 500
	httpCode      int32
	statusMessage string
	err           error
	name          string
}

func (e *BizError) WithName(n string) *BizError {
	e.name = n
	return e
}

func (e *BizError) String() string {
	return e.Error()
}

func (e *BizError) Error() string {
	if e == nil {
		return "(*BizError)<nil>"
	}
	if e.err != nil {
		return fmt.Sprintf("ErrorCode=[%v] Message=[%v] Err=[%v]", e.statusCode, e.statusMessage, e.err)
	}
	return fmt.Sprintf("ErrorCode=[%v] Message=[%v]", e.statusCode, e.statusMessage)
}

// WithMessage 修改 StatusMessage 并会返回一个新的 BizError
func (e *BizError) WithMessage(msg string) *BizError {
	bizErr := copyErr(e)
	bizErr.statusMessage = msg
	return bizErr
}

// WithMessagef 修改 StatusMessage 并会返回一个新的 BizError
func (e *BizError) WithMessagef(format string, a ...any) *BizError {
	bizErr := copyErr(e)
	bizErr.statusMessage = fmt.Sprintf(format, a...)
	return bizErr
}

// Wrap 包装 error 并会返回一个新的 BizError
func (e *BizError) Wrap(err error) *BizError {
	// 不能 wrap 自己
	if errors.Is(err, e) {
		return e
	}

	bizErr := copyErr(e)
	bizErr.err = err
	return bizErr
}
func (e *BizError) Name() string {
	if e == nil {
		return ""
	}
	return e.name
}

// Unwrap 返回当前包装的 error
func (e *BizError) Unwrap() error {
	return e.err
}

func (e *BizError) StatusCode() int32 {
	if e == nil {
		return 0
	}
	return e.statusCode
}

func (e *BizError) StatusMessage() string {
	return e.statusMessage
}

func (e *BizError) HTTPCode() int32 {
	return e.httpCode
}

// implement errors.Is
func (e *BizError) Is(oe error) bool {
	return e.HasSameCode(oe)
}

// HasSameCode 判断与另一个 BizError 是否拥有相同的 error code
func (e *BizError) HasSameCode(err error) (ok bool) {
	if e == err {
		return true
	}
	var bizErr *BizError

	if bizErr, ok = UnwrapForBizErr(err); ok {
		return e.statusCode == bizErr.statusCode
	}
	return false
}

func New(statusCode int32, msg string) *BizError {
	return NewHTTP(statusCode, msg, 500)
}

func NewHTTP(statusCode int32, msg string, httpCode int32) *BizError {
	err := &BizError{
		statusCode:    statusCode,
		httpCode:      httpCode,
		statusMessage: msg,
	}
	_mu.Lock()
	_pool = append(_pool, err)
	_mu.Unlock()
	return err
}

func copyErr(e *BizError) *BizError {
	return &BizError{
		statusCode:    e.statusCode,
		httpCode:      e.httpCode,
		statusMessage: e.statusMessage,
		err:           e.err,
	}
}

func CastBizErr(err error) (bizErr *BizError, ok bool) {
	if IsNil(err) {
		return nil, false
	}
	ok = errors.As(err, &bizErr)
	return bizErr, ok
}

func UnwrapForBizErr(err error) (*BizError, bool) {
	var bizErrType *BizError
	if !errors.As(err, &bizErrType) {
		return nil, false
	}
	// 最多 unwrap 100次, 避免出现死循环
	maxWrap := 100
	for i := 1; !IsNil(err) && i <= maxWrap; i++ {
		var bizErr *BizError
		if errors.As(err, &bizErr) {
			return bizErr, true
		}
		err = errors.Unwrap(err)
	}
	return nil, false
}

func IsNil(err error) bool {
	if err == nil {
		return true
	}
	t := reflect.ValueOf(err)
	return t.Kind() == reflect.Ptr && t.IsNil()
}

var (
	_mu   sync.Mutex
	_pool []*BizError
)

func GetAllBizErrors() []*BizError {
	return _pool
}

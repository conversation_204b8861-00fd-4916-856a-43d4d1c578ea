package context

import (
	"context"
	"testing"
	"time"
)

const (
	shortDuration = 1 * time.Millisecond
)

func TestCancel(t *testing.T) {
	t.Parallel()

	raw, cancel := context.WithCancel(context.Background())
	detached := Detach(raw)

	if d := raw.Done(); d == nil {
		t.<PERSON>rrorf("contextWithCancel.Done() == %v want non-nil before cancel", d)
	}
	if d := detached.Done(); d != nil {
		t.Errorf("detached.Done() == %v want nil before cancel", d)
	}

	if e := raw.Err(); e != nil {
		t.Errorf("contextWithCancel.Err() == %v want nil before cancel", e)
	}
	if e := detached.Err(); e != nil {
		t.Errorf("detached.Err() == %v want nil before cancel", e)
	}

	select {
	case x := <-raw.Done():
		t.Errorf("<-raw.Done() == %v want nothing (it should block)", x)
	case x := <-detached.Done():
		t.<PERSON><PERSON>rf("<-detached.Done() == %v want nothing (it should block)", x)
	default:
	}

	// 测试raw context cancel，预期表现:
	// 1. raw context 不会收到context.Done()
	// 2. detached, reset cancel context 会收到context.Done()
	cancel()
	select {
	case <-raw.Done():
	default:
		t.Errorf("<-raw.Done() blocked, but shouldn't have")
	}
	if e := raw.Err(); e != context.Canceled {
		t.Errorf("raw.Err() == %v want %v", e, context.Canceled)
	}

	select {
	case <-raw.Done():
	default:
		t.Errorf("<-raw.Done() blocked, but shouldn't have")
	}
	if e := raw.Err(); e != context.Canceled {
		t.Errorf("raw.Err() == %v want %v", e, context.Canceled)
	}
}

func TestRestCancel(t *testing.T) {
	t.Parallel()

	raw, _ := context.WithCancel(context.Background())
	detached := Detach(raw)
	resetCtx, resetCancel := context.WithCancel(detached)

	if d := raw.Done(); d == nil {
		t.Errorf("contextWithCancel.Done() == %v want non-nil before cancel", d)
	}
	if d := detached.Done(); d != nil {
		t.Errorf("detached.Done() == %v want nil before cancel", d)
	}
	if d := resetCtx.Done(); d == nil {
		t.Errorf("resetCtx.Done() == %v want non-nil before cancel", d)
	}
	if e := raw.Err(); e != nil {
		t.Errorf("contextWithCancel.Err() == %v want nil before cancel", e)
	}
	if e := detached.Err(); e != nil {
		t.Errorf("detached.Err() == %v want nil before cancel", e)
	}
	if e := resetCtx.Err(); e != nil {
		t.Errorf("resetCtx.Err() == %v want nil before cancel", e)
	}

	select {
	case x := <-raw.Done():
		t.Errorf("<-raw.Done() == %v want nothing (it should block)", x)
	case x := <-detached.Done():
		t.Errorf("<-detached.Done() == %v want nothing (it should block)", x)
	case x := <-resetCtx.Done():
		t.Errorf("<-resetCtx.Done() == %v want nothing (it should block)", x)
	default:
	}

	// 测试reset context cancel，预期表现:
	// 1. raw, detached context 不会收到context.Done()
	// 2. reset cancel context 会收到context.Done()
	resetCancel()

	select {
	case <-raw.Done():
		t.Errorf("raw context shouldn't receive Done()")
	default:
	}

	select {
	case <-detached.Done():
		t.Errorf("detached context shouldn't receive Done()")
	default:
	}
	if e := detached.Err(); e != nil {
		t.Errorf("detached.Err() == %v want %v", e, nil)
	}

	select {
	case <-resetCtx.Done():
	default:
		t.Errorf("<-resetCtx.Done() blocked, but shouldn't have")
	}
	if e := resetCtx.Err(); e != context.Canceled {
		t.Errorf("resetCtx.Err() == %v want %v", e, context.Canceled)
	}
}

type ctxKey string

var key = ctxKey("key")

func TestDeadline(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	defer cancel()
	ctx = context.WithValue(ctx, key, "value")
	dctx := Detach(ctx)
	// Detached context has the same values.
	got, ok := dctx.Value(key).(string)
	if !ok || got != "value" {
		t.Errorf("Value: got (%v, %t), want 'value', true", got, ok)
	}
	// Detached context doesn't time out.
	time.Sleep(500 * time.Millisecond)
	if err := ctx.Err(); err != context.DeadlineExceeded {
		t.Fatalf("original context Err: got %v, want DeadlineExceeded", err)
	}
	if err := dctx.Err(); err != nil {
		t.Errorf("detached context Err: got %v, want nil", err)
	}
}

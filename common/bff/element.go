package bff

import (
	"time"

	"hotel/common/i18n"
)

type ElementRow[T any] struct {
	// @generic: User,<PERSON>tity,InvitationInfoUserDetail,UserRole,OrderSummary,OrderGainSummary,StarlingItem
	Raw     T             `json:"raw"`     // 原始对象，一行的数据
	Key     string        `json:"key"`     // 行 id
	Columns []ElementItem `json:"columns"` // BFF的一行数据
}

type ElementItem struct {
	Type    ElementType    `json:"type"`
	Content ElementContent `json:"content" options:""`
}

// ElementContent
// @autowire: true
type ElementContent interface {
	Type() string
}

type ElementType = string

const (
	ElementTypeText               ElementType = "text"
	ElementTypeTime               ElementType = "time"
	ElementTypeIconText           ElementType = "icon_text"
	ElementTypeCheckbox           ElementType = "checkbox"
	ElementTypeSingleChoiceOption ElementType = "single_choice_option"
	ElementTypeMultiChoiceOption  ElementType = "multi_choice_option"
	ElementTypeMoney              ElementType = "money"
	ElementTypeLink               ElementType = "link"
	ElementTypeButton             ElementType = "button"
	ElementTypeButtonGroup        ElementType = "button_group"
)

type ElementButtons struct {
	Button *Button `json:"button"`
	CustomStyleComponent
}

func (b ElementButtons) Type() string {
	return ElementTypeButton
}

type ElementLink struct {
	Link     string    `json:"link"`
	LinkText i18n.I18N `json:"linkText"`
	IconComponent
}

func (e *ElementLink) Type() string {
	return ElementTypeLink
}

type ElementMoney struct {
	Amount            float64 `json:"amount"`
	Currency          string  `json:"currency"`
	CurrencyLabelIcon string  `json:"currencyLabelIcon"`
	Precision         int64   `json:"precision" default:"2"` // 小数点后精度，默认2
	CustomStyleComponent
}

func (e *ElementMoney) Type() string {
	return ElementTypeMoney
}

type ElementText struct {
	Text i18n.I18N `json:"text"`
	CustomStyleComponent
}

func (e *ElementText) Type() string {
	return ElementTypeText
}

// TextContent 简化的文本内容类型
type TextContent struct {
	Text i18n.I18N `json:"text"`
}

func (t TextContent) Type() string {
	return ElementTypeText
}

// ButtonGroupContent 按钮组内容类型
type ButtonGroupContent struct {
	Buttons []Button `json:"buttons"`
	CustomStyleComponent
}

func (b ButtonGroupContent) Type() string {
	return ElementTypeButtonGroup
}

type ElementTime struct {
	Time   time.Time `json:"time,omitzero"`
	Layout string    `json:"layout,omitempty"`
	CustomStyleComponent
}

func (e *ElementTime) Type() string {
	return ElementTypeTime
}

type ElementIconText struct {
	Text i18n.I18N `json:"text"`
	IconComponent
}

func (e *ElementIconText) Type() string {
	return ElementTypeIconText
}

type ElementSingleChoiceOption struct {
	Options []LabelText `json:"options"`
	Value   *LabelText  `json:"value"` // 在 response 中表示默认值；"value.key" -> value to backend
	CustomStyleComponent
}

func (e *ElementSingleChoiceOption) Type() string {
	return ElementTypeSingleChoiceOption
}

type ElementMultiChoiceOption struct {
	Options []LabelText  `json:"options"`
	Values  []*LabelText `json:"values"` // 在 response 中表示默认值；"values[].*.key" -> value to backend
	CustomStyleComponent
}

func (e *ElementMultiChoiceOption) Type() string {
	return ElementTypeMultiChoiceOption
}

type ElementCheckbox struct {
	Checked bool `json:"checked"` // value to backend
	TitleComponent
	IconComponent
	CustomStyleComponent
}

func (e *ElementCheckbox) Type() string {
	return ElementTypeCheckbox
}

type LabelText struct {
	Text i18n.I18N `json:"text"`
	Key  string    `json:"key"` // value to backend
	CustomStyleComponent
}

type CustomStyleComponent struct {
	StyleId StyleId `json:"styleId,omitempty"`
}
type IconComponent struct {
	Icon string `json:"icon,omitempty"`
}

type TitleComponent struct {
	Title    i18n.I18N `json:"title,omitzero"`
	SubTitle i18n.I18N `json:"subTitle,omitzero"`
}

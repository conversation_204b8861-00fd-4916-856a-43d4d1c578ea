package bff

import (
	"hotel/common/i18n"
)

var OrderDetailButton = Button{
	ID:       ActionId_DetailOrder,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetOrderDetailButton() Button {
	return OrderDetailButton
}

var OrderCancelButton = Button{
	ID:       ActionId_CancelOrder,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetOrderCancelButton() Button {
	return OrderCancelButton
}

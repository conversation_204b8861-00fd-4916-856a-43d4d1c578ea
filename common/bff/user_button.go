package bff

import (
	"hotel/common/i18n"
)

var UserFreezeButton = Button{
	ID:       ActionId_FreezeUser,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetUserFreezeButton() Button {
	return UserFreezeButton
}

var UserUnFreezeButton = Button{
	ID:       ActionId_UnFreezeUser,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetUserUnFreezeButton() Button {
	return UserUnFreezeButton
}

var UserDetailButton = Button{
	ID:       ActionId_DetailUser,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetUserDetailButton() Button {
	return UserDetailButton
}

var UserInviteButton = Button{
	ID:       ActionId_InviteUser,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetUserInviteButton() Button {
	return UserInviteButton
}

var UserReSendInvitationButton = Button{
	ID:       ActionId_ReSendInvitationUser,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetUserReSendInvitationButton() Button {
	return UserReSendInvitationButton
}

var UserResetPasswordButton = Button{
	ID:       ActionId_ResetPassword,
	Disabled: false,
	Content:  ElementItem{},
	Hover:    i18n.I18N{},
}

func GetUserResetPasswordButton() Button {
	return UserResetPasswordButton
}

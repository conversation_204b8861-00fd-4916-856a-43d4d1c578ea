package bff

import (
	"hotel/common/i18n"
)

//// NavigationTab 原有结构，保持向后兼容
//type NavigationTab struct {
//	ID       string `json:"id"`
//	Disabled bool   `json:"disabled"`
//
//	Title   i18n.I18N        `json:"title"`
//	Hover   i18n.I18N        `json:"hover"`
//	SubTabs []*NavigationTab `json:"subTabs"`
//	Unfold  bool             `json:"unfold,omitempty"`
//}

// MenuRoute 新的菜单路由结构，按照协议文档设计
type MenuRoute struct {
	ID        string           `json:"id"`                  // 菜单唯一标识
	Path      string           `json:"path"`                // 路由路径
	Component string           `json:"component,omitempty"` // 组件路径（叶子节点必须有）
	Name      string           `json:"name"`                // 路由名称
	Title     i18n.I18N        `json:"title"`               // 菜单标题（直接包含多语言内容）
	Icon      string           `json:"icon,omitempty"`      // 菜单图标
	Meta      *MenuRouteMeta   `json:"meta,omitempty"`      // 路由元信息
	Children  []*MenuRoute     `json:"children,omitempty"`  // 子菜单
}

// MenuRouteMeta 路由元信息
type MenuRouteMeta struct {
	RequiresAuth bool     `json:"requiresAuth,omitempty"` // 是否需要认证
	Roles        []string `json:"roles,omitempty"`        // 允许访问的角色列表
	Permissions  []string `json:"permissions,omitempty"`  // 需要的权限列表
	KeepAlive    bool     `json:"keepAlive,omitempty"`    // 是否缓存组件
	IsHide       bool     `json:"isHide,omitempty"`       // 是否在菜单中隐藏
	IsFullPage   bool     `json:"isFullPage,omitempty"`   // 是否全屏显示
	ActivePath   string   `json:"activePath,omitempty"`   // 激活的菜单路径
}

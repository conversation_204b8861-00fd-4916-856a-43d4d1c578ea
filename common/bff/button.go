package bff

import (
	"hotel/common/i18n"
)

type Button struct {
	ID       ActionId `json:"id"`       // 跟前端约定的值，前端根据这个 id 执行相应的动作
	Disabled bool     `json:"disabled"` // 是否禁用，处理用户可见，但不可点击的按钮

	Content    ElementItem `json:"content"`           // 按钮上展示的内容
	Hover      i18n.I18N   `json:"hover,omitempty"`   // 按钮提示文案
	JumpURL    string      `json:"jumpURL,omitempty"` // 跳转链接;若存在跳转链接，则默认情况下，后端需要将必要参数拼接到跳链中，然后前端可以无脑跳链
	OpenNewTab bool        `json:"openNewTab,omitempty"`
}

type ButtonList []*Button

type ActionId = string

const (
	ActionIdEdit                    ActionId = "edit"
	ActionIdDelete                  ActionId = "delete"
	ActionIdView                    ActionId = "view"
	ActionIdCreate                  ActionId = "create"
	ActionId_FreezeUser             ActionId = "user:freeze"
	ActionId_UnFreezeUser           ActionId = "user:unFreeze"
	ActionId_DetailUser             ActionId = "user:detail"
	ActionId_InviteUser             ActionId = "user:invite"
	ActionId_ReSendInvitationUser   ActionId = "user:reSendInvitation"
	ActionId_ResetPassword          ActionId = "user:resetPassword"
	ActionId_UpdateUserRoleStatus   ActionId = "user:updateUserRoleStatus"
	ActionId_UpdateUserBasicProfile ActionId = "user:updateUserBasicProfile"
	ActionId_DetailOrder            ActionId = "trade:detail"
	ActionId_CancelOrder            ActionId = "trade:cancel"
)

package bff

import (
	"hotel/common/pagehelper"
)

type Table[T any] struct {
	pagehelper.PageResp                   // 分页相关
	HeaderKeys           []string         `json:"headerKeys"` // 对应 rows 中每个 element 中的key
	Rows                 []*ElementRow[T] `json:"rows"`
	Header               []ElementItem    `json:"header"`
	StyleId              StyleId          `json:"styleId"`                        // 预定义的几种样式id
	IsRowToColumnEnabled bool             `json:"isRowToColumnEnabled,omitempty"` // 行转列
}

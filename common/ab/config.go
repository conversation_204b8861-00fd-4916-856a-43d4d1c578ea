package ab

import (
	"context"

	"hotel/common/gray"
	"hotel/common/log"
)

type AB struct {
	// Groups AB实验分组，命中第一个分组
	Groups []GroupItem `yaml:"groups" json:"groups"`
}

type GroupItem struct {
	Meta
	gray.Config
}

func (c *AB) GetABContextByCity(ctx context.Context, cityCode string, employeeNumber int64) context.Context {
	for _, grp := range c.Groups {
		if grp.HitByCity(ctx, cityCode, employeeNumber) {
			log.Infoc(ctx, "ab %v hit by city(%v) meta(%#v)", employeeNumber, cityCode, grp.Meta)
			return NewContext(ctx, grp.Meta)
		}
	}
	return ctx
}

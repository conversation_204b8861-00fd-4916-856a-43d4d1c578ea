package config

import (
	"os"
	"path/filepath"
	"strings"

	"hotel/common/envhelper"
)

func resolveRelativePath(p string) string {
	ext := filepath.Ext(p) // contains '.'
	basename := strings.TrimSuffix(p, ext)

	fn := basename + "." + strings.ToLower(envhelper.GetENV()) + ext
	if _, err := os.Stat(filepath.Join(GetProjectPath(), fn)); err == nil {
		return fn
	}
	return p // fallback
}

func SafeFlagString(name string, relativePath, usage string) *string {
	p := new(string)
	rel := resolveRelativePath(relativePath)
	if filepath.IsAbs(rel) {
		*p = rel
		return p
	}
	base := GetProjectPath()
	abs := filepath.Join(base, rel)
	*p = abs
	return p
}

package config

import (
	"os"
	"path/filepath"
)

func GetDefaultConfigPath() string {
	if envPath := os.Getenv("CONFIG_PATH"); envPath != "" {
		return envPath
	}
	if projPath := os.Getenv("PROJECT_PATH"); projPath != "" {
		return projPath + "/" + os.Getenv("SERVICE_NAME") + "/config/config.yaml" // 保持向后兼容
	}
	return ""
}

func GetProjectPath() string {
	if v := os.Getenv("PROJECT_PATH"); v != "" {
		if filepath.IsAbs(v) {
			return v
		}
		abs, err := filepath.Abs(v)
		if err == nil {
			return abs
		}
		return v
	}
	// 递归向上查找go.mod
	wd, err := os.Getwd()
	if err != nil {
		return "."
	}
	dir := wd
	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir
		}
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}
	return wd
}

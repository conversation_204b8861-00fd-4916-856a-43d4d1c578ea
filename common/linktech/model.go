package linktech

import (
	"context"

	"hotel/common/types"
)

type BuyerResolver interface {
	Sellers(ctx context.Context) ([]Participant, error)
	Buyer() Participant
}

type Link struct {
	Buyer  Participant `json:"buyer"`
	Seller Participant `json:"seller"`
}

type Participant struct {
	InNode   InNode   `json:"inNode"`
	OutNode  OutNode  `json:"outNode"`
	EntityId types.ID `json:"entityId"`
}

type InNode interface {
	Node
	GetCredentialId(ctx context.Context) (int64, error) // credential to access the downstream
}
type OutNode interface {
	Node
}
type Node interface {
	GetAggregatedRuleId(ctx context.Context) (int64, error) // one rule combined by multiple rules
}

func NewLink(buyer, seller Participant) *Link {
	return &Link{buyer, seller}
}

package linktech

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"testing"
	"time"

	"github.com/bytedance/arishem/typedef"

	"hotel/common/i18n"
	"hotel/common/money"
	"hotel/common/types"
	"hotel/content/domain"
	"hotel/rule"
	"hotel/search/protocol"
	supplierDomain "hotel/supplier/domain"
)

// HotelRatesDemo 完整的HotelRates接口演示实现
func Test(t *testing.T) {
	fmt.Println("=== HotelRates 接口演示 ===")
	fmt.Println("展示 Link Technology 架构中规则引擎的使用")
	fmt.Println()

	// 1. 演示规则引擎基础功能
	fmt.Println("1. 规则引擎基础演示")
	demoRuleEngine()
	fmt.Println()

	// 2. 演示完整的HotelRates流程
	fmt.Println("2. HotelRates 完整流程演示")
	demoHotelRatesFlow()
	fmt.Println()

	// 3. 演示Link Technology架构
	fmt.Println("3. Link Technology 架构演示")
	demoLinkTechnologyArchitecture()
}

// demoRuleEngine 演示规则引擎基础功能
func demoRuleEngine() {
	fmt.Println("=== 规则引擎基础演示 ===")

	// 演示1: 基础条件判断
	demoBasicCondition()

	// 演示2: 请求参数修改规则
	demoRequestModificationRule()

	// 演示3: 响应数据修改规则
	demoResponseModificationRule()

	// 演示4: 复杂业务规则
	demoComplexBusinessRule()
}

// demoBasicCondition 演示基础条件判断
func demoBasicCondition() {
	fmt.Println("--- 基础条件判断 ---")

	// 条件：用户年龄大于等于18岁
	condition := `{
		"OpLogic": "&&",
		"Conditions": [
			{
				"Operator": ">=",
				"Lhs": {
					"VarExpr": "user.age"
				},
				"Rhs": {
					"Const": {
						"NumConst": 18
					}
				}
			}
		]
	}`

	// 目标：返回成功消息
	aim := `{
		"Const": {
			"StrConst": "用户年龄符合要求"
		}
	}`

	// 创建规则
	ruleTarget, err := rule.NewRule("age_check_rule", condition, aim)
	if err != nil {
		fmt.Printf("创建规则失败: %v\n", err)
		return
	}

	// 创建数据上下文
	dc, err := rule.ParseFactorsMap(context.Background(), typedef.MetaType{
		"user": map[string]interface{}{
			"name": "张三",
			"age":  25,
		},
	})
	if err != nil {
		fmt.Printf("创建数据上下文失败: %v\n", err)
		return
	}

	// 执行规则
	rr := rule.ExecuteRule(context.Background(), ruleTarget, dc)
	if rr.Passed() {
		fmt.Printf("规则通过，输出: %s\n", rr.Aim().AsExpr())
	} else {
		fmt.Println("规则未通过")
	}
}

// demoRequestModificationRule 演示请求参数修改规则
func demoRequestModificationRule() {
	fmt.Println("--- 请求参数修改规则 ---")

	// 条件：供应商类型为 TBO
	condition := `{
		"OpLogic": "&&",
		"Conditions": [
			{
				"Operator": "==",
				"Lhs": {
					"VarExpr": "credential.supplier"
				},
				"Rhs": {
					"Const": {
						"NumConst": 1
					}
				}
			}
		]
	}`

	// 目标：修改请求参数 - 使用简单的表达式而不是ActionList
	aim := `{
		"Const": {
			"StrConst": "TBO_12345"
		}
	}`

	// 创建规则
	ruleTarget, err := rule.NewRule("tbo_request_rule", condition, aim)
	if err != nil {
		fmt.Printf("创建规则失败: %v\n", err)
		return
	}

	// 创建数据上下文
	dc, err := rule.ParseFactorsMap(context.Background(), typedef.MetaType{
		"credential": map[string]interface{}{
			"supplier": 1, // TBO
			"apiKey":   "demo_key",
		},
		"masterHotel": map[string]interface{}{
			"id":   12345,
			"name": "北京希尔顿酒店",
		},
	})
	if err != nil {
		fmt.Printf("创建数据上下文失败: %v\n", err)
		return
	}

	// 执行规则
	rr := rule.ExecuteRule(context.Background(), ruleTarget, dc)
	if rr.Passed() {
		fmt.Println("规则通过，执行请求修改操作:")
		fmt.Printf("  修改后的supplierHotelId: %s\n", rr.Aim().AsExpr())
	} else {
		fmt.Println("规则未通过")
	}
}

// demoResponseModificationRule 演示响应数据修改规则
func demoResponseModificationRule() {
	fmt.Println("--- 响应数据修改规则 ---")

	// 条件：房间价格大于1000
	condition := `{
		"OpLogic": "&&",
		"Conditions": [
			{
				"Operator": ">",
				"Lhs": {
					"VarExpr": "supplierResp.rooms#0.rates#0.rate.finalRate.amount"
				},
				"Rhs": {
					"Const": {
						"NumConst": 1000
					}
				}
			}
		]
	}`

	// 目标：修改响应数据（增加10%加价）
	aim := `{
		"MathExpr": {
			"OpMath": "*",
			"Lhs": {
				"VarExpr": "supplierResp.rooms#0.rates#0.rate.finalRate.amount"
			},
			"Rhs": {
				"Const": {
					"NumConst": 1.1
				}
			}
		}
	}`

	// 创建规则
	ruleTarget, err := rule.NewRule("price_markup_rule", condition, aim)
	if err != nil {
		fmt.Printf("创建规则失败: %v\n", err)
		return
	}

	// 创建数据上下文
	dc, err := rule.ParseFactorsMap(context.Background(), typedef.MetaType{
		"supplierResp": map[string]interface{}{
			"supplier": 1,
			"rooms": []map[string]interface{}{
				{
					"roomTypeId": "room_001",
					"roomName": map[string]interface{}{
						"en": "Deluxe Room",
					},
					"rates": []map[string]interface{}{
						{
							"ratePkgId": "rate_001",
							"rate": map[string]interface{}{
								"finalRate": map[string]interface{}{
									"amount":   1200.0,
									"currency": "USD",
								},
							},
						},
					},
				},
			},
		},
		"masterHotel": map[string]interface{}{
			"id":   12345,
			"name": "北京希尔顿酒店",
		},
	})
	if err != nil {
		fmt.Printf("创建数据上下文失败: %v\n", err)
		return
	}

	// 执行规则
	rr := rule.ExecuteRule(context.Background(), ruleTarget, dc)
	if rr.Passed() {
		fmt.Println("规则通过，执行响应修改操作:")
		fmt.Printf("  修改后的价格: %.2f\n", rr.Aim().AsExpr())
	} else {
		fmt.Println("规则未通过")
	}
}

// demoComplexBusinessRule 演示复杂业务规则
func demoComplexBusinessRule() {
	fmt.Println("--- 复杂业务规则 ---")

	// 条件：多个条件的组合
	// 1. 用户是VIP客户
	// 2. 酒店星级大于等于4星
	// 3. 房间价格在合理范围内
	condition := `{
		"OpLogic": "&&",
		"Conditions": [
			{
				"Operator": "==",
				"Lhs": {
					"VarExpr": "user.vipLevel"
				},
				"Rhs": {
					"Const": {
						"StrConst": "VIP"
					}
				}
			},
			{
				"Operator": ">=",
				"Lhs": {
					"VarExpr": "masterHotel.star"
				},
				"Rhs": {
					"Const": {
						"NumConst": 4
					}
				}
			},
			{
				"Operator": ">=",
				"Lhs": {
					"VarExpr": "supplierResp.rooms#0.rates#0.rate.finalRate.amount"
				},
				"Rhs": {
					"Const": {
						"NumConst": 500
					}
				}
			}
		]
	}`

	// 目标：应用VIP折扣 - 使用简单的表达式
	aim := `{
		"MathExpr": {
			"OpMath": "*",
			"Lhs": {
				"VarExpr": "supplierResp.rooms#0.rates#0.rate.finalRate.amount"
			},
			"Rhs": {
				"Const": {
					"NumConst": 0.9
				}
			}
		}
	}`

	// 创建规则
	ruleTarget, err := rule.NewRule("vip_business_rule", condition, aim)
	if err != nil {
		fmt.Printf("创建规则失败: %v\n", err)
		return
	}

	// 创建数据上下文
	dc, err := rule.ParseFactorsMap(context.Background(), typedef.MetaType{
		"user": map[string]interface{}{
			"id":       123,
			"name":     "VIP用户",
			"vipLevel": "VIP",
		},
		"masterHotel": map[string]interface{}{
			"id":   12345,
			"name": "北京希尔顿酒店",
			"star": 5,
		},
		"supplierResp": map[string]interface{}{
			"supplier": 1,
			"rooms": []map[string]interface{}{
				{
					"roomTypeId": "room_001",
					"roomName": map[string]interface{}{
						"en": "Deluxe Room",
					},
					"rates": []map[string]interface{}{
						{
							"ratePkgId": "rate_001",
							"rate": map[string]interface{}{
								"finalRate": map[string]interface{}{
									"amount":   1500.0,
									"currency": "USD",
								},
							},
						},
					},
				},
			},
		},
	})
	if err != nil {
		fmt.Printf("创建数据上下文失败: %v\n", err)
		return
	}

	// 执行规则
	rr := rule.ExecuteRule(context.Background(), ruleTarget, dc)
	if rr.Passed() {
		fmt.Println("VIP业务规则通过，应用折扣:")
		fmt.Printf("  折扣后价格: %.2f\n", rr.Aim().AsExpr())
	} else {
		fmt.Println("VIP业务规则未通过")
	}
}

// demoHotelRatesFlow 演示完整的HotelRates流程
func demoHotelRatesFlow() {
	fmt.Println("=== HotelRates 完整流程演示 ===")

	// 1. 模拟客户请求
	req := &protocol.HotelRatesReq{
		HotelId: 12345,
		CheckInOut: supplierDomain.CheckInOut{
			CheckIn:  types.DateInt(time.Now().AddDate(0, 0, 7).Unix()),  // 7天后入住
			CheckOut: types.DateInt(time.Now().AddDate(0, 0, 14).Unix()), // 14天后退房
		},
		GuestRoomOption: supplierDomain.GuestRoomOption{
			RoomCount:     1,
			AdultCount:    2,
			ChildrenCount: 0,
		},
	}

	fmt.Printf("客户请求: 酒店ID=%d, 入住=%s, 退房=%s, 房间数=%d, 成人=%d\n",
		req.HotelId,
		time.Unix(int64(req.CheckIn), 0).Format("2006-01-02"),
		time.Unix(int64(req.CheckOut), 0).Format("2006-01-02"),
		req.RoomCount,
		req.AdultCount,
	)

	// 2. 获取酒店信息
	hotel := &domain.Hotel{
		ID: 12345,
		HotelStaticProfile: supplierDomain.HotelStaticProfile{
			Name: i18n.I18N{En: "Beijing Hilton Hotel", Zh: "北京希尔顿酒店"},
			Star: 5,
		},
		HotelSupplierRef: []supplierDomain.HotelSupplierRef{
			{SupplierHotelID: "TBO_12345", Supplier: supplierDomain.Supplier_TBO},
			{SupplierHotelID: "DIDA_12345", Supplier: supplierDomain.Supplier_Dida},
		},
	}

	fmt.Printf("酒店信息: %s (星级: %v)\n", hotel.Name.En, hotel.Star)

	// 3. 创建买家解析器
	buyerResolver := createDemoBuyerResolver()

	// 4. 并发处理所有供应商
	var (
		supplierRatesRespList = make([]*supplierDomain.HotelRatesResp, 0)
		mu                    sync.Mutex
	)

	ctx := context.Background()
	sellers, err := buyerResolver.Sellers(ctx)
	if err != nil {
		log.Printf("获取供应商失败: %v", err)
		return
	}

	fmt.Printf("开始处理 %d 个供应商...\n", len(sellers))

	for _, seller := range sellers {
		// 模拟供应商请求处理
		supplierReq := &supplierDomain.HotelRatesReq{
			SupplierHotelId: "",
			HotelRegion:     supplierDomain.HotelRegion{},
			CheckInOut:      req.CheckInOut,
			GuestRoomOption: req.GuestRoomOption,
			CurrencyOption:  req.CurrencyOption,
		}

		// 应用卖家入规则
		applySellerInRule(ctx, seller, hotel, supplierReq)

		// 模拟供应商调用
		resp := simulateSupplierCall(ctx, supplierReq, seller)

		// 应用卖家出规则
		applySellerOutRule(ctx, seller, resp, hotel)

		// 添加到结果列表
		mu.Lock()
		supplierRatesRespList = append(supplierRatesRespList, resp)
		mu.Unlock()
	}

	// 5. 合并供应商响应
	rooms := mergeSupplierHotelRatesResp(supplierRatesRespList)

	// 6. 应用买家出规则
	applyBuyerOutRule(ctx, buyerResolver, rooms)

	fmt.Printf("最终结果: 获得 %d 个房间选项\n", len(rooms))
	for i, room := range rooms {
		fmt.Printf("  房间 %d: %s, 价格: %.2f %s\n",
			i+1,
			room.HotelRoomStaticProfile.RoomName.En,
			room.Rates[0].Rate.FinalRate.Amount,
			room.Rates[0].Rate.FinalRate.Currency,
		)
	}
}

// createDemoBuyerResolver 创建演示用的买家解析器
func createDemoBuyerResolver() BuyerResolver {
	return &DemoBuyerResolver{
		buyer: Participant{
			InNode:  &DemoInNode{ruleId: 1, credentialId: 100},
			OutNode: &DemoOutNode{ruleId: 2},
		},
		sellers: []Participant{
			{
				InNode:  &DemoInNode{ruleId: 3, credentialId: 101},
				OutNode: &DemoOutNode{ruleId: 4},
			},
			{
				InNode:  &DemoInNode{ruleId: 5, credentialId: 102},
				OutNode: &DemoOutNode{ruleId: 6},
			},
		},
	}
}

// 规则配置结构体
// 用于模拟从配置/DB加载规则
type RuleConfig struct {
	Condition string
	Aim       string
}

// 模拟规则配置表（ruleId -> 规则内容）
var demoRuleConfig = map[int64]RuleConfig{
	// 卖家入规则
	3: {
		Condition: `{"OpLogic": "&&", "Conditions": [{"Operator": ">", "Lhs": {"VarExpr": "masterHotel.star"}, "Rhs": {"Const": {"NumConst": 0}}}]}`,
		Aim:       `{"Const": {"StrConst": "TBO_12345"}}`,
	},
	5: {
		Condition: `{"OpLogic": "&&", "Conditions": [{"Operator": ">", "Lhs": {"VarExpr": "masterHotel.star"}, "Rhs": {"Const": {"NumConst": 0}}}]}`,
		Aim:       `{"Const": {"StrConst": "DIDA_12345"}}`,
	},
	// 卖家出规则
	4: {
		Condition: `{"OpLogic": "&&", "Conditions": [{"Operator": ">", "Lhs": {"VarExpr": "supplierResp.rooms#0.rates#0.rate.finalRate.amount"}, "Rhs": {"Const": {"NumConst": 0}}}]}`,
		Aim:       `{"Const": {"NumConst": 1.05}}`,
	},
	6: {
		Condition: `{"OpLogic": "&&", "Conditions": [{"Operator": ">", "Lhs": {"VarExpr": "supplierResp.rooms#0.rates#0.rate.finalRate.amount"}, "Rhs": {"Const": {"NumConst": 0}}}]}`,
		Aim:       `{"Const": {"NumConst": 1.08}}`,
	},
	// 买家出规则
	2: {
		Condition: `{"OpLogic": "&&", "Conditions": [{"Operator": ">", "Lhs": {"VarExpr": "rooms#0.rates#0.rate.finalRate.amount"}, "Rhs": {"Const": {"NumConst": 0}}}]}`,
		Aim:       `{"Const": {"NumConst": 1.02}}`,
	},
}

// 工具函数：struct转map
func structToMap(obj interface{}) map[string]interface{} {
	data, _ := json.Marshal(obj)
	var m map[string]interface{}
	json.Unmarshal(data, &m)
	return m
}

// 工具函数：rooms切片转[]map
func roomsToSliceMap(rooms []supplierDomain.Room) []map[string]interface{} {
	var out []map[string]interface{}
	for _, r := range rooms {
		data, _ := json.Marshal(r)
		var m map[string]interface{}
		json.Unmarshal(data, &m)
		out = append(out, m)
	}
	return out
}

func applySellerInRule(ctx context.Context, seller Participant, hotel *domain.Hotel, req *supplierDomain.HotelRatesReq) {
	ruleId, err := seller.InNode.GetAggregatedRuleId(ctx)
	if err != nil {
		log.Printf("[规则ID获取失败] err=%v", err)
		return
	}

	cfg, ok := demoRuleConfig[ruleId]
	if !ok {
		log.Printf("[规则配置缺失] sellerInRuleId=%d", ruleId)
		return
	}

	// 记录应用规则前的状态
	beforeSupplierHotelId := req.SupplierHotelId

	ruleTarget, err := rule.NewRule("seller_in_rule", cfg.Condition, cfg.Aim)
	if err != nil {
		log.Printf("[规则创建失败] sellerInRuleId=%d, err=%v", ruleId, err)
		return
	}
	dc, err := rule.ParseFactorsMap(ctx, typedef.MetaType{
		"masterHotel": structToMap(hotel),
	})
	if err != nil {
		log.Printf("[上下文创建失败] sellerInRuleId=%d, err=%v", ruleId, err)
		return
	}
	rr := rule.ExecuteRule(ctx, ruleTarget, dc)
	if rr.Passed() {
		supplierHotelId := rr.Aim().AsExpr().(string)
		req.SupplierHotelId = supplierHotelId
		log.Printf("[规则通过] sellerInRuleId=%d, supplierHotelId=%s", ruleId, supplierHotelId)
		log.Printf("[规则DIFF] sellerInRuleId=%d: supplierHotelId [%s] -> [%s]", ruleId, beforeSupplierHotelId, req.SupplierHotelId)
	} else {
		log.Printf("[规则未通过] sellerInRuleId=%d, aim=%v, condition=%v, ctx=%+v", ruleId, cfg.Aim, cfg.Condition, hotel)
	}
}

func applySellerOutRule(ctx context.Context, seller Participant, resp *supplierDomain.HotelRatesResp, hotel *domain.Hotel) {
	ruleId, err := seller.OutNode.GetAggregatedRuleId(ctx)
	if err != nil {
		log.Printf("[规则ID获取失败] err=%v", err)
		return
	}

	cfg, ok := demoRuleConfig[ruleId]
	if !ok {
		log.Printf("[规则配置缺失] sellerOutRuleId=%d", ruleId)
		return
	}

	// 记录应用规则前的价格状态
	var beforePrices []float64
	for _, room := range resp.Rooms {
		for _, rate := range room.Rates {
			beforePrices = append(beforePrices, rate.Rate.FinalRate.Amount)
		}
	}

	ruleTarget, err := rule.NewRule("seller_out_rule", cfg.Condition, cfg.Aim)
	if err != nil {
		log.Printf("[规则创建失败] sellerOutRuleId=%d, err=%v", ruleId, err)
		return
	}
	dc, err := rule.ParseFactorsMap(ctx, typedef.MetaType{
		"supplierResp": structToMap(resp),
		"masterHotel":  structToMap(hotel),
	})
	if err != nil {
		log.Printf("[上下文创建失败] sellerOutRuleId=%d, err=%v", ruleId, err)
		return
	}
	rr := rule.ExecuteRule(ctx, ruleTarget, dc)
	if rr.Passed() {
		markup := rr.Aim().AsExpr().(float64)
		log.Printf("[规则通过] sellerOutRuleId=%d, markup=%.2f", ruleId, markup)

		// 应用价格调整
		priceIndex := 0
		for i := range resp.Rooms {
			for j := range resp.Rooms[i].Rates {
				if priceIndex < len(beforePrices) {
					beforePrice := beforePrices[priceIndex]
					resp.Rooms[i].Rates[j].Rate.FinalRate.Amount = beforePrice * markup
					log.Printf("[价格调整] room=%d, rate=%d: %.2f -> %.2f (markup=%.2f)",
						i, j, beforePrice, resp.Rooms[i].Rates[j].Rate.FinalRate.Amount, markup)
					priceIndex++
				}
			}
		}
	} else {
		log.Printf("[规则未通过] sellerOutRuleId=%d, aim=%v, condition=%v", ruleId, cfg.Aim, cfg.Condition)
	}
}

func applyBuyerOutRule(ctx context.Context, buyerResolver BuyerResolver, rooms []supplierDomain.Room) {
	ruleId, err := buyerResolver.Buyer().OutNode.GetAggregatedRuleId(ctx)
	if err != nil {
		log.Printf("[规则ID获取失败] err=%v", err)
		return
	}

	cfg, ok := demoRuleConfig[ruleId]
	if !ok {
		log.Printf("[规则配置缺失] buyerOutRuleId=%d", ruleId)
		return
	}

	// 记录应用规则前的价格状态
	var beforePrices []float64
	for _, room := range rooms {
		for _, rate := range room.Rates {
			beforePrices = append(beforePrices, rate.Rate.FinalRate.Amount)
		}
	}

	ruleTarget, err := rule.NewRule("buyer_out_rule", cfg.Condition, cfg.Aim)
	if err != nil {
		log.Printf("[规则创建失败] buyerOutRuleId=%d, err=%v", ruleId, err)
		return
	}
	dc, err := rule.ParseFactorsMap(ctx, typedef.MetaType{
		"rooms": roomsToSliceMap(rooms),
	})
	if err != nil {
		log.Printf("[上下文创建失败] buyerOutRuleId=%d, err=%v", ruleId, err)
		return
	}
	rr := rule.ExecuteRule(ctx, ruleTarget, dc)
	if rr.Passed() {
		markup := rr.Aim().AsExpr().(float64)
		log.Printf("[规则通过] buyerOutRuleId=%d, markup=%.2f", ruleId, markup)

		// 应用价格调整
		priceIndex := 0
		for i := range rooms {
			for j := range rooms[i].Rates {
				if priceIndex < len(beforePrices) {
					beforePrice := beforePrices[priceIndex]
					rooms[i].Rates[j].Rate.FinalRate.Amount = beforePrice * markup
					log.Printf("[价格调整] room=%d, rate=%d: %.2f -> %.2f (markup=%.2f)",
						i, j, beforePrice, rooms[i].Rates[j].Rate.FinalRate.Amount, markup)
					priceIndex++
				}
			}
		}
	} else {
		log.Printf("[规则未通过] buyerOutRuleId=%d, aim=%v, condition=%v", ruleId, cfg.Aim, cfg.Condition)
	}
}

// simulateSupplierCall 模拟供应商调用
func simulateSupplierCall(ctx context.Context, req *supplierDomain.HotelRatesReq, seller Participant) *supplierDomain.HotelRatesResp {
	// 获取认证信息
	credentialId, err := seller.InNode.GetCredentialId(ctx)
	if err != nil {
		log.Printf("[认证ID获取失败] err=%v", err)
		return &supplierDomain.HotelRatesResp{}
	}

	log.Printf("[模拟供应商调用] credentialId=%d, supplierHotelId=%s", credentialId, req.SupplierHotelId)

	// 模拟供应商响应
	return &supplierDomain.HotelRatesResp{
		Rooms: []supplierDomain.Room{
			{
				HotelRoomStaticProfile: supplierDomain.HotelRoomStaticProfile{
					RoomName: i18n.I18N{En: "Standard Room"},
				},
				Rates: []supplierDomain.RoomRatePkg{
					{
						Rate: supplierDomain.Rate{
							FinalRate: money.Money{
								Amount:   100.0,
								Currency: "USD",
							},
						},
					},
				},
			},
		},
	}
}

// mergeSupplierHotelRatesResp 合并供应商响应
func mergeSupplierHotelRatesResp(responses []*supplierDomain.HotelRatesResp) []supplierDomain.Room {
	var allRooms []supplierDomain.Room
	for _, resp := range responses {
		if resp != nil && resp.Rooms != nil {
			allRooms = append(allRooms, resp.Rooms...)
		}
	}
	return allRooms
}

// demoLinkTechnologyArchitecture 演示Link Technology架构
func demoLinkTechnologyArchitecture() {
	fmt.Println("=== Link Technology 架构演示 ===")
	fmt.Println("Link Technology 是一个基于规则引擎的微服务架构，用于处理多供应商的酒店预订业务。")
	fmt.Println()
	fmt.Println("核心组件:")
	fmt.Println("1. 买家解析器 (BuyerResolver): 负责获取可用的供应商列表")
	fmt.Println("2. 参与者 (Participant): 包含入节点和出节点，每个节点可以配置规则")
	fmt.Println("3. 规则引擎: 基于 arishem 库，支持复杂的条件判断和动作执行")
	fmt.Println("4. 供应商工厂: 统一管理不同供应商的接口调用")
	fmt.Println()
	fmt.Println("处理流程:")
	fmt.Println("1. 客户端发送请求到买家入节点")
	fmt.Println("2. 买家入节点应用买家前置规则")
	fmt.Println("3. 请求传递到卖家入节点")
	fmt.Println("4. 卖家入节点应用卖家前置规则")
	fmt.Println("5. 调用供应商接口")
	fmt.Println("6. 供应商返回原始响应")
	fmt.Println("7. 卖家出节点应用卖家响应规则")
	fmt.Println("8. 响应传递到买家出节点")
	fmt.Println("9. 买家出节点应用买家后置规则")
	fmt.Println("10. 返回最终结果给客户端")
}

// DemoInNode 演示用的入节点
type DemoInNode struct {
	ruleId       int64
	credentialId int64
}

func (d *DemoInNode) GetAggregatedRuleId(ctx context.Context) (int64, error) {
	return d.ruleId, nil
}

func (d *DemoInNode) GetCredentialId(ctx context.Context) (int64, error) {
	return d.credentialId, nil
}

// DemoOutNode 演示用的出节点
type DemoOutNode struct {
	ruleId int64
}

func (d *DemoOutNode) GetAggregatedRuleId(ctx context.Context) (int64, error) {
	return d.ruleId, nil
}

// DemoBuyerResolver 演示用的买家解析器
type DemoBuyerResolver struct {
	buyer   Participant
	sellers []Participant
}

func (d *DemoBuyerResolver) Sellers(ctx context.Context) ([]Participant, error) {
	return d.sellers, nil
}

func (d *DemoBuyerResolver) Buyer() Participant {
	return d.buyer
}

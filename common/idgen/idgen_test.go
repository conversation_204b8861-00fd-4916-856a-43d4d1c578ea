package idgen

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestString2Int64(t *testing.T) {
	key := fmt.Sprintf("%d:%s:%s", 2, "大酒店", "<PERSON><PERSON>")
	t.Log(String2Int64(key))
}

func TestString2Int64_JavaScriptSafe(t *testing.T) {
	// JavaScript Number.MAX_SAFE_INTEGER = 2^53-1 = 9007199254740991
	maxSafeInt := int64(1<<53 - 1)

	testCases := []string{
		"test_hotel_1",
		"trip_123456789",
		"dida_987654321",
		"expedia_555666777",
		"very_long_hotel_id_string_for_testing_purposes",
		"",
		"a",
		"ab",
		"abc",
		"abcd",
		"abcde",
		"abcdef",
		"abcdefg",
		"abcdefgh",
		"abcdefghi",
		"abcdefghij",
	}

	for _, tc := range testCases {
		result := String2Int64(tc)

		// 验证结果不超过JavaScript安全整数范围
		assert.LessOrEqual(t, result, maxSafeInt, "ID should not exceed JavaScript Number.MAX_SAFE_INTEGER for input: %s", tc)

		// 验证结果为正数
		assert.Greater(t, result, int64(0), "ID should be positive for input: %s", tc)

		// 验证结果不为0
		assert.NotEqual(t, result, int64(0), "ID should not be zero for input: %s", tc)
	}
}

func TestString2Int64ID_JavaScriptSafe(t *testing.T) {
	// JavaScript Number.MAX_SAFE_INTEGER = 2^53-1 = 9007199254740991
	maxSafeInt := int64(1<<53 - 1)

	testCases := []string{
		"test_hotel_1",
		"trip_123456789",
		"dida_987654321",
	}

	for _, tc := range testCases {
		result := String2Int64ID(tc)

		// 验证结果不超过JavaScript安全整数范围
		assert.LessOrEqual(t, int64(result), maxSafeInt, "ID should not exceed JavaScript Number.MAX_SAFE_INTEGER for input: %s", tc)

		// 验证结果为正数
		assert.Greater(t, int64(result), int64(0), "ID should be positive for input: %s", tc)
	}
}

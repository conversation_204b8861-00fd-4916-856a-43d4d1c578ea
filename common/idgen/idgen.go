package idgen

import (
	"hash/crc32"
	"hash/fnv"
	"time"

	"github.com/sony/sonyflake"

	"hotel/common/types"
)

var (
	_flake *sonyflake.Sonyflake
)

func init() {
	_flake = sonyflake.NewSonyflake(sonyflake.Settings{
		StartTime: time.Date(2025, time.April, 13, 0, 0, 0, 0, time.UTC),
	})
}

func NextInt64ID() (types.ID, error) {
	id, err := _flake.NextID()
	if err != nil {
		return 0, err
	}
	return types.ID(int64(id)), nil
}

func MustNextInt64ID() types.ID {
	id, err := NextInt64ID()
	if err != nil {
		return types.ID(time.Now().UnixNano())
	}
	return types.ID(id)
}

func String2Int64(s string) int64 {
	h := fnv.New64a()
	_, _ = h.Write([]byte(s))
	hash := h.Sum64()

	// JavaScript Number.MAX_SAFE_INTEGER = 2^53-1 = 9007199254740991
	maxSafeInt := int64(1<<53 - 1)

	// 使用hash的低53位，确保不超过JavaScript安全整数范围
	out := int64(hash & ((1 << 53) - 1))

	// 如果结果为0，使用CRC32作为备选方案
	if out == 0 {
		out = int64(crc32.ChecksumIEEE([]byte(s)))
		out = out & maxSafeInt
		if out == 0 {
			out = 1 // 避免返回0
		}
	}

	return out
}

func String2Int64ID(s string) types.ID {
	return types.ID(String2Int64(s))
}

package domain

import (
	"context"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"hotel/common/fanout"
	"hotel/common/log"
	"hotel/common/metrics"
)

var (
	GlobalHBLogChan = make(chan *HBLog, 1000)
	// 防拥塞相关统计
	droppedLogsCount int64
	channelFullCount int64
	backupFan        = fanout.New("GlobalHBLogChan backup fanout")

	// 预构造的 metrics 实例 - 采用嵌套结构命名
	hbAPICallsProm            = metrics.New().WithCounter("hb_api_calls_total", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "error_name"})
	hbAPIErrorsProm           = metrics.New().WithCounter("hb_api_errors_total", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "error_name"})
	hbAPIHttpStatusProm       = metrics.New().WithCounter("hb_api_http_status_total", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "http_status"})
	hbAPIDurationProm         = metrics.New().WithTimer("hb_api_duration_ms", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "error_name"})
	hbAPIInternalDurationProm = metrics.New().WithTimer("hb_api_internal_duration_ms", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "error_name"})
	hbAPIRequestSizeProm      = metrics.New().WithTimer("hb_api_request_size_bytes", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "error_name"})
	hbAPIResponseSizeProm     = metrics.New().WithTimer("hb_api_response_size_bytes", []string{"api_out_supplier", "api_out_path", "api_out_biz_type", "api_in_path", "error_name"})
)

// 获取被丢弃的日志数量
func GetDroppedLogsCount() int64 {
	return atomic.LoadInt64(&droppedLogsCount)
}

// 获取 channel 满的次数
func GetChannelFullCount() int64 {
	return atomic.LoadInt64(&channelFullCount)
}

// 重置统计计数器
func ResetLogStats() {
	atomic.StoreInt64(&droppedLogsCount, 0)
	atomic.StoreInt64(&channelFullCount, 0)
}
func (h *HBLog) Print(ctx context.Context) {
	// 构建日志消息
	logMsg := "HBLog"
	if h.BizError != nil {
		logMsg += " [ERROR]"
	}

	// 构建关键信息
	fields := []string{
		"logId=" + h.LogId,
		"sessionId=" + h.SessionId,
	}

	// 添加 API 信息
	if h.APIIn != nil {
		fields = append(fields, "apiInPath="+h.APIIn.Path)
		fields = append(fields, "apiInBizType="+h.APIIn.BizType)
		fields = append(fields, "apiInBizId="+h.APIIn.BizId)
	}
	if h.APIOut != nil {
		fields = append(fields, "apiOutPath="+h.APIOut.Path)
		fields = append(fields, "apiOutBizType="+h.APIOut.BizType)
		fields = append(fields, "apiOutBizId="+h.APIOut.BizId)
		fields = append(fields, "apiOutSupplier="+h.APIOut.Supplier)
	}

	fields = append(fields, "costTime="+h.CostTime.String())

	if h.UserId > 0 {
		fields = append(fields, "userId="+fmt.Sprintf("%d", h.UserId))
	}
	if h.SellerEntityId > 0 {
		fields = append(fields, "sellerEntityId="+fmt.Sprintf("%d", h.SellerEntityId))
	}
	if h.BuyerEntityId > 0 {
		fields = append(fields, "buyerEntityId="+fmt.Sprintf("%d", h.BuyerEntityId))
	}

	// 添加业务错误信息
	if h.BizError != nil {
		fields = append(fields, "bizErrorCode="+h.BizError.Error())
	}

	// 添加输入输出信息
	if h.Input != nil {
		fields = append(fields, "inputBodySize="+fmt.Sprintf("%d", h.Input.BodySize))
		fields = append(fields, "inputBody=("+h.Input.Body+")")
	}
	if h.Output != nil {
		fields = append(fields, "outputBodySize="+fmt.Sprintf("%d", h.Output.BodySize))
		fields = append(fields, "httpStatusCode="+fmt.Sprintf("%d", h.Output.HttpStatusCode))
		fields = append(fields, "outputCostTime="+h.Output.CostTime.String())
		fields = append(fields, "outputBody=("+h.Output.Body+")")
	}

	// 添加自定义键值对
	for k, v := range h.KVs {
		fields = append(fields, k+"="+fmt.Sprintf("%v", v))
	}

	// 打印日志
	logMessage := strings.Join(fields, " ")
	if h.BizError != nil {
		log.Errorc(ctx, logMsg+": "+logMessage)
	} else {
		log.Infoc(ctx, logMsg+": "+logMessage)
	}
}

func (h *HBLog) Emit(ctx context.Context) {
	h.Print(ctx)
	// 上报 metrics: only once
	h.emitMetrics(ctx)

	// 防拥塞：非阻塞发送到 channel
	h.sendToChannelNonBlocking(ctx)
}

// sendToChannelNonBlocking 非阻塞方式发送日志到 channel
func (h *HBLog) sendToChannelNonBlocking(ctx context.Context) {
	select {
	case GlobalHBLogChan <- h:
		// 成功发送到 channel
		return
	default:
		// channel 已满，记录统计并降级处理
		atomic.AddInt64(&channelFullCount, 1)
		atomic.AddInt64(&droppedLogsCount, 1)

		// 记录警告日志
		log.Warnc(ctx, fmt.Sprintf("HBLog channel full, dropped log: logId=%s, sessionId=%s",
			h.LogId, h.SessionId))

		// 可选：尝试异步重试一次（使用 goroutine 避免阻塞）
		_ = backupFan.Do(ctx, func(ctx context.Context) {
			h.retrySendToChannel()
		})
	}
}

// retrySendToChannel 异步重试发送到 channel
func (h *HBLog) retrySendToChannel() {
	// 等待一小段时间后重试
	time.Sleep(10 * time.Millisecond)

	select {
	case GlobalHBLogChan <- h:
		// 重试成功，减少丢弃计数
		atomic.AddInt64(&droppedLogsCount, -1)
		return
	default:
		// 重试失败，记录最终丢弃
		log.Error("HBLog retry failed, permanently dropped: logId=%s", h.LogId)
	}
}

func (h *HBLog) emitMetrics(ctx context.Context) {
	supplier, apiPath, bizType, apiInPath := "", "", "", ""
	if h.APIOut != nil {
		supplier = h.APIOut.Supplier
		apiPath = h.APIOut.Path
		bizType = h.APIOut.BizType
	}
	if h.APIIn != nil {
		apiInPath = h.APIIn.Path
	}
	errorCode := ""
	if h.BizError != nil {
		errorCode = h.BizError.Name()
	}
	labels := []string{supplier, apiPath, bizType, apiInPath, errorCode}

	hbAPICallsProm.Incr(ctx, labels...)
	hbAPIDurationProm.Timing(ctx, h.CostTime.Milliseconds(), labels...)
	if h.BizError != nil {
		hbAPIErrorsProm.Incr(ctx, labels...)
	}
	if h.Output != nil {
		statusLabels := []string{supplier, apiPath, bizType, apiInPath, fmt.Sprintf("%d", h.Output.HttpStatusCode)}
		hbAPIHttpStatusProm.Incr(ctx, statusLabels...)
	}
	if h.Input != nil && h.Input.BodySize > 0 {
		hbAPIRequestSizeProm.Timing(ctx, h.Input.BodySize, labels...)
	}
	if h.Output != nil && h.Output.BodySize > 0 {
		hbAPIResponseSizeProm.Timing(ctx, h.Output.BodySize, labels...)
	}
	if h.Output != nil && h.Output.InternalCostTime > 0 {
		hbAPIInternalDurationProm.Timing(ctx, int64(h.Output.InternalCostTime.Milliseconds()), labels...)
	}
}

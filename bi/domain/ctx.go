package domain

import (
	"context"
)

const (
	_logKey = "HBLog"
)

func HBLogInit(ctx context.Context) context.Context {
	return HBLogSet(ctx, NewHBLog())
}

func HBLogGetOrInit(ctx context.Context) (context.Context, *HBLog) {
	v := ctx.Value(_logKey)
	if v == nil {
		l := NewHBLog()
		ctx = HBLogSet(ctx, l)
		return ctx, l
	}
	return ctx, v.(*HBLog)
}

func HBLogCreateSpan(ctx context.Context) (context.Context, *HBLog) {
	v := ctx.Value(_logKey)
	if v == nil {
		l := NewHBLog()
		ctx = HBLogSet(ctx, l)
		return ctx, l
	}
	vv := v.(*HBLog).CreateSpan()
	ctx = HBLogSet(ctx, vv)
	return ctx, vv
}

func HBLogGet(ctx context.Context) *HBLog {
	v := ctx.Value(_logKey)
	if v == nil {
		return NewHBLog()
	}
	return v.(*HBLog)
}

func HBLogSet(ctx context.Context, in *HBLog) context.Context {
	return context.WithValue(ctx, _logKey, in)
}

// todo: utils 方法，用于方便地往 context 里插入 hbLog 的字段，待补充

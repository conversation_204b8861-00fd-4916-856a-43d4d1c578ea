package domain

import (
	"time"

	"hotel/common/bizerr"
)

// NewHBLog 创建一个新的 HBLog 实例
func NewHBLog() *HBLog {
	return &HBLog{
		Timestamp: time.Now(),
		KVs:       make(map[string]any),
		APIIn:     &APIIn{},
		APIOut:    &APIOut{},
		Input: &Input{
			Header:     make(map[string]string),
			Credential: make(map[string]string),
		},
		Output: &Output{
			Header: make(map[string]string),
		},
	}
}

type HBLog struct {
	Timestamp      time.Time        `json:"timestamp"`
	KVs            map[string]any   `json:"kvs"`
	UserId         int64            `json:"userId"`
	LogId          string           `json:"logId"`
	SessionId      string           `json:"sessionId"`
	SellerEntityId int64            `json:"sellerEntityId"`
	BuyerEntityId  int64            `json:"buyerEntityId"`
	APIIn          *APIIn           `json:"apiIn"`
	APIOut         *APIOut          `json:"apiOut"`
	Input          *Input           `json:"input"`  // request body + header, JSON
	Output         *Output          `json:"output"` // response
	BizError       *bizerr.BizError `json:"bizError"`
	CostTime       time.Duration    `json:"costTime"`
}

func (h *HBLog) CreateSpan() *HBLog {
	out := NewHBLog()
	out.Timestamp = time.Now()
	out.KVs = h.KVs
	out.UserId = h.UserId
	out.LogId = h.LogId
	out.SessionId = h.SessionId
	out.SellerEntityId = h.SellerEntityId
	out.BuyerEntityId = h.BuyerEntityId
	return out
}

func (h *HBLog) AddKv(key string, value any) {
	h.KVs[key] = value
}

type APIIn struct {
	Path    string `json:"path"` // HotelByte的入口 API
	BizType string `json:"bizType"`
	BizId   string `json:"bizId"`
}

type APIOut struct {
	Path     string `json:"path"`
	BizType  string `json:"bizType"`
	BizId    string `json:"bizId"`
	Supplier string `json:"supplier"` // HotelByte的出口API 提供方（供应商）
}

type Input struct {
	Header     map[string]string `json:"header"`
	Body       string            `json:"body"`
	BodySize   int64             `json:"bodySize"`
	Credential map[string]string `json:"credential"`
}

type Output struct {
	Header           map[string]string `json:"header"`           // 供应商返回的 http headers
	Body             string            `json:"body"`             // 供应商返回的 body
	BodySize         int64             `json:"bodySize"`         // 供应商返回的 body
	CostTime         time.Duration     `json:"costTime"`         // 我方视角的供应商接口耗时
	InternalCostTime time.Duration     `json:"InternalCostTime"` // 供应商内部视角的接口耗时
	HttpStatusCode   int               `json:"httpStatusCode"`   // 供应商返回的 HTTP 状态码
}

type SupplierBizType = string

const (
	BizType_City         SupplierBizType = "city"
	BizType_Hotel        SupplierBizType = "hotel"
	BizType_Country      SupplierBizType = "country"
	BizType_HotelRatePkg SupplierBizType = "hotelRatePkg"
	BizType_Order        SupplierBizType = "order"
)

// LogQuery defines the query parameters for searching logs.
type LogQuery struct {
	StartTime            time.Time     `json:"startTime,omitempty"`
	EndTime              time.Time     `json:"endTime,omitempty"`
	LogId                string        `json:"logId,omitempty"`
	SessionId            string        `json:"sessionId,omitempty"`
	UserId               int64         `json:"userId,omitempty"`
	SellerEntityId       int64         `json:"sellerEntityId,omitempty"`
	BuyerEntityId        int64         `json:"buyerEntityId,omitempty"`
	SupplierBizId        string        `json:"supplierBizId,omitempty"`
	SupplierBizType      int           `json:"supplierBizType,omitempty"`
	ApiInPath            string        `json:"apiInPath,omitempty"`
	ApiOutSupplier       string        `json:"apiOutSupplier,omitempty"`
	ApiOutPath           string        `json:"apiOutPath,omitempty"`
	BizErrorCode         string        `json:"bizErrorCode,omitempty"`
	InputHeaderKey       string        `json:"inputHeaderKey,omitempty"`
	InputHeaderValue     string        `json:"inputHeaderValue,omitempty"`
	InputCredentialKey   string        `json:"inputCredentialKey,omitempty"`
	InputCredentialValue string        `json:"inputCredentialValue,omitempty"`
	InputBodyKeyword     string        `json:"inputBodyKeyword,omitempty"`
	OutputBodyKeyword    string        `json:"outputBodyKeyword,omitempty"`
	OutputHeaderKey      string        `json:"outputHeaderKey,omitempty"`
	OutputHeaderValue    string        `json:"outputHeaderValue,omitempty"`
	OutputHttpStatusCode int           `json:"outputHttpStatusCode,omitempty"`
	Page                 int           `json:"page"`
	PageSize             int           `json:"pageSize"`
	CostTimeMax          time.Duration `json:"costTimeMax,omitempty"`
	CostTimeMin          time.Duration `json:"costTimeMin,omitempty"`
}

// QueryResult holds the result of a log query.
type QueryResult struct {
	Logs  []*HBLog `json:"logs"`
	Total int64    `json:"total"`
}

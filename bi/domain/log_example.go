package domain

import (
	"context"
	"time"

	"hotel/common/bizerr"
)

// ExampleUsage 展示如何使用 HBLog 进行日志记录和指标上报
func ExampleUsage() {
	ctx := context.Background()

	// 模拟一个酒店查询请求
	startTime := time.Now()

	// 模拟请求处理
	time.Sleep(100 * time.Millisecond) // 模拟处理时间

	// 创建日志记录
	log := &HBLog{
		Timestamp: time.Now(),
		KVs: map[string]any{
			"hotel_count": 10,
			"city_code":   "PEK",
			"check_in":    "2025-06-28",
			"check_out":   "2025-06-29",
		},
		UserId:    12345,
		LogId:     "log_" + time.Now().Format("20060102150405"),
		SessionId: "session_" + time.Now().Format("20060102150405"),
		APIIn: &APIIn{
			Path:    "/api/hotel/search",
			BizType: "hotel",
			BizId:   "hotel_search_001",
		},
		APIOut: &APIOut{
			Path:     "/hotel/list",
			BizType:  "hotel",
			BizId:    "dida_hotel_search_001",
			Supplier: "dida",
		},
		Input: &Input{
			Header: map[string]string{
				"Content-Type": "application/json",
				"User-Agent":   "HotelByte/1.0",
			},
			Body:     `{"cityCode":"PEK","checkIn":"2025-06-28","checkOut":"2025-06-29"}`,
			BodySize: 89,
			Credential: map[string]string{
				"api_key": "dida_api_key_123",
			},
		},
		Output: &Output{
			Header: map[string]string{
				"Content-Type": "application/json",
				"Server":       "DidaAPI/1.0",
			},
			Body:             `{"hotels":[{"id":123,"name":"Test Hotel"}]}`,
			BodySize:         45,
			CostTime:         80 * time.Millisecond,
			InternalCostTime: 60 * time.Millisecond,
			HttpStatusCode:   200,
		},
		CostTime: time.Since(startTime),
	}

	// 发送日志和指标
	log.Emit(ctx)
}

// ExampleErrorUsage 展示错误情况下的日志记录
func ExampleErrorUsage() {
	ctx := context.Background()

	startTime := time.Now()

	// 模拟一个失败的请求
	time.Sleep(50 * time.Millisecond)

	// 创建错误日志记录
	log := &HBLog{
		Timestamp: time.Now(),
		KVs: map[string]any{
			"hotel_id":   "12345",
			"error_type": "supplier_timeout",
		},
		UserId:    12345,
		LogId:     "log_error_" + time.Now().Format("20060102150405"),
		SessionId: "session_error_" + time.Now().Format("20060102150405"),
		APIIn: &APIIn{
			Path:    "/api/hotel/detail",
			BizType: "hotel",
			BizId:   "hotel_detail_001",
		},
		APIOut: &APIOut{
			Path:     "/hotel/detail",
			BizType:  "hotel",
			BizId:    "dida_hotel_detail_001",
			Supplier: "dida",
		},
		Input: &Input{
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			Body:     `{"hotelId":"12345"}`,
			BodySize: 20,
		},
		Output: &Output{
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			Body:             `{"error":"timeout"}`,
			BodySize:         20,
			CostTime:         30 * time.Second, // 超时
			InternalCostTime: 30 * time.Second,
			HttpStatusCode:   408, // Request Timeout
		},
		BizError: bizerr.New(408, "供应商接口超时"),
		CostTime: time.Since(startTime),
	}

	// 发送错误日志和指标
	log.Emit(ctx)
}

// ExampleBusinessErrorUsage 展示业务错误情况
func ExampleBusinessErrorUsage() {
	ctx := context.Background()

	startTime := time.Now()

	// 模拟一个业务错误
	time.Sleep(20 * time.Millisecond)

	log := &HBLog{
		Timestamp: time.Now(),
		KVs: map[string]any{
			"rate_plan_id": "rate_plan_123",
			"error_reason": "insufficient_inventory",
		},
		UserId:    12345,
		LogId:     "log_biz_error_" + time.Now().Format("20060102150405"),
		SessionId: "session_biz_error_" + time.Now().Format("20060102150405"),
		APIIn: &APIIn{
			Path:    "/api/hotel/check_avail",
			BizType: "hotelRatePkg",
			BizId:   "rate_check_001",
		},
		APIOut: &APIOut{
			Path:     "/rate/check_avail",
			BizType:  "hotelRatePkg",
			BizId:    "dida_rate_check_001",
			Supplier: "dida",
		},
		Input: &Input{
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			Body:     `{"ratePlanId":"rate_plan_123","checkIn":"2025-06-28","checkOut":"2025-06-29"}`,
			BodySize: 85,
		},
		Output: &Output{
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			Body:             `{"available":false,"reason":"insufficient_inventory"}`,
			BodySize:         50,
			CostTime:         15 * time.Millisecond,
			InternalCostTime: 10 * time.Millisecond,
			HttpStatusCode:   200, // HTTP 成功，但业务失败
		},
		BizError: bizerr.New(1001, "库存不足"),
		CostTime: time.Since(startTime),
	}

	// 发送业务错误日志和指标
	log.Emit(ctx)
}

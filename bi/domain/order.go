package domain

import (
	"time"

	"hotel/supplier/domain"
)

// Q1: rebook前后的两个订单，应该如何统计？
type BIOrder struct {
	ID     int64 `json:"id"`
	Status int64 `json:"status"`
	// SmartBook: "ErrorRecovery", "ProfitGen"
	Tags             []string        `json:"tags"`
	Supplier         domain.Supplier `json:"supplier"`
	CreateTime       time.Time       `json:"create_time"`
	TenantEntityID   int64           `json:"tenant_entity_id"`
	CustomerEntityID int64           `json:"customer_entity_id"`
	// money
	ProfitGenAmount   float64 `json:"profit_gen_amount"`
	ProfitGenCurrency float64 `json:"profit_gen_currency"`
}

// SmartBook Overview
// Processed-bookings count(order)
// Smartbook-ProfitGen count(order)
// Smartbook-ErrorRecovery count(order)
// Processed-Bookings sum(order.xx_amount)
// Optimized-ProfitGen sum(order.profit_gen_amount)
// Saved-ErrorRecovery sum(order.xx_amount)

// ReBook Overview
// Processed-bookings count(order)
// Rebook-ProfitGen count(order)
// Processed-Bookings sum(order.xx_amount)
// Optimized-ProfitGen sum(order.profit_gen_amount)
// need transfer records, old_supplier -> new_supplier

// Know your clients
// 1. configuration
// -- roomMappingVersion
// -- smartBookMode - [ProfitGen,...]
// -- dynamicMarkups - [disabled/enabled]
// -- bestPackages - [disabled/enabled]
// -- 2FA - [disabled/enabled]
// -- booking count of suppliers
// -- booking volume of suppliers
// 2.1 request
// -- search type(geo,...) count
// -- list search count
// -- detail search count
// -- checkAvail count
// 2.2 response
// -- success/failure
// -- duration (avg,p50,p95,...)

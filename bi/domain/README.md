# HBLog - HotelByte 业务日志系统

HBLog 是一个用于记录 HotelByte 与供应商交互的完整日志系统，支持结构化日志记录和指标上报。

## 功能特性

- **结构化日志记录**：记录完整的请求/响应信息
- **指标上报**：自动上报到 Prometheus 监控系统
- **错误追踪**：支持业务错误和系统错误的区分
- **性能监控**：记录接口耗时、请求/响应大小等指标
- **灵活查询**：支持多种维度的日志查询

## 核心组件

### HBLog 结构体

```go
type HBLog struct {
    Timestamp       time.Time        // 日志时间戳
    KVs             map[string]any   // 自定义键值对
    UserId          int64            // 用户ID
    LogId           string           // 日志ID
    SessionId       string           // 会话ID
    SellerEntityId  int64            // 卖家实体ID
    BuyerEntityId   int64            // 买家实体ID
    SupplierBizId   string           // 供应商业务ID
    SupplierBizType SupplierBizType  // 供应商业务类型
    APIInPath       string           // HotelByte入口API
    APIOutPath      string           // HotelByte出口API（供应商接口名）
    APIOutSupplier  string           // HotelByte出口API提供方（供应商）
    Input           *Input           // 请求信息
    Output          *Output          // 响应信息
    BizError        *bizerr.BizError // 业务错误
    CostTime        time.Duration    // 总耗时
}
```

### 业务类型

```go
const (
    SupplierBizType_City         = "city"         // 城市相关
    SupplierBizType_Hotel        = "hotel"        // 酒店相关
    SupplierBizType_HotelRatePkg = "hotelRatePkg" // 酒店价格包
    SupplierBizType_Order        = "order"        // 订单相关
)
```

## 使用方法

### 基本使用

```go
import (
    "context"
    "time"
    "hotel/bi/domain"
    "hotel/common/bizerr"
)

func example() {
    ctx := context.Background()
    startTime := time.Now()
    
    // 模拟业务处理
    // ... 业务逻辑 ...
    
    // 创建日志记录
    log := &domain.HBLog{
        Timestamp:       time.Now(),
        LogId:           "log_" + time.Now().Format("20060102150405"),
        SessionId:       "session_123",
        SupplierBizId:   "dida_hotel_search_001",
        SupplierBizType: domain.SupplierBizType_Hotel,
        APIInPath:       "/api/hotel/search",
        APIOutPath:      "/hotel/list",
        APIOutSupplier:  "dida",
        Input: &domain.Input{
            Header:   map[string]string{"Content-Type": "application/json"},
            Body:     `{"cityCode":"PEK"}`,
            BodySize: 20,
        },
        Output: &domain.Output{
            Header:         map[string]string{"Content-Type": "application/json"},
            Body:           `{"hotels":[]}`,
            BodySize:       15,
            CostTime:       100 * time.Millisecond,
            HttpStatusCode: 200,
        },
        CostTime: time.Since(startTime),
    }
    
    // 发送日志和指标
    log.Emit(ctx)
}
```

### 错误处理

```go
// 业务错误
log := &domain.HBLog{
    // ... 其他字段 ...
    BizError: bizerr.New(1001, "库存不足"),
    Output: &domain.Output{
        HttpStatusCode: 200, // HTTP成功，但业务失败
    },
}

// 系统错误
log := &domain.HBLog{
    // ... 其他字段 ...
    Output: &domain.Output{
        HttpStatusCode: 500, // HTTP错误
    },
}
```

### 自定义字段

```go
log := &domain.HBLog{
    // ... 其他字段 ...
    KVs: map[string]any{
        "hotel_count": 10,
        "city_code":   "PEK",
        "check_in":    "2025-06-28",
        "check_out":   "2025-06-29",
        "rate_plan_id": "rate_plan_123",
    },
}
```

## 指标监控

HBLog 会自动上报以下指标到 Prometheus：

### 计数器指标

- `hb_api_calls_total` - 接口调用总次数
- `hb_api_errors_total` - 错误总次数
- `hb_api_http_status_total` - HTTP状态码分布

### 直方图指标

- `hb_api_duration_ms` - 接口耗时分布（毫秒）
- `hb_api_internal_duration_ms` - 供应商内部耗时分布（毫秒）

### 状态指标

- `hb_api_request_size_bytes` - 请求体大小
- `hb_api_response_size_bytes` - 响应体大小

### 标签维度

所有指标都包含以下标签：
- `supplier` - 供应商名称
- `api_path` - 供应商接口路径
- `biz_type` - 业务类型
- `api_in_path` - HotelByte入口API

## 日志查询

支持通过 `LogQuery` 进行多维度查询：

```go
query := &domain.LogQuery{
    StartTime:       time.Now().Add(-1 * time.Hour),
    EndTime:         time.Now(),
    SupplierBizType: int(domain.SupplierBizType_Hotel),
    ApiOutSupplier:  "dida",
    BizErrorCode:    "1001",
    Page:            1,
    PageSize:        20,
}
```

## 最佳实践

1. **及时记录**：在处理完请求后立即调用 `log.Emit(ctx)`
2. **完整信息**：尽可能填写所有相关字段，便于问题排查
3. **错误分类**：正确区分业务错误和系统错误
4. **性能监控**：关注耗时和大小指标，及时发现性能问题
5. **标签规范**：使用一致的标签命名规范

## 示例代码

完整的使用示例请参考：
- `log_example.go` - 基本使用示例
- `log_test.go` - 单元测试示例 
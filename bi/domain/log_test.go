package domain

import (
	"context"
	"testing"
	"time"

	"hotel/common/bizerr"
)

func TestHBLog_Emit(t *testing.T) {
	ctx := context.Background()

	// 创建测试日志
	log := &HBLog{
		Timestamp:      time.Now(),
		KVs:            map[string]any{"test_key": "test_value"},
		UserId:         12345,
		LogId:          "test_log_id",
		SessionId:      "test_session_id",
		SellerEntityId: 67890,
		BuyerEntityId:  11111,
		APIIn: &APIIn{
			Path:    "/api/hotel/search",
			BizType: "hotel",
			BizId:   "test_supplier_biz_id",
		},
		APIOut: &APIOut{
			Path:     "/hotel/list",
			BizType:  "hotel",
			BizId:    "test_supplier_biz_id",
			Supplier: "dida",
		},
		Input: &Input{
			Header:   map[string]string{"Content-Type": "application/json"},
			Body:     `{"hotelId": "123"}`,
			BodySize: 20,
		},
		Output: &Output{
			Header:           map[string]string{"Content-Type": "application/json"},
			Body:             `{"hotels": []}`,
			BodySize:         15,
			CostTime:         100 * time.Millisecond,
			InternalCostTime: 80 * time.Millisecond,
			HttpStatusCode:   200,
		},
		CostTime: 150 * time.Millisecond,
	}

	// 测试正常情况
	t.Run("normal case", func(t *testing.T) {
		log.Emit(ctx)
		// 这里主要是验证不会 panic，实际日志输出需要查看控制台
	})

	// 测试错误情况
	t.Run("error case", func(t *testing.T) {
		log.BizError = bizerr.New(1001, "测试错误")
		log.Emit(ctx)
		// 这里主要是验证不会 panic，实际日志输出需要查看控制台
	})

	// 测试空输入输出
	t.Run("empty input output", func(t *testing.T) {
		log.Input = nil
		log.Output = nil
		log.BizError = nil
		log.Emit(ctx)
		// 这里主要是验证不会 panic，实际日志输出需要查看控制台
	})
}

func TestHBLog_emitMetrics(t *testing.T) {
	ctx := context.Background()

	// 创建测试日志
	log := &HBLog{
		Timestamp: time.Now(),
		LogId:     "test_metrics_log_id",
		SessionId: "test_metrics_session_id",
		APIIn: &APIIn{
			Path:    "/api/hotel/rates",
			BizType: "hotelRatePkg",
			BizId:   "test_metrics_supplier_biz_id",
		},
		APIOut: &APIOut{
			Path:     "/rate/search",
			BizType:  "hotelRatePkg",
			BizId:    "test_metrics_supplier_biz_id",
			Supplier: "dida",
		},
		Input: &Input{
			BodySize: 1024,
		},
		Output: &Output{
			BodySize:         2048,
			CostTime:         200 * time.Millisecond,
			InternalCostTime: 150 * time.Millisecond,
			HttpStatusCode:   200,
		},
		CostTime: 250 * time.Millisecond,
	}

	// 测试正常情况
	t.Run("normal metrics", func(t *testing.T) {
		log.Emit(ctx)
		// 这里主要是验证不会 panic，实际 metrics 上报需要查看监控系统
	})

	// 测试错误情况
	t.Run("error metrics", func(t *testing.T) {
		log.BizError = bizerr.New(1002, "测试指标错误")
		log.Emit(ctx)
		// 这里主要是验证不会 panic，实际 metrics 上报需要查看监控系统
	})

	// 测试 HTTP 错误状态码
	t.Run("http error status", func(t *testing.T) {
		log.Output.HttpStatusCode = 500
		log.Emit(ctx)
		// 这里主要是验证不会 panic，实际 metrics 上报需要查看监控系统
	})
}

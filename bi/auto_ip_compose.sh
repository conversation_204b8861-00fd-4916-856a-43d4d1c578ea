#!/bin/bash

# 自动获取本机 IP（优先 Wi-Fi en0，再试有线 en1）
LOCAL_IP=$(ipconfig getifaddr en0 2>/dev/null)
if [ -z "$LOCAL_IP" ]; then
  LOCAL_IP=$(ipconfig getifaddr en1 2>/dev/null)
fi
if [ -z "$LOCAL_IP" ]; then
  echo "无法自动获取本机 IP，请手动指定。"
  exit 1
fi

echo "检测到本机 IP: $LOCAL_IP"

# 替换 docker-compose.yaml
COMPOSE_FILE="docker-compose.yaml"

# 备份原文件
cp $COMPOSE_FILE ${COMPOSE_FILE}.bak

# 支持服务名、IP、host.docker.internal 的全部自动替换
sed -i '' "s/FE_SERVERS=fe1:[^:]*:9010/FE_SERVERS=fe1:$LOCAL_IP:9010/g" $COMPOSE_FILE
sed -i '' "s/BE_ADDR=[^:]*:9050/BE_ADDR=$LOCAL_IP:9050/g" $COMPOSE_FILE

# 检查替换后内容
cat $COMPOSE_FILE | grep FE_SERVERS
cat $COMPOSE_FILE | grep BE_ADDR

echo "已自动替换 FE_SERVERS 和 BE_ADDR 为 $LOCAL_IP"

# 检测系统类型
UNAME=$(uname)
if [[ "$UNAME" == "Linux" ]]; then
  echo "检测到 Linux 环境，自动设置 vm.max_map_count..."
  sudo sysctl -w vm.max_map_count=2000000
elif [[ "$UNAME" == "Darwin" ]]; then
  echo "检测到 macOS 环境。请确保 Docker Desktop 的 Linux 虚拟机内已设置 vm.max_map_count=2000000。"
  echo "可参考 Doris/ElasticSearch 官方文档，或在 BE 容器内手动执行：sysctl -w vm.max_map_count=2000000"
fi

echo "现在可以执行: docker-compose up -d" 
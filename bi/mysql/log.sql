CREATE TABLE hb_log (
    `timestamp` DATETIME NOT NULL COMMENT '日志时间',
    `log_id` VARCHAR(36) NOT NULL COMMENT '日志ID',
    `session_id` VARCHAR(36) COMMENT '会话ID',
    `user_id` BIGINT COMMENT '用户ID',
    `seller_entity_id` BIGINT COMMENT '卖家实体ID',
    `buyer_entity_id` BIGINT COMMENT '买家实体ID',
    `api_in_path` VARCHAR(255) COMMENT '入口API路径',
    `api_in_biz_type` VARCHAR(20) COMMENT '入口API业务类型',
    `api_in_biz_id` VARCHAR(50) COMMENT '入口API业务ID',
    `api_out_path` VARCHAR(255) COMMENT '出口API路径',
    `api_out_biz_type` VARCHAR(20) COMMENT '出口API业务类型',
    `api_out_biz_id` VARCHAR(50) COMMENT '出口API业务ID',
    `api_out_supplier` VARCHAR(50) COMMENT '出口API供应商',
    `input_header` MAP<STRING, STRING> COMMENT '请求头',
    `input_body` STRING COMMENT '请求体',
    `input_body_size` BIGINT COMMENT '请求体大小',
    `input_credential` MAP<STRING, STRING> COMMENT '请求认证信息',
    `output_header` MAP<STRING, STRING> COMMENT '响应头',
    `output_body` STRING COMMENT '响应体',
    `output_body_size` BIGINT COMMENT '响应体大小',
    `output_cost_time` INT COMMENT '响应耗时（毫秒）',
    `output_internal_cost_time` INT COMMENT '内部响应耗时（毫秒）',
    `output_http_status_code` INT COMMENT '响应HTTP状态码',
    `cost_time` INT COMMENT '耗时（毫秒）',
    `biz_error_code` VARCHAR(20) COMMENT '业务错误码',
    `kvs` MAP<STRING, STRING> COMMENT '自定义键值对'
)
DUPLICATE KEY(`timestamp`, `log_id`)
PARTITION BY RANGE(`timestamp`)
(
    PARTITION p202506 VALUES [('2025-06-01 00:00:00'), ('2025-07-01 00:00:00')),
    PARTITION p202507 VALUES [('2025-07-01 00:00:00'), ('2025-08-01 00:00:00')),
    PARTITION p202508 VALUES [('2025-08-01 00:00:00'), ('2025-09-01 00:00:00')),
    PARTITION p202509 VALUES [('2025-09-01 00:00:00'), ('2025-10-01 00:00:00')),
    PARTITION p202510 VALUES [('2025-10-01 00:00:00'), ('2025-11-01 00:00:00')),
    PARTITION p202511 VALUES [('2025-11-01 00:00:00'), ('2025-12-01 00:00:00')),
    PARTITION p202512 VALUES [('2025-12-01 00:00:00'), ('2026-01-01 00:00:00'))
)
DISTRIBUTED BY HASH(`log_id`) BUCKETS 10
PROPERTIES (
    "replication_num" = "1"
);
CREATE TABLE hb_log (
    `timestamp` DATETIME NOT NULL COMMENT '日志时间',
    `log_id` VARCHAR(36) NOT NULL COMMENT '日志ID',
    `session_id` VARCHAR(36) COMMENT '会话ID',
    `user_id` BIGINT COMMENT '用户ID',
    `seller_entity_id` BIGINT COMMENT '卖家实体ID',
    `buyer_entity_id` BIGINT COMMENT '买家实体ID',
    `api_in_path` VARCHAR(255) COMMENT '入口API路径',
    `api_in_biz_type` VARCHAR(20) COMMENT '入口API业务类型',
    `api_in_biz_id` VARCHAR(50) COMMENT '入口API业务ID',
    `api_out_path` VARCHAR(255) COMMENT '出口API路径',
    `api_out_biz_type` VARCHAR(20) COMMENT '出口API业务类型',
    `api_out_biz_id` VARCHAR(50) COMMENT '出口API业务ID',
    `api_out_supplier` VARCHAR(50) COMMENT '出口API供应商',
    `input_header` JSON COMMENT '请求头',
    `input_body` TEXT COMMENT '请求体',
    `input_body_size` BIGINT COMMENT '请求体大小',
    `input_credential` JSON COMMENT '请求认证信息',
    `output_header` JSON COMMENT '响应头',
    `output_body` TEXT COMMENT '响应体',
    `output_body_size` BIGINT COMMENT '响应体大小',
    `output_cost_time` INT COMMENT '响应耗时（毫秒）',
    `output_internal_cost_time` INT COMMENT '内部响应耗时（毫秒）',
    `output_http_status_code` INT COMMENT '响应HTTP状态码',
    `cost_time` INT COMMENT '耗时（毫秒）',
    `biz_error_code` VARCHAR(20) COMMENT '业务错误码',
    `kvs` JSON COMMENT '自定义键值对',
    PRIMARY KEY (`timestamp`, `log_id`),
    INDEX idx_session_id (`session_id`),
    INDEX idx_user_id (`user_id`),
    INDEX idx_seller_entity_id (`seller_entity_id`),
    INDEX idx_buyer_entity_id (`buyer_entity_id`),
    INDEX idx_api_in_path (`api_in_path`),
    INDEX idx_api_out_path (`api_out_path`),
    INDEX idx_api_out_supplier (`api_out_supplier`),
    INDEX idx_biz_error_code (`biz_error_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(`timestamp`) * 100 + MONTH(`timestamp`)) (
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
); 
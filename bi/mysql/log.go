package mysql

import (
	"time"
)

// HBLogModel is the model for hb_log table
type HBLogModel struct {
	Timestamp              time.Time `db:"timestamp"`
	LogId                  string    `db:"log_id"`
	SessionId              string    `db:"session_id"`
	UserId                 int64     `db:"user_id"`
	SellerEntityId         int64     `db:"seller_entity_id"`
	BuyerEntityId          int64     `db:"buyer_entity_id"`
	ApiInPath              string    `db:"api_in_path"`
	ApiInBizType           string    `db:"api_in_biz_type"`
	ApiInBizId             string    `db:"api_in_biz_id"`
	ApiOutSupplier         string    `db:"api_out_supplier"`
	ApiOutPath             string    `db:"api_out_path"`
	ApiOutBizType          string    `db:"api_out_biz_type"`
	ApiOutBizId            string    `db:"api_out_biz_id"`
	CostTime               int64     `db:"cost_time"`
	BizErrorCode           string    `db:"biz_error_code"`
	InputHeader            string    `db:"input_header"` // JSON
	InputBody              string    `db:"input_body"`
	InputBodySize          int64     `db:"input_body_size"`
	InputCredential        string    `db:"input_credential"` // JSON
	OutputHttpStatusCode   int       `db:"output_http_status_code"`
	OutputHeader           string    `db:"output_header"` // JSON
	OutputBody             string    `db:"output_body"`
	OutputBodySize         int64     `db:"output_body_size"`
	OutputCostTime         int64     `db:"output_cost_time"`
	OutputInternalCostTime int64     `db:"output_internal_cost_time"`
	Kvs                    string    `db:"kvs"` // JSON
}

package service

import (
	_ "github.com/go-sql-driver/mysql"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sync"

	"hotel/bi/config"
	configutils "hotel/common/config"
)

var (
	_logSrv  *LogService
	_logOnce sync.Once
)

var configFile = configutils.SafeFlagString("rule", "bi/config/config.yaml", "rule config file")

type LogService struct {
	conn sqlx.SqlConn
	cfg  config.Config
}

func NewLogService() *LogService {
	_logOnce.Do(func() {
		var cfg config.Config
		conf.MustLoad(*configFile, &cfg)
		conn := sqlx.NewMysql(cfg.DSN)
		_logSrv = &LogService{
			conn: conn,
			cfg:  cfg,
		}
		go _logSrv.asyncWriteLog()
	})
	return _logSrv
}

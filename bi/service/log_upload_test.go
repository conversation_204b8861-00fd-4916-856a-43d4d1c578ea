package service

import (
	"context"
	"testing"
	"time"

	"hotel/bi/domain"

	"github.com/stretchr/testify/assert"
)

func TestLogUpload(t *testing.T) {
	ctx := context.Background()

	// 创建一个完整的 HBLog 实例进行测试
	testLog := domain.NewHBLog()
	testLog.LogId = "test-log-id-123"
	testLog.SessionId = "test-session-456"
	testLog.UserId = 12345
	testLog.SellerEntityId = 67890
	testLog.BuyerEntityId = 11111
	testLog.CostTime = 100 * time.Millisecond

	// 设置 APIIn 信息
	testLog.APIIn.Path = "/api/test/in"
	testLog.APIIn.BizType = "test"
	testLog.APIIn.BizId = "test-biz-id"

	// 设置 APIOut 信息
	testLog.APIOut.Path = "/api/test/out"
	testLog.APIOut.BizType = "test"
	testLog.APIOut.BizId = "test-biz-id"
	testLog.APIOut.Supplier = "test-supplier"

	// 设置输入输出信息
	testLog.Input.Body = "test input body"
	testLog.Input.BodySize = 100
	testLog.Input.Header["Content-Type"] = "application/json"
	testLog.Input.Credential["api_key"] = "test-key"

	testLog.Output.Body = "test output body"
	testLog.Output.BodySize = 200
	testLog.Output.HttpStatusCode = 200
	testLog.Output.CostTime = 50 * time.Millisecond
	testLog.Output.Header["Content-Type"] = "application/json"

	// 添加自定义键值对
	testLog.AddKv("test_key", "test_value")
	testLog.AddKv("test_number", 42)

	// 测试异步上传日志
	err := s.AsyncUploadLog(ctx, testLog)
	assert.Nil(t, err, "AsyncUploadLog should not return error")

	// 等待一段时间让异步处理完成
	time.Sleep(3 * time.Second)

	// 查库校验
	var row struct {
		LogId     string `db:"log_id"`
		SessionId string `db:"session_id"`
		UserId    int64  `db:"user_id"`
	}
	err = s.conn.QueryRowCtx(ctx, &row, "SELECT log_id, session_id, user_id FROM hb_log WHERE log_id = ?", testLog.LogId)
	assert.Nil(t, err, "should find the inserted log in doris")
	assert.Equal(t, testLog.LogId, row.LogId)
	assert.Equal(t, testLog.SessionId, row.SessionId)
	assert.Equal(t, testLog.UserId, row.UserId)

	t.Log("TestLogUpload completed successfully")
}

func TestLogUploadWithNilLog(t *testing.T) {
	ctx := context.Background()

	// 测试空日志的情况
	err := s.AsyncUploadLog(ctx, nil)
	assert.NotNil(t, err, "AsyncUploadLog should return error for nil log")
}

func TestLogUploadWithEmptyLog(t *testing.T) {
	ctx := context.Background()

	// 测试空日志的情况
	emptyLog := &domain.HBLog{}
	err := s.AsyncUploadLog(ctx, emptyLog)
	assert.Nil(t, err, "AsyncUploadLog should handle empty log")

	// 等待一段时间让异步处理完成
	time.Sleep(1 * time.Second)

	t.Log("TestLogUploadWithEmptyLog completed successfully")
}

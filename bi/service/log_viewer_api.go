package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"hotel/bi/domain"
	"hotel/common/bizerr"
	userDomain "hotel/user/domain"
)

// LogViewerService 日志查看器服务
type LogViewerService struct {
	logSrv *LogService
}

func NewLogViewerService() *LogViewerService {
	return &LogViewerService{
		logSrv: NewLogService(),
	}
}

func (s *LogViewerService) Name() string {
	return "logViewer"
}

// QueryLogsReq 查询日志请求
type QueryLogsReq struct {
	*userDomain.User `apidoc:"-"`

	// 时间范围
	StartTime string `json:"startTime,omitempty" form:"startTime"`
	EndTime   string `json:"endTime,omitempty" form:"endTime"`

	// 基础筛选
	LogId          string `json:"logId,omitempty" form:"logId"`
	SessionId      string `json:"sessionId,omitempty" form:"sessionId"`
	UserId         int64  `json:"userId,omitempty" form:"userId"`
	SellerEntityId int64  `json:"sellerEntityId,omitempty" form:"sellerEntityId"`
	BuyerEntityId  int64  `json:"buyerEntityId,omitempty" form:"buyerEntityId"`

	// API 相关
	ApiInPath      string `json:"apiInPath,omitempty" form:"apiInPath"`
	ApiOutSupplier string `json:"apiOutSupplier,omitempty" form:"apiOutSupplier"`
	ApiOutPath     string `json:"apiOutPath,omitempty" form:"apiOutPath"`

	// 业务相关
	SupplierBizId   string `json:"supplierBizId,omitempty" form:"supplierBizId"`
	SupplierBizType string `json:"supplierBizType,omitempty" form:"supplierBizType"`
	BizErrorCode    string `json:"bizErrorCode,omitempty" form:"bizErrorCode"`

	// 请求/响应相关
	InputHeaderKey       string `json:"inputHeaderKey,omitempty" form:"inputHeaderKey"`
	InputHeaderValue     string `json:"inputHeaderValue,omitempty" form:"inputHeaderValue"`
	InputCredentialKey   string `json:"inputCredentialKey,omitempty" form:"inputCredentialKey"`
	InputCredentialValue string `json:"inputCredentialValue,omitempty" form:"inputCredentialValue"`
	InputBodyKeyword     string `json:"inputBodyKeyword,omitempty" form:"inputBodyKeyword"`
	OutputBodyKeyword    string `json:"outputBodyKeyword,omitempty" form:"outputBodyKeyword"`
	OutputHeaderKey      string `json:"outputHeaderKey,omitempty" form:"outputHeaderKey"`
	OutputHeaderValue    string `json:"outputHeaderValue,omitempty" form:"outputHeaderValue"`
	OutputHttpStatusCode int    `json:"outputHttpStatusCode,omitempty" form:"outputHttpStatusCode"`

	// 性能相关
	CostTimeMin string `json:"costTimeMin,omitempty" form:"costTimeMin"`
	CostTimeMax string `json:"costTimeMax,omitempty" form:"costTimeMax"`

	// 分页
	Page     int `json:"page" form:"page"`
	PageSize int `json:"pageSize" form:"pageSize"`

	// 排序
	SortBy    string `json:"sortBy,omitempty" form:"sortBy"`
	SortOrder string `json:"sortOrder,omitempty" form:"sortOrder"`
}

// QueryLogsResp 查询日志响应
type QueryLogsResp struct {
	Logs  []*LogItem `json:"logs"`
	Total int64      `json:"total"`
	Page  int        `json:"page"`
	Size  int        `json:"size"`
}

// LogItem 日志条目（用于列表展示）
type LogItem struct {
	Timestamp      time.Time `json:"timestamp"`
	LogId          string    `json:"logId"`
	SessionId      string    `json:"sessionId"`
	UserId         int64     `json:"userId"`
	SellerEntityId int64     `json:"sellerEntityId"`
	BuyerEntityId  int64     `json:"buyerEntityId"`

	// API 信息
	ApiInPath      string `json:"apiInPath"`
	ApiOutSupplier string `json:"apiOutSupplier"`
	ApiOutPath     string `json:"apiOutPath"`

	// 业务信息
	SupplierBizId   string `json:"supplierBizId"`
	SupplierBizType string `json:"supplierBizType"`

	// 状态信息
	HttpStatusCode int    `json:"httpStatusCode"`
	BizErrorCode   string `json:"bizErrorCode"`
	CostTime       int64  `json:"costTime"` // 毫秒

	// 请求/响应大小
	InputBodySize  int64 `json:"inputBodySize"`
	OutputBodySize int64 `json:"outputBodySize"`

	// 快速预览
	InputPreview  string `json:"inputPreview"`  // 前100字符
	OutputPreview string `json:"outputPreview"` // 前100字符

	// 状态标识
	Status string `json:"status"` // success, error, warning
}

// QueryLogs 查询日志列表
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) QueryLogs(ctx context.Context, req *QueryLogsReq) (*QueryLogsResp, error) {
	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 构建查询条件
	query := s.buildLogQuery(req)

	// 查询日志
	result, err := s.logSrv.QueryLogs(ctx, query)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	logs := make([]*LogItem, len(result.Logs))
	for i, hbLog := range result.Logs {
		logs[i] = s.convertToLogItem(hbLog)
	}

	return &QueryLogsResp{
		Logs:  logs,
		Total: result.Total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// buildLogQuery 构建日志查询条件
func (s *LogViewerService) buildLogQuery(req *QueryLogsReq) *domain.LogQuery {
	query := &domain.LogQuery{
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	// 解析时间范围
	if req.StartTime != "" {
		if startTime, err := time.Parse(time.RFC3339, req.StartTime); err == nil {
			query.StartTime = startTime
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse(time.RFC3339, req.EndTime); err == nil {
			query.EndTime = endTime
		}
	}

	// 设置其他查询条件
	query.LogId = req.LogId
	query.SessionId = req.SessionId
	query.UserId = req.UserId
	query.SellerEntityId = req.SellerEntityId
	query.BuyerEntityId = req.BuyerEntityId
	query.ApiInPath = req.ApiInPath
	query.ApiOutSupplier = req.ApiOutSupplier
	query.ApiOutPath = req.ApiOutPath
	query.SupplierBizId = req.SupplierBizId
	query.BizErrorCode = req.BizErrorCode
	query.InputHeaderKey = req.InputHeaderKey
	query.InputHeaderValue = req.InputHeaderValue
	query.InputCredentialKey = req.InputCredentialKey
	query.InputCredentialValue = req.InputCredentialValue
	query.InputBodyKeyword = req.InputBodyKeyword
	query.OutputBodyKeyword = req.OutputBodyKeyword
	query.OutputHeaderKey = req.OutputHeaderKey
	query.OutputHeaderValue = req.OutputHeaderValue
	query.OutputHttpStatusCode = req.OutputHttpStatusCode

	// 解析耗时范围
	if req.CostTimeMin != "" {
		if duration, err := time.ParseDuration(req.CostTimeMin); err == nil {
			query.CostTimeMin = duration
		}
	}
	if req.CostTimeMax != "" {
		if duration, err := time.ParseDuration(req.CostTimeMax); err == nil {
			query.CostTimeMax = duration
		}
	}

	return query
}

// convertToLogItem 转换 HBLog 为 LogItem
func (s *LogViewerService) convertToLogItem(hbLog *domain.HBLog) *LogItem {
	item := &LogItem{
		Timestamp:      hbLog.Timestamp,
		LogId:          hbLog.LogId,
		SessionId:      hbLog.SessionId,
		UserId:         hbLog.UserId,
		SellerEntityId: hbLog.SellerEntityId,
		BuyerEntityId:  hbLog.BuyerEntityId,
		CostTime:       int64(hbLog.CostTime / time.Millisecond),
	}

	// API 信息
	if hbLog.APIIn != nil {
		item.ApiInPath = hbLog.APIIn.Path
	}
	if hbLog.APIOut != nil {
		item.ApiOutSupplier = hbLog.APIOut.Supplier
		item.ApiOutPath = hbLog.APIOut.Path
		item.SupplierBizType = hbLog.APIOut.BizType
		item.SupplierBizId = hbLog.APIOut.BizId
	}

	// 状态信息
	if hbLog.Output != nil {
		item.HttpStatusCode = hbLog.Output.HttpStatusCode
		item.OutputBodySize = hbLog.Output.BodySize

		// 输出预览
		if len(hbLog.Output.Body) > 100 {
			item.OutputPreview = hbLog.Output.Body[:100] + "..."
		} else {
			item.OutputPreview = hbLog.Output.Body
		}
	}

	if hbLog.Input != nil {
		item.InputBodySize = hbLog.Input.BodySize

		// 输入预览
		if len(hbLog.Input.Body) > 100 {
			item.InputPreview = hbLog.Input.Body[:100] + "..."
		} else {
			item.InputPreview = hbLog.Input.Body
		}
	}

	// 错误信息
	if hbLog.BizError != nil {
		item.BizErrorCode = strconv.Itoa(int(hbLog.BizError.StatusCode()))
	}

	// 状态判断
	item.Status = s.determineLogStatus(hbLog)

	return item
}

// determineLogStatus 判断日志状态
func (s *LogViewerService) determineLogStatus(hbLog *domain.HBLog) string {
	if hbLog.BizError != nil {
		return "error"
	}

	if hbLog.Output != nil {
		if hbLog.Output.HttpStatusCode >= 400 {
			return "error"
		}
		if hbLog.Output.HttpStatusCode >= 300 {
			return "warning"
		}
	}

	// 耗时超过5秒认为是警告
	if hbLog.CostTime > 5*time.Second {
		return "warning"
	}

	return "success"
}

// GetLogDetailReq 获取日志详情请求
type GetLogDetailReq struct {
	*userDomain.User `apidoc:"-"`
	LogId            string `json:"logId" form:"logId" binding:"required"`
}

// GetLogDetailResp 获取日志详情响应
type GetLogDetailResp struct {
	Log *LogDetail `json:"log"`
}

// LogDetail 日志详情
type LogDetail struct {
	*LogItem

	// 完整的请求/响应数据
	Input  *LogInput  `json:"input,omitempty"`
	Output *LogOutput `json:"output,omitempty"`

	// 自定义键值对
	KVs map[string]interface{} `json:"kvs,omitempty"`

	// 错误详情
	BizError *LogBizError `json:"bizError,omitempty"`
}

// LogInput 日志输入详情
type LogInput struct {
	Header     map[string]string `json:"header,omitempty"`
	Body       string            `json:"body,omitempty"`
	BodySize   int64             `json:"bodySize"`
	Credential map[string]string `json:"credential,omitempty"`
}

// LogOutput 日志输出详情
type LogOutput struct {
	Header           map[string]string `json:"header,omitempty"`
	Body             string            `json:"body,omitempty"`
	BodySize         int64             `json:"bodySize"`
	CostTime         int64             `json:"costTime"`         // 毫秒
	InternalCostTime int64             `json:"internalCostTime"` // 毫秒
	HttpStatusCode   int               `json:"httpStatusCode"`
}

// LogBizError 业务错误详情
type LogBizError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// GetLogDetail 获取日志详情
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) GetLogDetail(ctx context.Context, req *GetLogDetailReq) (*GetLogDetailResp, error) {
	// 查询单条日志
	query := &domain.LogQuery{
		LogId:    req.LogId,
		Page:     1,
		PageSize: 1,
	}

	result, err := s.logSrv.QueryLogs(ctx, query)
	if err != nil {
		return nil, err
	}

	if len(result.Logs) == 0 {
		return nil, bizerr.NewHTTP(0, "日志不存在", 404)
	}

	hbLog := result.Logs[0]
	detail := &LogDetail{
		LogItem: s.convertToLogItem(hbLog),
		KVs:     hbLog.KVs,
	}

	// 设置完整的输入输出数据
	if hbLog.Input != nil {
		detail.Input = &LogInput{
			Header:     hbLog.Input.Header,
			Body:       hbLog.Input.Body,
			BodySize:   hbLog.Input.BodySize,
			Credential: hbLog.Input.Credential,
		}
	}

	if hbLog.Output != nil {
		detail.Output = &LogOutput{
			Header:           hbLog.Output.Header,
			Body:             hbLog.Output.Body,
			BodySize:         hbLog.Output.BodySize,
			CostTime:         int64(hbLog.Output.CostTime / time.Millisecond),
			InternalCostTime: int64(hbLog.Output.InternalCostTime / time.Millisecond),
			HttpStatusCode:   hbLog.Output.HttpStatusCode,
		}
	}

	if hbLog.BizError != nil {
		detail.BizError = &LogBizError{
			Code:    int(hbLog.BizError.StatusCode()),
			Message: hbLog.BizError.StatusMessage(),
		}
	}

	return &GetLogDetailResp{Log: detail}, nil
}

// ExportLogsReq 导出日志请求
type ExportLogsReq struct {
	*userDomain.User `apidoc:"-"`

	// 继承查询条件
	QueryLogsReq

	// 导出格式
	Format string `json:"format" form:"format"` // csv, json, excel

	// 导出字段
	Fields []string `json:"fields,omitempty" form:"fields"`
}

// ExportLogsResp 导出日志响应
type ExportLogsResp struct {
	DownloadURL string `json:"downloadUrl"`
	FileName    string `json:"fileName"`
	FileSize    int64  `json:"fileSize"`
}

// ExportLogs 导出日志
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) ExportLogs(ctx context.Context, req *ExportLogsReq) (*ExportLogsResp, error) {
	// 限制导出数量
	if req.PageSize <= 0 || req.PageSize > 10000 {
		req.PageSize = 1000
	}

	// 构建查询条件
	query := s.buildLogQuery(&req.QueryLogsReq)

	// 查询日志
	result, err := s.logSrv.QueryLogs(ctx, query)
	if err != nil {
		return nil, err
	}

	// 根据格式导出
	var fileName string
	var fileContent []byte

	switch req.Format {
	case "csv":
		fileName, fileContent, err = s.exportToCSV(result.Logs, req.Fields)
	case "json":
		fileName, fileContent, err = s.exportToJSON(result.Logs)
	default:
		return nil, bizerr.NewHTTP(0, "不支持的导出格式", 400)
	}

	if err != nil {
		return nil, err
	}

	// 这里应该将文件保存到文件系统或对象存储，并返回下载URL
	// 为了简化，这里直接返回文件信息
	return &ExportLogsResp{
		DownloadURL: "/api/logViewer/download/" + fileName,
		FileName:    fileName,
		FileSize:    int64(len(fileContent)),
	}, nil
}

// exportToCSV 导出为CSV格式
func (s *LogViewerService) exportToCSV(logs []*domain.HBLog, fields []string) (string, []byte, error) {
	var buf strings.Builder
	writer := csv.NewWriter(&buf)

	// 默认字段
	if len(fields) == 0 {
		fields = []string{"timestamp", "logId", "sessionId", "apiInPath", "apiOutSupplier", "httpStatusCode", "costTime"}
	}

	// 写入表头
	if err := writer.Write(fields); err != nil {
		return "", nil, err
	}

	// 写入数据
	for _, log := range logs {
		record := make([]string, len(fields))
		for i, field := range fields {
			record[i] = s.getFieldValue(log, field)
		}
		if err := writer.Write(record); err != nil {
			return "", nil, err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", nil, err
	}

	fileName := fmt.Sprintf("logs_%s.csv", time.Now().Format("20060102_150405"))
	return fileName, []byte(buf.String()), nil
}

// exportToJSON 导出为JSON格式
func (s *LogViewerService) exportToJSON(logs []*domain.HBLog) (string, []byte, error) {
	data, err := json.MarshalIndent(logs, "", "  ")
	if err != nil {
		return "", nil, err
	}

	fileName := fmt.Sprintf("logs_%s.json", time.Now().Format("20060102_150405"))
	return fileName, data, nil
}

// getFieldValue 获取字段值
func (s *LogViewerService) getFieldValue(log *domain.HBLog, field string) string {
	switch field {
	case "timestamp":
		return log.Timestamp.Format(time.RFC3339)
	case "logId":
		return log.LogId
	case "sessionId":
		return log.SessionId
	case "userId":
		return strconv.FormatInt(log.UserId, 10)
	case "apiInPath":
		if log.APIIn != nil {
			return log.APIIn.Path
		}
		return ""
	case "apiOutSupplier":
		if log.APIOut != nil {
			return log.APIOut.Supplier
		}
		return ""
	case "apiOutPath":
		if log.APIOut != nil {
			return log.APIOut.Path
		}
		return ""
	case "httpStatusCode":
		if log.Output != nil {
			return strconv.Itoa(log.Output.HttpStatusCode)
		}
		return ""
	case "costTime":
		return strconv.FormatInt(int64(log.CostTime/time.Millisecond), 10)
	case "bizErrorCode":
		if log.BizError != nil {
			return strconv.Itoa(int(log.BizError.StatusCode()))
		}
		return ""
	default:
		return ""
	}
}

// GetLogStatisticsReq 获取日志统计请求
type GetLogStatisticsReq struct {
	*userDomain.User `apidoc:"-"`

	// 时间范围
	StartTime string `json:"startTime,omitempty" form:"startTime"`
	EndTime   string `json:"endTime,omitempty" form:"endTime"`

	// 统计维度
	GroupBy string `json:"groupBy,omitempty" form:"groupBy"` // hour, day, supplier, api
}

// GetLogStatisticsResp 获取日志统计响应
type GetLogStatisticsResp struct {
	TotalCount   int64 `json:"totalCount"`
	SuccessCount int64 `json:"successCount"`
	WarningCount int64 `json:"warningCount"`
	ErrorCount   int64 `json:"errorCount"`
	AvgCostTime  int64 `json:"avgCostTime"` // 毫秒

	// 分组统计
	GroupStats []GroupStat `json:"groupStats,omitempty"`

	// 趋势数据
	TrendData []TrendPoint `json:"trendData,omitempty"`
}

// GroupStat 分组统计
type GroupStat struct {
	Name  string  `json:"name"`
	Count int64   `json:"count"`
	Rate  float64 `json:"rate"` // 百分比
}

// TrendPoint 趋势点
type TrendPoint struct {
	Time        string  `json:"time"`
	TotalCount  int64   `json:"totalCount"`
	ErrorCount  int64   `json:"errorCount"`
	ErrorRate   float64 `json:"errorRate"`
	AvgCostTime int64   `json:"avgCostTime"`
}

// GetLogStatistics 获取日志统计
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) GetLogStatistics(ctx context.Context, req *GetLogStatisticsReq) (*GetLogStatisticsResp, error) {
	// 构建查询条件
	query := &domain.LogQuery{
		Page:     1,
		PageSize: 10000, // 统计需要更多数据
	}

	// 解析时间范围
	if req.StartTime != "" {
		if startTime, err := time.Parse(time.RFC3339, req.StartTime); err == nil {
			query.StartTime = startTime
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse(time.RFC3339, req.EndTime); err == nil {
			query.EndTime = endTime
		}
	}

	// 查询日志
	result, err := s.logSrv.QueryLogs(ctx, query)
	if err != nil {
		return nil, err
	}

	// 计算统计数据
	resp := &GetLogStatisticsResp{
		TotalCount: result.Total,
	}

	var totalCostTime int64
	supplierStats := make(map[string]int64)
	apiStats := make(map[string]int64)
	hourlyStats := make(map[string]*TrendPoint)

	for _, log := range result.Logs {
		// 基础统计
		switch s.determineLogStatus(log) {
		case "success":
			resp.SuccessCount++
		case "warning":
			resp.WarningCount++
		case "error":
			resp.ErrorCount++
		}

		totalCostTime += int64(log.CostTime / time.Millisecond)

		// 供应商统计
		if log.APIOut != nil {
			supplierStats[log.APIOut.Supplier]++
		}

		// API统计
		if log.APIIn != nil {
			apiStats[log.APIIn.Path]++
		}

		// 按小时统计
		hour := log.Timestamp.Format("2006-01-02 15:00")
		if point, exists := hourlyStats[hour]; exists {
			point.TotalCount++
			if s.determineLogStatus(log) == "error" {
				point.ErrorCount++
			}
			point.AvgCostTime = (point.AvgCostTime + int64(log.CostTime/time.Millisecond)) / 2
		} else {
			errorCount := int64(0)
			if s.determineLogStatus(log) == "error" {
				errorCount = 1
			}
			hourlyStats[hour] = &TrendPoint{
				Time:        hour,
				TotalCount:  1,
				ErrorCount:  errorCount,
				AvgCostTime: int64(log.CostTime / time.Millisecond),
			}
		}
	}

	// 计算平均耗时
	if resp.TotalCount > 0 {
		resp.AvgCostTime = totalCostTime / resp.TotalCount
	}

	// 根据分组类型返回统计数据
	switch req.GroupBy {
	case "supplier":
		for supplier, count := range supplierStats {
			resp.GroupStats = append(resp.GroupStats, GroupStat{
				Name:  supplier,
				Count: count,
				Rate:  float64(count) / float64(resp.TotalCount) * 100,
			})
		}
	case "api":
		for api, count := range apiStats {
			resp.GroupStats = append(resp.GroupStats, GroupStat{
				Name:  api,
				Count: count,
				Rate:  float64(count) / float64(resp.TotalCount) * 100,
			})
		}
	}

	// 趋势数据
	for _, point := range hourlyStats {
		if point.TotalCount > 0 {
			point.ErrorRate = float64(point.ErrorCount) / float64(point.TotalCount) * 100
		}
		resp.TrendData = append(resp.TrendData, *point)
	}

	return resp, nil
}

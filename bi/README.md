docker-compose -f ./bi/docker-compose.yaml up -d

# mark

- mac 本地跑不起来

开发及测试环境

模块 CPU 内存 磁盘 网络 实例数量（最低要求）
Frontend 8 核 + 8 GB+ SSD 或 SATA，10 GB+ 千兆/万兆网卡 1
Backend 8 核 + 16 GB+ SSD 或 SATA，50 GB+ 千兆/万兆网卡 1
提示
说明

在验证测试环境中，可以将 FE 与 BE 部署在同一台服务器上

一台机器上一般只建议部署一个 BE 实例，同时只能部署一个 FE

如果需要 3 副本数据，那么至少需要 3 台机器各部署一个 BE 实例，而不是 1 台机器部署 3 个 BE 实例

多个 FE 所在服务器的时钟必须保持一致，最多允许 5 秒的时钟偏差

测试环境也可以仅使用一个 BE 进行测试。实际生产环境，BE 实例数量直接决定了整体查询延迟。

生产环境

模块 CPU 内存 磁盘 网络 实例数量（最低要求）
Frontend 16 核 + 64 GB+ SSD 或 RAID 卡，100GB+ 万兆网卡 1
Backend 16 核 + 64 GB+ SSD 或 SATA，100G+ 万兆网卡 3

# bi

本目录为BI（商业智能）相关服务，负责日志采集、分析、报表等功能。

## 主要内容

- `service/`：BI服务实现
- `mysql/`：BI相关数据库脚本
- `config/`：配置文件
- `domain/`：领域模型

## 迭代开发约定

- 日志采集、分析需解耦，便于扩展
- 报表开发需关注性能与可视化
- 配置与代码分离，便于多环境部署

## 注意事项

- 严禁采集敏感/隐私数据
- 日志格式需标准化
- 变更需同步更新README

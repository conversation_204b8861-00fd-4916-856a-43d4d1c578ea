#!/bin/bash

# 启动本地 Doris 集群
echo "Starting local Doris cluster..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker first."
    exit 1
fi

# 启动 Doris 集群
cd "$(dirname "$0")"
docker-compose up -d

echo "Waiting for <PERSON> to be ready..."
sleep 30

# 检查 Doris 是否启动成功
echo "Checking Doris status..."
if docker-compose ps | grep -q "Up"; then
    echo "Doris cluster is running successfully!"
    echo "FE (Frontend): http://localhost:8030"
    echo "BE (Backend): http://localhost:8040"
    echo "MySQL Client: localhost:9030"
else
    echo "Error: Failed to start Doris cluster"
    docker-compose logs
    exit 1
fi

echo "You can now connect to Doris using:"
echo "mysql -h 127.0.0.1 -P 9030 -u root"
echo ""
echo "To create the database and table, run:"
echo "mysql -h 127.0.0.1 -P 9030 -u root < mysql/log.sql" 
scp -i "/Users/<USER>/hotelcode/dev/hoteldev.pem" ~/Downloads/jdk-8u202-linux-x64.tar.gz <EMAIL>:/home/<USER>/java
sudo apt-get install openjdk-21-jdk


vi /etc/security/limits.conf
* soft nofile 1000000
* hard nofile 1000000

cat >> /etc/sysctl.conf << EOF
vm.max_map_count = 2000000
EOF

## Take effect immediately
sudo sysctl -p

vim apache-doris-3.0.6-bin-x64/fe/conf/fe.conf
## 指定 Java 环境
JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64/

# 指定 FE 监听 IP 的 CIDR 网段
priority_networks=127.0.0.1/32


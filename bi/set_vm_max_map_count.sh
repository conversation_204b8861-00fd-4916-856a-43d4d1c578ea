#!/bin/bash

# 自动进入 Doris BE 容器并设置 vm.max_map_count=2000000（适用于 macOS Docker Desktop）
CONTAINER_NAME="bi-be-1"

# 检查容器是否存在
if ! docker ps | grep -q $CONTAINER_NAME; then
  echo "BE 容器 $CONTAINER_NAME 未启动，请先用 docker-compose up -d 启动 Doris。"
  exit 1
fi

echo "正在进入 $CONTAINER_NAME 并设置 vm.max_map_count=2000000 ..."
docker exec -u 0 $CONTAINER_NAME sysctl -w vm.max_map_count=2000000

if [ $? -eq 0 ]; then
  echo "设置完成，建议重启 BE 容器："
  echo "docker restart $CONTAINER_NAME"
else
  echo "设置失败，请手动进入容器执行：sysctl -w vm.max_map_count=2000000"
fi 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button-group {
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
        }
        .btn-danger {
            background-color: #f56c6c;
            color: white;
        }
        .btn-success {
            background-color: #67c23a;
            color: white;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>菜单调试工具</h1>
        <p>用于调试菜单结构和缓存问题</p>
        
        <div class="button-group">
            <button class="btn-primary" onclick="testBackendAPI()">测试后端API</button>
            <button class="btn-success" onclick="testStarlingAPI()">测试多语言API</button>
            <button class="btn-danger" onclick="clearAllCache()">清除所有缓存</button>
            <button class="btn-primary" onclick="testMenuStructure()">检查菜单结构</button>
        </div>
        
        <div id="status"></div>
        <div id="output" class="output"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
        }
        
        async function testBackendAPI() {
            log('开始测试后端菜单API...');
            try {
                const response = await fetch(`${API_BASE}/api/view/internalHomepage`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('后端API响应成功:');
                log(JSON.stringify(data, null, 2));
                
                // 检查菜单结构
                if (data.menuRoutes) {
                    const systemMenu = data.menuRoutes.find(route => route.id === 'system');
                    const logsMenu = data.menuRoutes.find(route => route.id === 'logs');
                    
                    log('\n=== 菜单结构分析 ===');
                    log(`System菜单: ${systemMenu ? '✓ 存在' : '✗ 不存在'}`);
                    log(`Logs菜单: ${logsMenu ? '✓ 存在' : '✗ 不存在'}`);
                    
                    if (systemMenu) {
                        log(`System路径: ${systemMenu.path}`);
                        log(`System子菜单数量: ${systemMenu.children ? systemMenu.children.length : 0}`);
                        if (systemMenu.children) {
                            systemMenu.children.forEach(child => {
                                log(`  - ${child.id}: ${child.path}`);
                            });
                        }
                    }
                    
                    if (logsMenu) {
                        log(`Logs路径: ${logsMenu.path}`);
                        log(`Logs是否为顶级菜单: ${logsMenu.path === '/logs' ? '✓ 是' : '✗ 否'}`);
                    }
                }
                
                showStatus('后端API测试成功', 'success');
            } catch (error) {
                log(`后端API测试失败: ${error.message}`);
                showStatus(`后端API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testStarlingAPI() {
            log('开始测试多语言API...');
            try {
                const response = await fetch(`${API_BASE}/api/starling/listStarling`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        page: { pageNum: 1, pageSize: 10 }
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('多语言API响应成功:');
                log(JSON.stringify(data, null, 2));
                showStatus('多语言API测试成功', 'success');
            } catch (error) {
                log(`多语言API测试失败: ${error.message}`);
                showStatus(`多语言API测试失败: ${error.message}`, 'error');
            }
        }
        
        function clearAllCache() {
            log('开始清除所有缓存...');
            
            // 清除localStorage
            const localStorageKeys = Object.keys(localStorage);
            localStorageKeys.forEach(key => {
                if (key.includes('menu') || key.includes('internal') || key.includes('pinia')) {
                    localStorage.removeItem(key);
                    log(`清除localStorage: ${key}`);
                }
            });
            
            // 清除sessionStorage
            const sessionStorageKeys = Object.keys(sessionStorage);
            sessionStorageKeys.forEach(key => {
                if (key.includes('menu') || key.includes('internal') || key.includes('iframe')) {
                    sessionStorage.removeItem(key);
                    log(`清除sessionStorage: ${key}`);
                }
            });
            
            log('缓存清除完成');
            showStatus('所有缓存已清除，请刷新页面', 'success');
        }
        
        function testMenuStructure() {
            log('检查当前菜单结构...');
            
            // 检查localStorage中的菜单数据
            const menuStoreKey = Object.keys(localStorage).find(key => key.includes('menuStore'));
            if (menuStoreKey) {
                try {
                    const menuData = JSON.parse(localStorage.getItem(menuStoreKey));
                    log('当前菜单存储数据:');
                    log(JSON.stringify(menuData, null, 2));
                } catch (error) {
                    log(`解析菜单数据失败: ${error.message}`);
                }
            } else {
                log('未找到菜单存储数据');
            }
            
            // 检查当前URL
            log(`当前URL: ${window.location.href}`);
            log(`当前Hash: ${window.location.hash}`);
            
            showStatus('菜单结构检查完成', 'success');
        }
        
        // 页面加载时显示当前状态
        window.onload = function() {
            log('菜单调试工具已加载');
            log(`API地址: ${API_BASE}`);
            testMenuStructure();
        };
    </script>
</body>
</html>

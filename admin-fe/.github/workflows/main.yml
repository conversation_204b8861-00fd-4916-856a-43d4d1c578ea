# .github/workflows/deploy.yml
name: Deploy to S3

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v3

      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'  # 使用 npm 缓存
          
      - name: 安装依赖
        run: npm install --legacy-peer-deps
        
      - name: 构建项目 (UAT环境)
        run: npm run build:uat  # 使用 npm 执行脚本

      - name: 配置 AWS 凭证
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
          
#      - name: 同步文件到 S3 的 admin-fe 目录
#        run: |
#          aws s3 sync dist/ s3://hotelfe/admin-fe/ --delete
  

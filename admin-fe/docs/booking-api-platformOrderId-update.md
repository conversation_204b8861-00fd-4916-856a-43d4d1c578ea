# Book 接口协议调整 - 新增 platformOrderId

## 概述

根据后端接口协议调整，book 接口响应中新增了 `platformOrderId` 字段，后续订单详情查询应该优先使用这个参数。

## 更新内容

### 1. BookingResponse 接口更新

```typescript
// 更新前
export interface BookingResponse {
  supplierOrderId: string
  orderStatus: OrderStatus
}

// 更新后
export interface BookingResponse {
  supplierOrderId: string
  orderStatus: OrderStatus
  platformOrderId: number // 新增：用于后续订单详情查询
}
```

### 2. OrderDetailRequest 接口更新

```typescript
// 更新前
export interface OrderDetailRequest {
  orderId: string
}

// 更新后
export interface OrderDetailRequest {
  orderId?: string // 保留向后兼容性
  platformOrderId?: number // 新增：推荐使用的查询参数
}
```

### 3. 订单详情查询方法更新

所有订单详情查询方法现在都支持两种参数格式：

```typescript
// 方式1：传统方式（向后兼容）
const orderDetail = await orderApi.getTenantOrderDetail('order123')

// 方式2：使用 platformOrderId（推荐）
const orderDetail = await orderApi.getTenantOrderDetail({ 
  platformOrderId: 3 
})

// 方式3：使用 orderId 对象格式
const orderDetail = await orderApi.getTenantOrderDetail({ 
  orderId: 'order123' 
})
```

## 使用示例

### 完整的预订流程

```typescript
import { bookingApi, orderApi } from '@/api/booking'

// 1. 创建预订
const bookingRequest = {
  ratePkgId: 'rate123',
  guests: [
    { firstName: 'John', lastName: 'Doe' }
  ]
}

try {
  // 2. 调用预订接口
  const bookingResponse = await bookingApi.createBooking(bookingRequest)
  
  console.log('预订成功:', {
    supplierOrderId: bookingResponse.supplierOrderId,
    orderStatus: bookingResponse.orderStatus,
    platformOrderId: bookingResponse.platformOrderId // 新字段
  })

  // 3. 使用 platformOrderId 查询订单详情（推荐方式）
  const orderDetail = await orderApi.getTenantOrderDetail({
    platformOrderId: bookingResponse.platformOrderId
  })

  console.log('订单详情:', orderDetail)

} catch (error) {
  console.error('预订失败:', error)
}
```

### 使用辅助函数

```typescript
// 使用新增的辅助函数创建订单详情查询请求
const bookingResponse = await bookingApi.createBooking(bookingRequest)
const detailRequest = bookingApi.createOrderDetailRequest(bookingResponse)

// detailRequest = { platformOrderId: 3 }
const orderDetail = await orderApi.getTenantOrderDetail(detailRequest)
```

## 后端响应示例

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "supplierOrderId": "HB3",
    "orderStatus": 1,
    "platformOrderId": 3
  }
}
```

## 迁移指南

### 对于现有代码

1. **无需立即修改**：现有使用 `orderId` 的代码仍然可以正常工作
2. **推荐升级**：新的预订流程建议使用 `platformOrderId` 查询订单详情
3. **逐步迁移**：可以逐步将现有代码迁移到新的 API 格式

### 迁移步骤

1. 确保预订成功后保存 `platformOrderId`
2. 在查询订单详情时优先使用 `platformOrderId`
3. 保留 `orderId` 作为备用查询方式

## 注意事项

1. `platformOrderId` 是数字类型，`orderId` 是字符串类型
2. 新的预订响应中会同时包含 `supplierOrderId` 和 `platformOrderId`
3. 订单详情查询接口现在支持两种参数格式，但推荐使用 `platformOrderId`
4. 所有相关的 API 方法都已更新以支持新的参数格式

## 相关文件

- `admin-fe/src/api/booking/bookingApi.ts` - 预订 API 接口定义
- `admin-fe/src/api/booking/orderApi.ts` - 订单 API 接口定义
- `admin-fe/src/views/booking/order-management/` - 订单管理页面

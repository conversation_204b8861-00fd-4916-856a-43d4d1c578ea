# 城市搜索功能实现文档

## 概述

根据用户需求："这里输入城市需要调用后端的(s *SearchService) Search接口拿到regionId，以便后续传参"，我们实现了完整的城市搜索自动完成功能。

## 实现的功能

### 1. 后端API对接

#### 接口信息
- **接口路径**: `/api/search`
- **请求方法**: POST
- **功能**: 通过关键词搜索城市、地点和酒店

#### 请求格式
```typescript
interface CitySearchRequest {
  keyword: string
  context?: {
    regionId?: string
  }
}
```

#### 响应格式
```typescript
// 后端API响应格式
interface ApiResponse<T> {
  code: number
  data: T
  msg: string
}

// 搜索结果数据格式
interface CitySearchResponse {
  candidates: SearchItem[]
}

interface SearchItem {
  type: 'city' | 'place' | 'hotel'
  region?: Region
  place?: {
    latlngCoordinator?: LatlngCoordinator
  }
  hotel?: any
}

interface Region {
  id: number
  name: string
  nameFull: string
  type: string
  countryCode: string
  countrySubdivisionCode?: string
  expediaId?: string
  tripId?: string
  coordinates?: any
  ancestors?: Region[]
  descendants?: Region[]
  extra?: any
}
```

### 2. 前端组件实现

#### CitySearchInput 组件
- **文件位置**: `src/components/booking/CitySearchInput.vue`
- **功能特性**:
  - 自动完成输入框
  - 防抖搜索（300ms）
  - 城市建议列表显示
  - 选择城市后获取regionId
  - 清空功能
  - 加载状态显示

#### 组件使用方法
```vue
<template>
  <CitySearchInput
    v-model="cityName"
    placeholder="请输入目的地城市"
    @select="handleCitySelect"
    @clear="handleCityClear"
    ref="citySearchRef"
  />
</template>

<script setup>
const handleCitySelect = (city) => {
  console.log('Selected city:', city)
  console.log('Region ID:', city.regionId)
}

const handleCityClear = () => {
  console.log('City cleared')
}
</script>
```

### 3. 酒店搜索页面集成

#### 更新内容
- **文件位置**: `src/views/booking/hotel-search/index.vue`
- **主要改动**:
  1. 替换原有的普通输入框为 `CitySearchInput` 组件
  2. 添加城市选择处理逻辑
  3. 更新搜索参数，优先使用 `regionId` 而不是 `regionName`
  4. 添加表单验证，确保选择了目的地

#### 搜索逻辑优化
```typescript
// 优先使用regionId进行搜索
if (selectedCity.value) {
  searchParams.regionId = selectedCity.value.regionId.toString()
  console.log('Searching with regionId:', searchParams.regionId)
} else if (searchForm.destination) {
  searchParams.regionName = searchForm.destination
  console.log('Searching with regionName:', searchParams.regionName)
}
```

### 4. API服务扩展

#### 新增方法
- **文件位置**: `src/api/booking/searchApi.ts`
- **新增方法**: `searchCities()`

```typescript
// 城市搜索API
async searchCities(params: CitySearchRequest): Promise<CitySearchResponse> {
  try {
    const requestData: any = {
      keyword: params.keyword
    }

    if (params.context) {
      requestData.context = params.context
    }

    const response = await request.post<CitySearchResponse>({
      url: '/api/search',
      data: requestData
    })

    // 注意：axios响应拦截器已经处理了后端的 { code, data, msg } 格式
    // 当code=0时，拦截器直接返回data字段，所以response已经是 { candidates: [...] }
    return response || {
      candidates: []
    }
  } catch (error) {
    console.error('City search failed:', error)
    throw new Error('城市搜索失败')
  }
}
```

## 测试页面

### 测试页面位置
- **文件位置**: `src/views/booking/test-city-search.vue`
- **功能**: 独立测试城市搜索组件的功能

### 测试功能
1. 输入城市名称进行搜索
2. 显示搜索结果列表
3. 选择城市并获取详细信息
4. 清空选择功能
5. 显示选择的城市信息（包括regionId）

## 技术特点

### 1. 用户体验优化
- **防抖搜索**: 避免频繁API调用
- **加载状态**: 提供视觉反馈
- **清空功能**: 方便用户重新选择
- **错误处理**: 友好的错误提示

### 2. 数据处理
- **类型安全**: 完整的TypeScript类型定义
- **数据转换**: 后端数据格式到前端显示格式的转换
- **regionId优先**: 优先使用精确的regionId进行搜索

### 3. 组件设计
- **可复用**: 组件可在多个页面使用
- **可配置**: 支持自定义placeholder等属性
- **事件驱动**: 通过事件与父组件通信
- **方法暴露**: 提供编程式操作接口

## 使用流程

1. **用户输入**: 在目的地输入框中输入城市名称
2. **自动搜索**: 组件自动调用后端 `/api/search` 接口
3. **显示建议**: 展示匹配的城市列表
4. **选择城市**: 用户点击选择城市
5. **获取regionId**: 组件获取并存储选择城市的regionId
6. **酒店搜索**: 使用regionId进行精确的酒店搜索

## 后续优化建议

1. **缓存机制**: 添加搜索结果缓存，减少重复请求
2. **历史记录**: 保存用户最近搜索的城市
3. **地理位置**: 集成地理位置API，提供附近城市建议
4. **多语言**: 支持多语言城市名称显示
5. **性能优化**: 虚拟滚动支持大量搜索结果

## 总结

本次实现完全满足了用户的需求，通过调用后端 `/api/search` 接口（对应 `(s *SearchService) Search` 方法）获取regionId，并在后续的酒店搜索中使用这个精确的regionId参数，提高了搜索的准确性和用户体验。

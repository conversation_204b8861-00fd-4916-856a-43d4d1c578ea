import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Hotel, HotelSearchRequest, CitySearchHistoryItem } from '@/api/booking/searchApi'
import type { Order } from '@/api/booking/orderApi'

export interface BookingState {
  // Search state
  searchParams: HotelSearchRequest | null
  searchResults: Hotel[]
  searchLoading: boolean
  searchTotal: number

  // City search history
  citySearchHistory: CitySearchHistoryItem[]

  // Selected hotel/room state
  selectedHotel: Hotel | null
  selectedRoom: any | null
  selectedRate: any | null

  // Booking state
  currentBooking: any | null
  bookingStep: number

  // Orders state
  recentOrders: Order[]
  ordersLoading: boolean
}

export const useBookingStore = defineStore('booking', () => {
  // State
  const searchParams = ref<HotelSearchRequest | null>(null)
  const searchResults = ref<Hotel[]>([])
  const searchLoading = ref(false)
  const searchTotal = ref(0)

  // City search history state
  const citySearchHistory = ref<CitySearchHistoryItem[]>([])

  const selectedHotel = ref<Hotel | null>(null)
  const selectedRoom = ref<any | null>(null)
  const selectedRate = ref<any | null>(null)

  const currentBooking = ref<any | null>(null)
  const bookingStep = ref(1)

  const recentOrders = ref<Order[]>([])
  const ordersLoading = ref(false)
  
  // Getters
  const hasSearchResults = computed(() => searchResults.value.length > 0)
  const hasSelectedRoom = computed(() => selectedRoom.value && selectedRate.value)
  const isBookingInProgress = computed(() => currentBooking.value !== null)
  
  const searchSummary = computed(() => {
    if (!searchParams.value) return null
    
    return {
      destination: searchParams.value.destination,
      checkIn: searchParams.value.checkIn,
      checkOut: searchParams.value.checkOut,
      guests: `${searchParams.value.adults} 成人${searchParams.value.children ? `, ${searchParams.value.children} 儿童` : ''}`,
      rooms: `${searchParams.value.rooms} 间房`
    }
  })
  
  const nightsCount = computed(() => {
    if (!searchParams.value?.checkIn || !searchParams.value?.checkOut) return 0
    
    const checkIn = new Date(searchParams.value.checkIn)
    const checkOut = new Date(searchParams.value.checkOut)
    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
  })
  
  // Actions
  const setSearchParams = (params: HotelSearchRequest) => {
    searchParams.value = params
  }
  
  const setSearchResults = (results: Hotel[], total: number = 0) => {
    searchResults.value = results
    searchTotal.value = total
  }
  
  const setSearchLoading = (loading: boolean) => {
    searchLoading.value = loading
  }
  
  const clearSearchResults = () => {
    searchResults.value = []
    searchTotal.value = 0
  }
  
  const selectHotel = (hotel: Hotel) => {
    selectedHotel.value = hotel
    // Clear previous room/rate selection when selecting a new hotel
    selectedRoom.value = null
    selectedRate.value = null
  }
  
  const selectRoom = (room: any, rate: any) => {
    selectedRoom.value = room
    selectedRate.value = rate
  }
  
  const clearSelection = () => {
    selectedHotel.value = null
    selectedRoom.value = null
    selectedRate.value = null
  }
  
  const startBooking = (bookingData: any) => {
    currentBooking.value = bookingData
    bookingStep.value = 1
  }
  
  const updateBookingStep = (step: number) => {
    bookingStep.value = step
  }
  
  const completeBooking = () => {
    currentBooking.value = null
    bookingStep.value = 1
    clearSelection()
  }
  
  const cancelBooking = () => {
    currentBooking.value = null
    bookingStep.value = 1
  }
  
  const setRecentOrders = (orders: Order[]) => {
    recentOrders.value = orders
  }
  
  const setOrdersLoading = (loading: boolean) => {
    ordersLoading.value = loading
  }
  
  const addOrder = (order: Order) => {
    recentOrders.value.unshift(order)
    // Keep only the most recent 10 orders
    if (recentOrders.value.length > 10) {
      recentOrders.value = recentOrders.value.slice(0, 10)
    }
  }
  
  const updateOrderStatus = (orderId: string, status: string) => {
    const order = recentOrders.value.find(o => o.orderId === orderId)
    if (order) {
      order.status = status as any
    }
  }

  // City search history management
  const addCityToHistory = (city: CitySearchHistoryItem) => {
    // Remove existing entry if it exists (to avoid duplicates)
    const existingIndex = citySearchHistory.value.findIndex(
      item => item.regionId === city.regionId
    )
    if (existingIndex !== -1) {
      citySearchHistory.value.splice(existingIndex, 1)
    }

    // Add to the beginning of the array
    citySearchHistory.value.unshift(city)

    // Keep only the most recent 10 cities
    if (citySearchHistory.value.length > 10) {
      citySearchHistory.value = citySearchHistory.value.slice(0, 10)
    }
  }

  const removeCityFromHistory = (regionId: string) => {
    const index = citySearchHistory.value.findIndex(item => item.regionId === regionId)
    if (index !== -1) {
      citySearchHistory.value.splice(index, 1)
    }
  }

  const clearCityHistory = () => {
    citySearchHistory.value = []
  }

  // Reset all state
  const resetStore = () => {
    searchParams.value = null
    searchResults.value = []
    searchLoading.value = false
    searchTotal.value = 0
    citySearchHistory.value = []
    selectedHotel.value = null
    selectedRoom.value = null
    selectedRate.value = null
    currentBooking.value = null
    bookingStep.value = 1
    recentOrders.value = []
    ordersLoading.value = false
  }
  
  return {
    // State
    searchParams,
    searchResults,
    searchLoading,
    searchTotal,
    citySearchHistory,
    selectedHotel,
    selectedRoom,
    selectedRate,
    currentBooking,
    bookingStep,
    recentOrders,
    ordersLoading,

    // Getters
    hasSearchResults,
    hasSelectedRoom,
    isBookingInProgress,
    searchSummary,
    nightsCount,

    // Actions
    setSearchParams,
    setSearchResults,
    setSearchLoading,
    clearSearchResults,
    addCityToHistory,
    removeCityFromHistory,
    clearCityHistory,
    selectHotel,
    selectRoom,
    clearSelection,
    startBooking,
    updateBookingStep,
    completeBooking,
    cancelBooking,
    setRecentOrders,
    setOrdersLoading,
    addOrder,
    updateOrderStatus,
    resetStore
  }
}, {
  persist: {
    key: 'booking-store',
    storage: localStorage,
    paths: ['citySearchHistory'] // Only persist city search history
  }
})

// Export type for use in components
export type BookingStore = ReturnType<typeof useBookingStore>

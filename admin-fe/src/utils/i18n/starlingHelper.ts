/**
 * Starling多语言处理工具
 * 统一处理后端starlingMap和前端语言包
 */

import { useI18n } from 'vue-i18n'
import { LanguageEnum } from '@/enums/appEnum'
import { getI18nText } from './getI18nText'

// 全局starlingMap存储
let globalStarlingMap: Record<string, any> = {}

/**
 * 设置全局starlingMap
 * @param starlingMap 从后端获取的starlingMap
 */
export function setGlobalStarlingMap(starlingMap: Record<string, any>) {
  globalStarlingMap = starlingMap || {}
  console.log('[starling] 设置全局starlingMap:', Object.keys(globalStarlingMap).length, '条')
}

/**
 * 获取全局starlingMap
 */
export function getGlobalStarlingMap(): Record<string, any> {
  return globalStarlingMap
}

/**
 * 统一的多语言文本获取函数
 * 优先级：后端专门接口下发的i18n > 后端全局starlingMap > 前端语言包 > 兜底文本
 * @param key 多语言键名或i18n对象
 * @param fallback 兜底文本
 * @param currentLang 当前语言
 * @returns 多语言文本
 */
export function useStarlingText() {
  const { t, locale } = useI18n()

  return (key: string | any, fallback?: string): string => {
    const currentLang = locale.value

    // 1. 优先级最高：如果传入的是i18n对象（后端专门接口下发）
    if (typeof key === 'object' && key !== null) {
      const text = getI18nText(key, currentLang)
      if (text) {
        return text
      }
    }

    // 2. 如果传入的是字符串key，则按优先级处理
    if (typeof key === 'string') {
      // 2.1 使用全局starlingMap
      if (globalStarlingMap[key]) {
        const i18nText = globalStarlingMap[key]
        const text = getI18nText(i18nText, currentLang)
        if (text) {
          return text
        }
      }

      // 2.2 使用前端语言包
      try {
        const frontendText = t(key)
        if (frontendText && frontendText !== key) {
          return frontendText
        }
      } catch (error) {
        // 忽略翻译错误
      }
    }

    // 3. 使用兜底文本
    if (fallback) {
      return fallback
    }

    // 4. 最后返回key本身或空字符串
    return typeof key === 'string' ? key : ''
  }
}

/**
 * 检查starlingMap中是否存在指定的key
 * @param key 多语言键名
 * @returns 是否存在
 */
export function hasStarlingKey(key: string): boolean {
  return !!globalStarlingMap[key]
}

/**
 * 获取starlingMap中指定key的所有语言版本
 * @param key 多语言键名
 * @returns 所有语言版本的对象
 */
export function getStarlingAllLanguages(key: string): Record<string, string> {
  const i18nText = globalStarlingMap[key]
  if (!i18nText || typeof i18nText !== 'object') {
    return {}
  }
  return i18nText
}

/**
 * 专门处理后端接口下发的i18n对象
 * 这是最高优先级的多语言处理方式
 * @param i18nObj 后端下发的i18n对象 { zh: '', en: '', ar: '' }
 * @param currentLang 当前语言
 * @returns 多语言文本
 */
export function useBackendI18nText() {
  const { locale } = useI18n()

  return (i18nObj: any, fallback?: string): string => {
    if (!i18nObj || typeof i18nObj !== 'object') {
      return fallback || ''
    }

    const text = getI18nText(i18nObj, locale.value)
    return text || fallback || ''
  }
}

/**
 * 智能多语言文本处理函数
 * 自动判断输入类型并应用正确的优先级
 * @param input 可以是i18n对象、key字符串
 * @param fallback 兜底文本
 * @returns 多语言文本
 */
export function useSmartI18nText() {
  const getStarlingText = useStarlingText()
  const getBackendI18nText = useBackendI18nText()

  return (input: any, fallback?: string): string => {
    // 如果是对象，优先使用后端i18n处理
    if (typeof input === 'object' && input !== null) {
      return getBackendI18nText(input, fallback)
    }

    // 如果是字符串，使用starling处理
    if (typeof input === 'string') {
      return getStarlingText(input, fallback)
    }

    // 其他情况返回兜底文本
    return fallback || ''
  }
}

/**
 * 批量获取多语言文本
 * @param keys 多语言键名数组
 * @param currentLang 当前语言
 * @returns 键值对映射
 */
export function getBatchStarlingText(keys: string[], currentLang?: string): Record<string, string> {
  const result: Record<string, string> = {}
  const lang = currentLang || LanguageEnum.ZH

  keys.forEach(key => {
    if (globalStarlingMap[key]) {
      const i18nText = globalStarlingMap[key]
      result[key] = getI18nText(i18nText, lang)
    }
  })

  return result
}

/**
 * 初始化starlingMap（在应用启动时调用）
 */
export async function initializeStarlingMap() {
  try {
    // 这里可以调用API获取starlingMap
    // 暂时使用空对象，实际使用时需要从API获取
    console.log('[starling] 初始化starlingMap...')
    
    // 可以在这里调用menuApiV2.getStarlingMap()
    // const starlingMap = await menuApiV2.getStarlingMap()
    // setGlobalStarlingMap(starlingMap)
  } catch (error) {
    console.error('[starling] 初始化starlingMap失败:', error)
  }
}

/**
 * 清空starlingMap
 */
export function clearStarlingMap() {
  globalStarlingMap = {}
  console.log('[starling] 已清空starlingMap')
}

/**
 * 获取starlingMap统计信息
 */
export function getStarlingMapStats() {
  const totalKeys = Object.keys(globalStarlingMap).length
  const spaces = new Set<string>()
  const applications = new Set<string>()

  Object.keys(globalStarlingMap).forEach(key => {
    const parts = key.split('.')
    if (parts.length >= 2) {
      spaces.add(parts[0])
      if (parts.length >= 3) {
        applications.add(parts[0] + '.' + parts[1])
      }
    }
  })

  return {
    totalKeys,
    spacesCount: spaces.size,
    applicationsCount: applications.size,
    spaces: Array.from(spaces),
    applications: Array.from(applications)
  }
}

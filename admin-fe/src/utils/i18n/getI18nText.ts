/**
 * 通用多语言文本处理工具函数
 */

import { useI18n } from 'vue-i18n'
import { LanguageEnum } from '@/enums/appEnum'

/**
 * 多语言文本对象接口
 */
export interface I18N {
  zh?: string
  en?: string
  ar?: string
}

/**
 * 根据当前语言设置获取多语言文本
 * @param i18nText 多语言文本对象 { zh: '', en: '', ar: '' }
 * @param currentLang 可选的当前语言设置，如果不传则自动获取
 * @returns 当前语言对应的文本，带兜底逻辑
 */
export function getI18nText(i18nText: any, currentLang?: string): string {
  if (!i18nText || typeof i18nText !== 'object') {
    return String(i18nText || '')
  }

  // 如果没有传入当前语言，则获取当前语言设置
  let lang = currentLang
  if (!lang) {
    try {
      const { locale } = useI18n()
      lang = locale.value
    } catch {
      // 如果在非组件环境中调用，使用默认语言
      lang = LanguageEnum.ZH
    }
  }

  // 优先使用当前语言设置
  if (i18nText[lang]) {
    return i18nText[lang]
  }

  // 兜底逻辑：按优先级选择可用语言
  const fallbackOrder = [LanguageEnum.ZH, LanguageEnum.EN, 'ar']

  for (const fallbackLang of fallbackOrder) {
    if (i18nText[fallbackLang]) {
      return i18nText[fallbackLang]
    }
  }

  // 最后兜底：返回第一个非空值或空字符串
  const firstValue = Object.values(i18nText).find(v => v && String(v).trim())
  return firstValue ? String(firstValue) : ''
}

/**
 * 根据用户语言设置获取多语言文本（组合式API版本）
 * 这个版本必须在 Vue 组件内部使用
 * @param i18nText 多语言文本对象 { zh: '', en: '', ar: '' }
 * @returns 当前语言对应的文本，带兜底逻辑
 */
export function useI18nText() {
  const { locale } = useI18n()

  return (i18nText: any): string => {
    return getI18nText(i18nText, locale.value)
  }
}

/**
 * 简化版多语言文本提取函数（兼容旧代码）
 * @param i18nObj 多语言文本对象
 * @param fallback 兜底文本
 * @returns 提取的文本
 */
export function extractI18nText(i18nObj: I18N, fallback: string = ''): string {
  if (!i18nObj || typeof i18nObj !== 'object') return fallback
  return i18nObj.zh || i18nObj.en || i18nObj.ar || Object.values(i18nObj)[0] || fallback
}

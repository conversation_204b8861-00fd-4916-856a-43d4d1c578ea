/**
 * 实体类型定义 - 支持位运算的多类型系统
 */

// 实体类型枚举 - 使用位运算支持多种类型组合
export enum EntityType {
  Platform = 1,    // 1 << 0 = 1
  Tenant = 2,      // 1 << 1 = 2  
  Customer = 4,    // 1 << 2 = 4
  Extranet = 8,    // 1 << 3 = 8
  Supplier = 16    // 1 << 4 = 16
}

// 常用的组合类型
export const EntityTypeCombinations = {
  TenantSupplier: EntityType.Tenant | EntityType.Supplier,
  All: EntityType.Platform | EntityType.Tenant | EntityType.Customer | EntityType.Extranet | EntityType.Supplier
} as const

// 实体类型工具类
export class EntityTypeUtils {
  /**
   * 检查实体是否包含指定类型
   */
  static hasType(entityType: number, targetType: EntityType): boolean {
    return (entityType & targetType) !== 0
  }

  /**
   * 添加类型
   */
  static addType(entityType: number, targetType: EntityType): number {
    return entityType | targetType
  }

  /**
   * 移除类型
   */
  static removeType(entityType: number, targetType: EntityType): number {
    return entityType & ~targetType
  }

  /**
   * 获取类型的字符串表示
   */
  static getTypeString(entityType: number, language: 'zh' | 'en' | 'ar' = 'zh'): string {
    const types: string[] = []
    
    const typeMap = {
      zh: {
        [EntityType.Platform]: '平台',
        [EntityType.Tenant]: '租户', 
        [EntityType.Customer]: '客户',
        [EntityType.Extranet]: '外网',
        [EntityType.Supplier]: '供应商'
      },
      en: {
        [EntityType.Platform]: 'Platform',
        [EntityType.Tenant]: 'Tenant',
        [EntityType.Customer]: 'Customer', 
        [EntityType.Extranet]: 'Extranet',
        [EntityType.Supplier]: 'Supplier'
      },
      ar: {
        [EntityType.Platform]: 'منصة',
        [EntityType.Tenant]: 'مستأجر',
        [EntityType.Customer]: 'عميل',
        [EntityType.Extranet]: 'شبكة خارجية', 
        [EntityType.Supplier]: 'مورد'
      }
    }

    const currentMap = typeMap[language]
    
    if (this.hasType(entityType, EntityType.Platform)) {
      types.push(currentMap[EntityType.Platform])
    }
    if (this.hasType(entityType, EntityType.Tenant)) {
      types.push(currentMap[EntityType.Tenant])
    }
    if (this.hasType(entityType, EntityType.Customer)) {
      types.push(currentMap[EntityType.Customer])
    }
    if (this.hasType(entityType, EntityType.Extranet)) {
      types.push(currentMap[EntityType.Extranet])
    }
    if (this.hasType(entityType, EntityType.Supplier)) {
      types.push(currentMap[EntityType.Supplier])
    }

    return types.length > 0 ? types.join('|') : (language === 'zh' ? '未知' : language === 'en' ? 'Unknown' : 'غير معروف')
  }

  /**
   * 获取所有可用的类型选项
   */
  static getTypeOptions(language: 'zh' | 'en' | 'ar' = 'zh') {
    const typeMap = {
      zh: [
        { label: '平台', value: EntityType.Platform },
        { label: '租户', value: EntityType.Tenant },
        { label: '客户', value: EntityType.Customer },
        { label: '外网', value: EntityType.Extranet },
        { label: '供应商', value: EntityType.Supplier }
      ],
      en: [
        { label: 'Platform', value: EntityType.Platform },
        { label: 'Tenant', value: EntityType.Tenant },
        { label: 'Customer', value: EntityType.Customer },
        { label: 'Extranet', value: EntityType.Extranet },
        { label: 'Supplier', value: EntityType.Supplier }
      ],
      ar: [
        { label: 'منصة', value: EntityType.Platform },
        { label: 'مستأجر', value: EntityType.Tenant },
        { label: 'عميل', value: EntityType.Customer },
        { label: 'شبكة خارجية', value: EntityType.Extranet },
        { label: 'مورد', value: EntityType.Supplier }
      ]
    }
    
    return typeMap[language]
  }

  /**
   * 从类型数组创建组合类型
   */
  static createCombinedType(types: EntityType[]): number {
    return types.reduce((combined, type) => combined | type, 0)
  }

  /**
   * 将组合类型分解为单个类型数组
   */
  static decomposeCombinedType(combinedType: number): EntityType[] {
    const types: EntityType[] = []
    
    if (this.hasType(combinedType, EntityType.Platform)) {
      types.push(EntityType.Platform)
    }
    if (this.hasType(combinedType, EntityType.Tenant)) {
      types.push(EntityType.Tenant)
    }
    if (this.hasType(combinedType, EntityType.Customer)) {
      types.push(EntityType.Customer)
    }
    if (this.hasType(combinedType, EntityType.Extranet)) {
      types.push(EntityType.Extranet)
    }
    if (this.hasType(combinedType, EntityType.Supplier)) {
      types.push(EntityType.Supplier)
    }
    
    return types
  }
}

// 实体接口定义
export interface Entity {
  id: string
  type: number  // 使用number类型支持位运算
  name: string
  rootEntityId: string
  parentEntityId: string
  profile: Record<string, any>
  distributionConfig: Record<string, any>
  financeConfig: Record<string, any>
  supplierConfig: Record<string, any>
  status: number
  createTime: string
  ancestors?: Entity[]
}

// 实体列表响应
export interface EntityListResponse {
  headerKeys: string[]
  rows: Array<{
    data: Entity
    elements: Array<{
      type: string
      content: any
    }>
  }>
  pageResp: {
    total: number
    hasMore?: boolean
  }
}

// 实体查询请求
export interface EntityListRequest {
  parentEntityIDs?: string[]
  page?: {
    pageSize: number
    offset: number
  }
}

// 创建实体请求
export interface CreateEntityRequest {
  type: number
  name: string
  rootEntityId: string
  parentEntityId: string
  profile?: Record<string, any>
  distributionConfig?: Record<string, any>
  financeConfig?: Record<string, any>
  supplierConfig?: Record<string, any>
}

// 更新实体请求
export interface UpdateEntityRequest extends CreateEntityRequest {
  id: string
}

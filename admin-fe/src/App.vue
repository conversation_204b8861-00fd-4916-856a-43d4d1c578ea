<template>
  <ElConfigProvider size="default" :locale="locales[language]" :z-index="3000">
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { systemUpgrade } from './utils/sys'
  import { UserService } from './api/usersApi'
  import { setThemeTransitionClass } from './utils/theme/animation'
  import { checkStorageCompatibility } from './utils/storage'
  import { setGlobalStarlingMap } from './utils/i18n'
  import { menuApiV2 } from './api/menuApiV2'
  import { internalHomepageCache } from './utils/cache/internalHomepageCache'

  const userStore = useUserStore()
  const { language } = storeToRefs(userStore)

  const locales = {
    zh: zh,
    en: en
  }

  onBeforeMount(() => {
    setThemeTransitionClass(true)

    // 检查是否是首页刷新，如果是则设置强制刷新标识
    const currentPath = window.location.hash.replace('#', '') || '/'
    if (currentPath === '/' || currentPath === '') {
      console.log('[App] 检测到首页刷新，设置强制刷新标识')
      internalHomepageCache.setForceRefresh()
    }
  })

  onMounted(async () => {
    // 检查存储兼容性
    checkStorageCompatibility()
    // 提升暗黑主题下页面刷新视觉体验
    setThemeTransitionClass(false)
    // 系统升级
    systemUpgrade()

    // 初始化starlingMap（仅在用户已登录时）
    if (userStore.isLogin) {
      await initializeStarlingMap()
    }
  })

  // 初始化starlingMap
  const initializeStarlingMap = async () => {
    try {
      console.log('[App] 开始初始化starlingMap...')
      const starlingMap = await menuApiV2.getStarlingMap()
      setGlobalStarlingMap(starlingMap)
      console.log('[App] starlingMap初始化完成，条目数:', Object.keys(starlingMap).length)
    } catch (error) {
      console.error('[App] starlingMap初始化失败:', error)
    }
  }

  // 监听用户登录状态变化，登录后初始化starlingMap
  watch(() => userStore.isLogin, async (isLogin) => {
    if (isLogin) {
      await initializeStarlingMap()
    }
  })
</script>

<template>
  <div ref="editorContainer" class="monaco-editor-container" :style="{ height }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as monaco from 'monaco-editor'

// Props
interface Props {
  value?: string
  language?: string
  theme?: string
  readOnly?: boolean
  height?: string
  options?: monaco.editor.IStandaloneEditorConstructionOptions
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  language: 'json',
  theme: 'vs-dark',
  readOnly: false,
  height: '400px',
  options: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:value': [value: string]
  change: [value: string]
}>()

// 响应式数据
const editorContainer = ref<HTMLElement>()
let editor: monaco.editor.IStandaloneCodeEditor | null = null

// 生命周期
onMounted(async () => {
  await nextTick()
  initEditor()
})

onUnmounted(() => {
  if (editor) {
    editor.dispose()
  }
})

// 监听器
watch(() => props.value, (newValue) => {
  if (editor && newValue !== editor.getValue()) {
    editor.setValue(newValue || '')
  }
})

watch(() => props.language, (newLanguage) => {
  if (editor) {
    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelLanguage(model, newLanguage)
    }
  }
})

watch(() => props.theme, (newTheme) => {
  if (editor) {
    monaco.editor.setTheme(newTheme)
  }
})

watch(() => props.readOnly, (newReadOnly) => {
  if (editor) {
    editor.updateOptions({ readOnly: newReadOnly })
  }
})

// 方法
const initEditor = () => {
  if (!editorContainer.value) return

  // 默认编辑器选项
  const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
    value: props.value,
    language: props.language,
    theme: props.theme,
    readOnly: props.readOnly,
    automaticLayout: true,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 14,
    lineNumbers: 'on',
    roundedSelection: false,
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto'
    },
    wordWrap: 'on',
    folding: true,
    foldingStrategy: 'indentation',
    showFoldingControls: 'always',
    formatOnPaste: true,
    formatOnType: true
  }

  // 合并用户选项
  const editorOptions = { ...defaultOptions, ...props.options }

  // 创建编辑器
  editor = monaco.editor.create(editorContainer.value, editorOptions)

  // 监听内容变化
  editor.onDidChangeModelContent(() => {
    if (editor) {
      const value = editor.getValue()
      emit('update:value', value)
      emit('change', value)
    }
  })

  // 设置 JSON 语言的格式化选项
  if (props.language === 'json') {
    monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
      validate: true,
      allowComments: false,
      schemas: [],
      enableSchemaRequest: false
    })
  }
}

// 暴露方法给父组件
const getValue = () => {
  return editor?.getValue() || ''
}

const setValue = (value: string) => {
  if (editor) {
    editor.setValue(value)
  }
}

const focus = () => {
  if (editor) {
    editor.focus()
  }
}

const formatDocument = () => {
  if (editor) {
    editor.getAction('editor.action.formatDocument')?.run()
  }
}

const resize = () => {
  if (editor) {
    editor.layout()
  }
}

defineExpose({
  getValue,
  setValue,
  focus,
  formatDocument,
  resize,
  editor: () => editor
})
</script>

<style scoped lang="scss">
.monaco-editor-container {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}
</style>

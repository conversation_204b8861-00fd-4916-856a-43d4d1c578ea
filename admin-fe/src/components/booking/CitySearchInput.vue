<template>
  <div class="city-search-input">
    <ElAutocomplete
      v-model="inputValue"
      :fetch-suggestions="querySearchAsync"
      :placeholder="placeholder || t('booking.search.citySearchPlaceholder')"
      :loading="loading"
      :trigger-on-focus="true"
      :debounce="300"
      :hide-loading="false"
      :popper-class="'city-search-popper'"
      clearable
      fit-input-width
      @select="handleSelect"
      @clear="handleClear"
      @input="handleInput"
    >
      <template #default="{ item }">
        <div class="city-suggestion-item" :class="{ 'history-item': item.isHistory }">
          <div class="city-name">
            <ElIcon class="location-icon">
              <Location v-if="!item.isHistory" />
              <ElIcon v-else class="history-icon">
                <svg viewBox="0 0 1024 1024" width="1em" height="1em">
                  <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
                  <path d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.5 1.8-8.5-1.8-11.2z" fill="currentColor"/>
                </svg>
              </ElIcon>
            </ElIcon>
            {{ item.name || item.value }}
            <span v-if="item.isHistory" class="history-label">{{ t('booking.search.recentSearch') }}</span>
          </div>
          <div class="city-full-name">{{ item.nameFull || item.name }}</div>
        </div>
      </template>
    </ElAutocomplete>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElAutocomplete, ElIcon, ElMessage } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { hotelSearchApi } from '@/api/booking/searchApi'
import type { Region, SearchItem, CitySearchHistoryItem } from '@/api/booking/searchApi'
import { useBookingStore } from '@/stores/booking'

interface CityOption {
  value: string
  name: string
  nameFull: string
  regionId: string
  countryCode: string
  type: string
  region: Region
  isHistory?: boolean // Mark if this is from search history
}

interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'select', city: CityOption): void
  (e: 'clear'): void
}

const emit = defineEmits<Emits>()

// i18n
const { t } = useI18n()

// Store
const bookingStore = useBookingStore()

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  disabled: false
})

// 响应式数据
const inputValue = ref(props.modelValue || '')
const loading = ref(false)
const selectedCity = ref<CityOption | null>(null)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue || ''
})

// 监听输入值变化，同步到外部
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

/**
 * 异步查询城市建议
 */
const querySearchAsync = (queryString: string, callback: (suggestions: CityOption[]) => void) => {
  console.log('querySearchAsync called with:', queryString)

  // If query is empty or too short, show search history
  if (!queryString || queryString.length < 2) {
    console.log('Query string too short, returning search history')
    console.log('Current search history:', bookingStore.citySearchHistory)
    const historySuggestions = bookingStore.citySearchHistory.map(item => ({
      value: item.name,
      name: item.name,
      nameFull: item.nameFull,
      regionId: item.regionId,
      countryCode: item.countryCode,
      type: item.type,
      region: {
        id: parseInt(item.regionId),
        name: item.name,
        nameFull: item.nameFull,
        type: item.type,
        countryCode: item.countryCode
      } as Region,
      isHistory: true // Mark as history item
    }))
    console.log('History suggestions:', historySuggestions)
    callback(historySuggestions)
    return
  }

  loading.value = true

  // Use async IIFE to handle the async call
  ;(async () => {
    try {
      console.log('Calling searchCities API...')
      const response = await hotelSearchApi.searchCities({
        keyword: queryString.trim()
      })

    console.log('API response:', response)
    const suggestions: CityOption[] = []

    if (response.candidates && Array.isArray(response.candidates)) {
      console.log('Processing candidates:', response.candidates.length)
      response.candidates.forEach((item: SearchItem, index: number) => {
        console.log(`Candidate ${index}:`, item)

        // 处理所有类型的结果，不仅仅是city
        if (item.region) {
          suggestions.push({
            value: item.region.name,
            name: item.region.name,
            nameFull: item.region.nameFull || item.region.name,
            regionId: item.region.id.toString(), // Convert to string for big number safety
            countryCode: item.region.countryCode,
            type: item.region.type || item.type,
            region: item.region
          })
        }
      })
    } else {
      console.log('No candidates found or candidates is not an array')
    }

      console.log('Final suggestions:', suggestions)
      callback(suggestions)
    } catch (error) {
      console.error('City search failed:', error)
      ElMessage.error(t('booking.search.citySearchFailed'))
      callback([])
    } finally {
      loading.value = false
    }
  })()
}

/**
 * 处理选择城市
 */
const handleSelect = (item: any) => {
  // Type assertion to CityOption since Element Plus passes Record<string, any>
  const cityOption = item as CityOption
  selectedCity.value = cityOption
  inputValue.value = cityOption.name

  // Save to search history (only if not already from history)
  if (!cityOption.isHistory) {
    const historyItem: CitySearchHistoryItem = {
      regionId: cityOption.regionId,
      name: cityOption.name,
      nameFull: cityOption.nameFull,
      countryCode: cityOption.countryCode,
      type: cityOption.type,
      searchedAt: Date.now()
    }
    bookingStore.addCityToHistory(historyItem)
  }

  emit('select', cityOption)
}

/**
 * 处理清空
 */
const handleClear = () => {
  selectedCity.value = null
  inputValue.value = ''
  emit('clear')
}

/**
 * 处理输入
 */
const handleInput = (value: string) => {
  // 如果用户手动输入，清除已选择的城市
  if (selectedCity.value && value !== selectedCity.value.name) {
    selectedCity.value = null
  }
}

/**
 * 获取当前选择的城市信息
 */
const getSelectedCity = (): CityOption | null => {
  return selectedCity.value
}

/**
 * 设置城市（用于外部调用）
 */
const setCity = (city: CityOption) => {
  selectedCity.value = city
  inputValue.value = city.name
}

/**
 * 清空选择
 */
const clear = () => {
  handleClear()
}

// 暴露方法给父组件
defineExpose({
  getSelectedCity,
  setCity,
  clear
})
</script>

<style scoped>
.city-search-input {
  width: 100%;
}

.city-suggestion-item {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.city-name {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.location-icon {
  margin-right: 6px;
  color: #909399;
  font-size: 12px;
}

.city-full-name {
  font-size: 12px;
  color: #909399;
  margin-left: 18px;
}

.city-suggestion-item:hover {
  background-color: #f5f7fa;
}

/* 历史记录样式 */
.history-item {
  background-color: #fafafa;
}

.history-item:hover {
  background-color: #f0f0f0;
}

.history-icon {
  margin-right: 6px;
  color: #909399;
  font-size: 12px;
}

.history-label {
  font-size: 11px;
  color: #909399;
  margin-left: 8px;
  padding: 1px 4px;
  background-color: #e4e7ed;
  border-radius: 2px;
}

/* 自定义 autocomplete 样式 */
:deep(.el-autocomplete) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 下拉建议样式 */
:deep(.el-autocomplete-suggestion) {
  max-height: 300px;
  overflow-y: auto;
  z-index: 9999;
}

:deep(.el-autocomplete-suggestion__item) {
  padding: 0 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-autocomplete-suggestion__item:last-child) {
  border-bottom: none;
}

:deep(.el-autocomplete-suggestion__item.highlighted) {
  background-color: #f5f7fa;
}

/* 确保下拉框显示 */
.city-search-popper {
  z-index: 9999 !important;
}

/* 全局样式 */
:global(.city-search-popper) {
  z-index: 9999 !important;
}

:global(.el-popper.is-light) {
  z-index: 9999 !important;
}
</style>

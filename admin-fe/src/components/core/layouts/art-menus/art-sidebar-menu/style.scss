@use '@styles/variables.scss' as *;

.layout-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 101;
  display: flex;
  height: 100vh;
  user-select: none;
  scrollbar-width: none;

  &.no-border {
    border-right: none !important;
  }

  .dual-menu-left {
    position: relative;
    width: 80px;
    height: 100%;
    border-right: 1px solid var(--art-card-border) !important;

    // 隐藏滚动条
    :deep(.el-scrollbar__bar.is-vertical) {
      display: none;
    }

    .logo {
      margin: auto;
      margin-top: 15px;
      cursor: pointer;
    }

    ul {
      li {
        > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          text-align: center;
          cursor: pointer;
          border-radius: 5px;

          i {
            display: block;
            font-size: 20px;
          }

          span {
            display: block;
            width: 100%;
            font-size: 12px;
          }

          &.is-active {
            background: var(--main-color);

            i,
            span {
              color: #fff !important;
            }
          }
        }
      }
    }

    .switch-btn {
      position: absolute;
      right: 0;
      bottom: 15px;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        display: block;
        align-items: center;
        width: 40px;
        height: 40px;
        font-size: 20px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        border-radius: 5px;
        transition: all 0.1s;

        &:hover {
          background-color: var(--art-gray-200);
        }
      }
    }
  }

  :deep(.badge) {
    position: absolute;
    top: 0;
    right: 20px;
    bottom: 0;
    width: 6px;
    height: 6px;
    margin: auto;
    background: #ff3860;
    border-radius: 50%;
    animation: breathe 1.5s ease-in-out infinite;
  }

  :deep(.text-badge) {
    position: absolute;
    top: 0;
    right: 12px;
    bottom: 0;
    min-width: 20px;
    height: 20px;
    padding: 0 5px;
    margin: auto;
    font-size: 12px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    background: #fd4e4e;
    border-radius: 5px;
  }

  .header {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    height: 60px;
    overflow: hidden;
    line-height: 60px;
    cursor: pointer;

    .logo {
      margin-left: 28px;
    }

    p {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 64px;
      box-sizing: border-box;
      margin-left: 10px;
      font-size: 18px;

      &.is-dual-menu-name {
        right: 0;
        left: 0;
        margin: auto;
        text-align: center;
      }
    }
  }

  .el-menu {
    box-sizing: border-box;
    height: calc(100vh - 60px);
    overflow-y: auto;
    // 防止菜单内的滚动影响整个页面滚动
    overscroll-behavior: contain;
    border-right: 0;
    scrollbar-width: none;
    -ms-scroll-chaining: contain;

    &::-webkit-scrollbar {
      width: 0 !important;
    }
  }

  .menu-model {
    display: none;
  }
}

@media only screen and (max-width: $device-ipad) {
  .layout-sidebar {
    .header {
      display: none;
    }

    .el-menu {
      height: 100vh;
    }

    .el-menu--collapse {
      width: 0;
    }
  }
}

@media only screen and (max-width: $device-ipad) {
  .layout-sidebar {
    width: 0;

    .menu-model {
      position: fixed;
      top: 0;
      left: 0;
      z-index: -1;
      display: block;
      width: 100%;
      height: 100vh;
      background: rgba($color: #000, $alpha: 50%);
      transition: opacity 0.2s ease-in-out;
    }
  }
}

@keyframes breathe {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
  }

  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

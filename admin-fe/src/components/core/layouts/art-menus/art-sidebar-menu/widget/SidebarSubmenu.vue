<template>
  <template v-for="item in filteredMenuItems" :key="item.path">
    <!-- 包含子菜单的项目 -->
    <el-sub-menu v-if="hasChildren(item)" :index="item.path" :level="level">
      <template #title>
        <MenuItemIcon :icon="item.meta.icon" :color="theme?.iconColor" />
        <span class="menu-name">{{ getMenuTitle(item.meta.title) }}</span>
        <div v-if="item.meta.showBadge" class="badge" style="right: 35px" />
      </template>
      <SidebarSubmenu
        :list="item.children"
        :is-mobile="isMobile"
        :level="level + 1"
        :theme="theme"
        @close="closeMenu"
      />
    </el-sub-menu>

    <!-- 普通菜单项 -->
    <el-menu-item
      v-else
      :index="item.path"
      :level-item="level + 1"
      @click="goPage(item)"
    >
      <MenuItemIcon :icon="item.meta.icon" :color="theme?.iconColor" />
      <template #title>
        <span class="menu-name">{{ getMenuTitle(item.meta.title) }}</span>
        <div v-if="item.meta.showBadge" class="badge" />
        <div v-if="item.meta.showTextBadge" class="text-badge">
          {{ item.meta.showTextBadge }}
        </div>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import type { AppRouteRecord } from '@/types/router'
  import { formatMenuTitle } from '@/router/utils/utils'
  import { handleMenuJump } from '@/utils/navigation'
  import { useI18n } from 'vue-i18n'

  // 类型定义
  interface Props {
    title?: string
    list?: AppRouteRecord[]
    theme?: {
      iconColor?: string
    }
    isMobile?: boolean
    level?: number
  }

  // Props定义
  const props = withDefaults(defineProps<Props>(), {
    title: '',
    list: () => [],
    theme: () => ({}),
    isMobile: false,
    level: 0
  })

  // Emits定义
  const emit = defineEmits<{
    (e: 'close'): void
  }>()

  // 获取当前语言设置
  const { locale } = useI18n()

  // 计算属性
  const filteredMenuItems = computed(() => filterRoutes(props.list))

  // 响应式菜单标题获取函数
  const getMenuTitle = (title: any) => {
    // 传入当前语言设置，确保语言切换时重新计算
    return formatMenuTitle(title, locale.value)
  }

  // 跳转页面
  const goPage = (item: AppRouteRecord) => {
    closeMenu()
    handleMenuJump(item)
  }

  // 关闭菜单
  const closeMenu = () => emit('close')

  // 判断是否有子菜单
  const hasChildren = (item: AppRouteRecord): boolean => {
    return Boolean(item.children?.length)
  }

  // 过滤菜单项
  const filterRoutes = (items: AppRouteRecord[]): AppRouteRecord[] => {
    return items
      .filter((item) => !item.meta.isHide)
      .map((item) => ({
        ...item,
        children: item.children ? filterRoutes(item.children) : undefined
      }))
  }
</script>

<script lang="ts">
  // 抽取图标组件
  const MenuItemIcon = defineComponent({
    name: 'MenuItemIcon',
    props: {
      icon: String,
      color: String
    },
    setup(props) {
      // 图标映射表：将后端下发的字符串映射为对应的图标编码
      const iconMap: Record<string, string> = {
        'dashboard': '&#xe6d0;',
        'booking': '&#xe6d1;',
        'entity': '&#xe6d2;',
        'user': '&#xe6d3;',
        'setting': '&#xe6d4;',
        'console': '&#xe6d0;',
        'hotel-search': '&#xe6d1;',
        'order-management': '&#xe6d5;',
        'analytics': '&#xe6d6;',
        'tenant-users': '&#xe6d3;',
        'customer-users': '&#xe6d7;',
        'entity-list': '&#xe6d2;',
        'entity-settings': '&#xe6d8;',
        'user-center': '&#xe6d9;',
        'system-settings': '&#xe6da;'
      }

      return () => {
        // 如果 icon 是HTML实体编码（以 &#x 开头），直接使用
        if (props.icon && props.icon.startsWith('&#x')) {
          return h('i', {
            class: 'menu-icon iconfont-sys',
            style: props.color ? { color: props.color } : undefined,
            innerHTML: props.icon
          })
        }

        // 如果 icon 是字符串，从映射表中查找对应的编码
        const iconCode = props.icon ? iconMap[props.icon] || '&#xe6d0;' : '&#xe6d0;'
        return h('i', {
          class: 'menu-icon iconfont-sys',
          style: props.color ? { color: props.color } : undefined,
          innerHTML: iconCode
        })
      }
    }
  })
</script>

<template>
  <div class="art-json-editor">
    <div ref="editorContainer" class="vscode-editor-container" :style="{ height }"></div>
    <div v-if="errorMessage" class="error-message">
      <el-alert :title="errorMessage" type="error" :closable="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { ElAlert, ElMessage } from 'element-plus'
import * as monaco from 'monaco-editor'

defineOptions({ name: 'ArtJsonEditor' })

interface Props {
  modelValue?: string
  height?: string
  theme?: 'vs' | 'vs-dark'
  readonly?: boolean
  placeholder?: string
  autoFormat?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'validate', isValid: boolean, errors?: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  height: '300px',
  theme: 'vs',
  readonly: false,
  placeholder: '请输入JSON配置',
  autoFormat: true
})

const emit = defineEmits<Emits>()

const editorContainer = ref<HTMLElement>()
const errorMessage = ref('')
let monacoEditor: monaco.editor.IStandaloneCodeEditor | null = null

// 初始化VSCode Web版本编辑器
const initVSCodeEditor = async () => {
  if (!editorContainer.value) return

  try {
    console.log('开始初始化VSCode Web版本编辑器...')

    // 创建Monaco Editor实例（VSCode的核心编辑器）
    monacoEditor = monaco.editor.create(editorContainer.value, {
      value: formatJsonValue(props.modelValue),
      language: 'json',
      theme: props.theme,
      readOnly: props.readonly,
      automaticLayout: true,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      lineNumbers: 'on',
      folding: true,
      formatOnPaste: true,
      formatOnType: true,
      tabSize: 2,
      insertSpaces: true,
      bracketPairColorization: { enabled: true },
      suggest: {
        showKeywords: true,
        showSnippets: true
      },
      quickSuggestions: {
        other: true,
        comments: false,
        strings: true
      }
    })

    console.log('VSCode编辑器初始化成功:', monacoEditor)

    // 创建编辑器实例
    monacoEditor = monaco.editor.create(editorContainer.value, {
      value: formatJsonValue(props.modelValue),
      language: 'json',
      theme: props.theme,
      readOnly: props.readonly,
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      lineNumbers: 'on',
      folding: true,
      formatOnPaste: true,
      formatOnType: true,
      tabSize: 2,
      insertSpaces: true,
      bracketPairColorization: { enabled: true },
      suggest: {
        showKeywords: true,
        showSnippets: true
      },
      quickSuggestions: {
        other: true,
        comments: false,
        strings: true
      }
    })

    // 监听内容变化
    monacoEditor.onDidChangeModelContent(() => {
      const value = monacoEditor?.getValue() || ''
      emit('update:modelValue', value)
      emit('change', value)
      validateJson(value)
    })

    // 设置占位符
    if (props.placeholder && !props.modelValue) {
      setPlaceholder()
    }

    // 初始验证
    validateJson(props.modelValue)

  } catch (error) {
    console.error('Monaco Editor 初始化失败:', error)
    ElMessage.error('JSON编辑器初始化失败')
  }
}

// 格式化JSON值
const formatJsonValue = (value: string): string => {
  if (!value || !props.autoFormat) return value
  
  try {
    const parsed = JSON.parse(value)
    return JSON.stringify(parsed, null, 2)
  } catch {
    return value
  }
}

// 验证JSON格式
const validateJson = (value: string) => {
  if (!value.trim()) {
    errorMessage.value = ''
    emit('validate', true)
    return
  }

  try {
    JSON.parse(value)
    errorMessage.value = ''
    emit('validate', true)
  } catch (error) {
    const err = error as Error
    errorMessage.value = `JSON格式错误: ${err.message}`
    emit('validate', false, [err.message])
  }
}

// 设置占位符
const setPlaceholder = () => {
  if (!monacoEditor) return
  
  // 使用Monaco的装饰器API添加占位符
  monacoEditor.deltaDecorations([], [{
    range: new (window as any).monaco.Range(1, 1, 1, 1),
    options: {
      afterContentClassName: 'monaco-placeholder',
      isWholeLine: true
    }
  }])
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (monacoEditor && monacoEditor.getValue() !== newValue) {
    const formattedValue = formatJsonValue(newValue || '')
    monacoEditor.setValue(formattedValue)
    validateJson(formattedValue)
  }
})

watch(() => props.theme, (newTheme) => {
  if (monacoEditor) {
    (window as any).monaco.editor.setTheme(newTheme)
  }
})

// 暴露方法
const formatJson = () => {
  if (!monacoEditor) return
  
  const value = monacoEditor.getValue()
  try {
    const parsed = JSON.parse(value)
    const formatted = JSON.stringify(parsed, null, 2)
    monacoEditor.setValue(formatted)
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

const getValue = () => monacoEditor?.getValue() || ''
const setValue = (value: string) => monacoEditor?.setValue(value)
const focus = () => monacoEditor?.focus()

defineExpose({
  formatJson,
  getValue,
  setValue,
  focus,
  getEditor: () => monacoEditor
})

// 生命周期
onMounted(async () => {
  await nextTick()
  await initVSCodeEditor()
})

onBeforeUnmount(() => {
  if (monacoEditor) {
    monacoEditor.dispose()
    monacoEditor = null
  }
})
</script>

<style scoped>
.art-json-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.vscode-editor-container {
  width: 100%;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
}

.error-message {
  padding: 8px;
  border-top: 1px solid var(--el-border-color);
}

/* 占位符样式 */
:deep(.monaco-placeholder::after) {
  content: attr(data-placeholder);
  color: var(--el-text-color-placeholder);
  font-style: italic;
  pointer-events: none;
}
</style>

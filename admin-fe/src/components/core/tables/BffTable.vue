<template>
  <ArtTable
    :data="processedTableData"
    :loading="loading"
    :total="bffData?.total || 0"
    :current-page="currentPage"
    :page-size="pageSize"
    v-bind="$attrs"
    @current-change="handlePageChange"
    @size-change="handleSizeChange"
  >
    <template #default>
      <!-- 动态生成表格列 -->
      <ElTableColumn
        v-for="(headerItem, index) in getTableHeaders"
        :key="headerItem.key"
        :prop="headerItem.key"
        :label="headerItem.label"
        :min-width="getColumnWidth(headerItem.key)"
      >
        <template #default="{ row, $index }">
          <!-- 使用 BFF 协议数据渲染 -->
          <div v-if="isButtonsColumn(headerItem.key, $index)" class="action-buttons">
            <ElButton
              v-for="button in getRowButtons(row, $index)"
              :key="button.id"
              :type="getButtonType(button)"
              :size="'small'"
              :disabled="button.disabled"
              :title="getButtonHover(button)"
              @click="handleButtonClick(button, row)"
            >
              {{ extractButtonText(button) }}
            </ElButton>
          </div>
          <!-- 普通数据列 -->
          <span v-else>{{ row[headerItem.key] }}</span>
        </template>
      </ElTableColumn>
    </template>
  </ArtTable>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElTableColumn, ElButton } from 'element-plus'
import ArtTable from './ArtTable.vue'
import type { BffTable } from '@/types/bff'
import { LanguageEnum } from '@/enums/appEnum'

// 操作按钮配置接口
interface ActionConfig {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'large' | 'default' | 'small'
  handler: (row: any) => void
}

// 组件 Props
interface BffTableProps {
  /** BFF Table 数据 */
  data?: BffTable<any>
  /** 加载状态 */
  loading?: boolean
  /** 当前页码 */
  currentPage?: number
  /** 每页大小 */
  pageSize?: number
  /** 操作按钮配置 */
  actions?: ActionConfig[]
  /** 自定义列宽配置 */
  columnWidths?: Record<string, number>
}

const props = withDefaults(defineProps<BffTableProps>(), {
  loading: false,
  currentPage: 1,
  pageSize: 20,
  actions: () => [],
  columnWidths: () => ({})
})

const { locale } = useI18n()
const router = useRouter()

/**
 * 根据当前语言设置获取多语言文本
 * 优先使用后端下发的i18n对象（最高优先级）
 * @param i18nText 多语言文本对象 { zh: '', en: '', ar: '' }
 * @returns 当前语言对应的文本，带兜底逻辑
 */
function getI18nText(i18nText: any): string {
  if (!i18nText || typeof i18nText !== 'object') {
    return String(i18nText || '')
  }

  const currentLang = locale.value

  // 优先使用当前语言设置
  if (i18nText[currentLang]) {
    return i18nText[currentLang]
  }

  // 兜底逻辑：按优先级选择可用语言
  const fallbackOrder = [LanguageEnum.ZH, LanguageEnum.EN, 'ar']

  for (const lang of fallbackOrder) {
    if (i18nText[lang]) {
      return i18nText[lang]
    }
  }

  // 最后兜底：返回第一个非空值或JSON字符串
  const firstValue = Object.values(i18nText).find(v => v && String(v).trim())
  return firstValue ? String(firstValue) : JSON.stringify(i18nText)
}

// 事件定义
const emit = defineEmits<{
  'current-change': [page: number]
  'size-change': [size: number]
  'action-click': [action: string, row: any]
}>()

// BFF 数据引用
const bffData = computed(() => {
  console.log('[BffTable DEBUG] props.data:', props.data)
  return props.data
})

/**
 * 提取 BFF Table 列数据的值
 * 处理多语言文本、时间、按钮组等不同类型
 */
const extractColumnValue = (col: any): string => {
  if (!col || !col.content) return ''

  switch (col.type) {
    case 'text':
      if (col.content.text && typeof col.content.text === 'object') {
        // 多语言文本：根据当前语言设置选择，带兜底逻辑
        return getI18nText(col.content.text)
      }
      return col.content.text || col.content.value || col.content.content || col.content

    case 'time':
      return col.content.time || col.content.value || col.content.content || col.content

    case 'icon_text':
      if (col.content.text && typeof col.content.text === 'object') {
        return getI18nText(col.content.text)
      }
      return col.content.text || ''

    case 'money':
      const amount = col.content.amount || 0
      const currency = col.content.currency || ''
      const precision = col.content.precision || 2
      return `${amount.toFixed(precision)} ${currency}`

    case 'link':
      if (col.content.linkText && typeof col.content.linkText === 'object') {
        return getI18nText(col.content.linkText)
      }
      return col.content.linkText || col.content.link || ''

    case 'buttons':
      // 按钮组类型，返回操作标识
      return '操作'

    case 'checkbox':
      return col.content.checked ? '是' : '否'

    case 'single_choice_option':
    case 'multi_choice_option':
      // 选择类型，显示选中的值
      if (col.content.value && col.content.value.text) {
        const text = col.content.value.text
        return getI18nText(text)
      }
      return ''

    default:
      return col.content || col
  }
}

/**
 * 获取表头信息，支持多语言
 */
const getTableHeaders = computed(() => {
  // 明确依赖语言设置，确保语言切换时重新计算
  const currentLocale = locale.value
  if (!bffData.value) return []

  // 如果有新的header字段，使用header字段
  if (bffData.value.header && bffData.value.header.length > 0) {
    return bffData.value.header.map((headerItem, index) => {
      const key = bffData.value!.headerKeys[index] || `col_${index}`
      let label = key

      // 处理多语言表头
      if (headerItem.content && headerItem.content.text) {
        label = getI18nText(headerItem.content.text)
      } else if (headerItem.content && typeof headerItem.content === 'string') {
        label = headerItem.content
      }

      return { key, label }
    })
  }

  // 兜底：使用headerKeys
  return (bffData.value.headerKeys || []).map(key => ({ key, label: key }))
})

/**
 * 处理后的表格数据
 * 将 BFF Table 的 rows 转换为标准的表格数据格式
 */
const processedTableData = computed(() => {
  // 明确依赖语言设置，确保语言切换时重新计算
  const currentLocale = locale.value
  console.log('[BffTable DEBUG] bffData.value:', bffData.value, 'locale:', currentLocale)
  if (!bffData.value?.rows) {
    console.log('[BffTable DEBUG] No rows found, returning empty array')
    return []
  }

  console.log('[BffTable DEBUG] Processing rows:', bffData.value.rows.length)
  return bffData.value.rows.map((row) => {
    const rowData: any = {
      key: row.key,
      _raw: row.raw,  // 保存原始数据
      _columns: row.columns  // 保存原始列数据
    }

    // 遍历每一列，提取数据
    row.columns.forEach((col, index) => {
      const headerKey = bffData.value!.headerKeys[index]
      const value = extractColumnValue(col)
      rowData[headerKey] = value

      // 保存按钮数据，用于操作按钮处理
      if (col.type === 'buttons') {
        rowData._buttonData = col.content
      }
    })

    return rowData
  })
})

/**
 * 获取列宽度
 */
const getColumnWidth = (headerKey: string): number => {
  // 优先使用自定义配置
  if (props.columnWidths[headerKey]) {
    return props.columnWidths[headerKey]
  }
  
  // 默认列宽配置
  const defaultWidths: Record<string, number> = {
    'ID': 80,
    '操作': 120,
    '状态': 100,
    '创建时间': 180,
    '更新时间': 180
  }
  
  return defaultWidths[headerKey] || 120
}

/**
 * 获取行操作按钮
 * 根据 BFF 协议解析按钮配置
 */
const getRowActions = (row: any): ActionConfig[] => {
  // 如果有自定义操作配置，使用自定义配置
  if (props.actions.length > 0) {
    return props.actions
  }

  // 如果行数据中有按钮数据，解析按钮配置
  if (row._buttonData?.buttons && Array.isArray(row._buttonData.buttons)) {
    return row._buttonData.buttons.map((btn: any) => ({
      label: extractButtonText(btn),
      type: getButtonType(btn.id),
      size: 'small',
      disabled: btn.disabled || false,
      handler: (rowData: any) => {
        // 如果有 jumpURL，优先使用跳转
        if (btn.jumpURL) {
          handleButtonJump(btn, rowData)
        } else {
          // 否则触发 action-click 事件
          emit('action-click', btn.id, rowData)
        }
      }
    }))
  }

  // 默认操作按钮
  return [
    {
      label: '编辑',
      type: 'primary',
      size: 'small',
      handler: (rowData: any) => {
        emit('action-click', 'edit', rowData)
      }
    }
  ]
}

/**
 * 提取按钮文本
 */
const extractButtonText = (btn: any): string => {
  if (!btn.content) return '操作'

  // 按钮内容也是 ElementItem 结构
  const content = btn.content
  if (content.type === 'text' && content.content?.text) {
    const text = content.content.text
    if (typeof text === 'object') {
      return getI18nText(text) || '操作'
    }
    return text || '操作'
  }

  if (content.type === 'icon_text' && content.content?.text) {
    const text = content.content.text
    if (typeof text === 'object') {
      return getI18nText(text) || '操作'
    }
    return text || '操作'
  }

  return '操作'
}

/**
 * 判断是否为按钮列
 */
function isButtonsColumn(headerKey: string, rowIndex: number): boolean {
  if (!props.data?.rows?.[rowIndex]) return false

  const row = props.data.rows[rowIndex]
  const headerIndex = props.data.headerKeys.indexOf(headerKey)

  if (headerIndex === -1 || !row.columns?.[headerIndex]) return false

  const isButtons = row.columns[headerIndex].type === 'buttons' || row.columns[headerIndex].type === 'button_group'
  console.log('[isButtonsColumn]', headerKey, 'rowIndex:', rowIndex, 'headerIndex:', headerIndex, 'type:', row.columns[headerIndex].type, 'isButtons:', isButtons)
  return isButtons
}

/**
 * 获取行的按钮列表
 */
function getRowButtons(row: any, rowIndex: number): any[] {
  if (!props.data?.rows?.[rowIndex]) return []

  const bffRow = props.data.rows[rowIndex]
  const buttonsColumn = bffRow.columns?.find(col => col.type === 'buttons' || col.type === 'button_group')

  return buttonsColumn?.content?.buttons || []
}

/**
 * 处理按钮点击
 */
function handleButtonClick(button: any, row: any) {
  const actionId = button.id || button.actionId

  // 优先处理 jumpURL
  if (button.jumpURL) {
    if (button.openNewTab) {
      window.open(button.jumpURL, '_blank')
    } else {
      router.push(button.jumpURL)
    }
    return
  }

  // 发送 action-click 事件
  emit('action-click', actionId, row)
}



/**
 * 获取按钮的 hover 文本
 */
function getButtonHover(btn: any): string {
  if (btn.hover) {
    if (typeof btn.hover === 'object') {
      return getI18nText(btn.hover)
    }
    return String(btn.hover)
  }
  return ''
}

/**
 * 根据按钮获取按钮类型
 */
function getButtonType(btn: any): string {
  const actionId = btn.id || btn.actionId
  switch (actionId) {
    case 'user:freeze':
    case 'trade:cancel':
      return 'danger'
    case 'user:unFreeze':
      return 'success'
    case 'user:detail':
      return 'info'
    case 'user:invite':
    case 'user:reSendInvitation':
      return 'warning'
    default:
      return 'primary'
  }
}

/**
 * 处理按钮跳转
 */
const handleButtonJump = (btn: any, rowData: any) => {
  const url = btn.jumpURL
  if (!url) return

  // 这里可以添加路由跳转逻辑
  // 由于我们在组件内部，需要通过事件通知父组件处理跳转
  emit('action-click', `jump:${url}`, rowData)
}

/**
 * 分页变化处理
 */
const handlePageChange = (page: number) => {
  emit('current-change', page)
}

/**
 * 页面大小变化处理
 */
const handleSizeChange = (size: number) => {
  emit('size-change', size)
}
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}
</style>

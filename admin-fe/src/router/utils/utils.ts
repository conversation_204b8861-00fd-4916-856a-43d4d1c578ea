import { useTheme } from '@/composables/useTheme'
import { useSettingStore } from '@/store/modules/setting'
import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import AppConfig from '@/config'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { $t } from '@/locales'
import { useI18n } from 'vue-i18n'

/** 扩展的路由配置类型 */
export type AppRouteRecordRaw = RouteRecordRaw & {
  hidden?: boolean
}

/** 顶部进度条配置 */
export const configureNProgress = () => {
  NProgress.configure({
    easing: 'ease',
    speed: 600,
    showSpinner: false,
    trickleSpeed: 200,
    parent: 'body'
  })
}

/**
 * 设置页面标题，根据路由元信息和系统信息拼接标题
 * @param to 当前路由对象
 */
export const setPageTitle = (to: RouteLocationNormalized): void => {
  const { title } = to.meta
  if (title) {
    setTimeout(() => {
      document.title = `${formatMenuTitle(title)} - ${AppConfig.systemInfo.name}`
    }, 150)
  }
}

/**
 * 根据路由元信息设置系统主题
 * @param to 当前路由对象
 */
export const setSystemTheme = (to: RouteLocationNormalized): void => {
  if (to.meta.setTheme) {
    useTheme().switchThemeStyles(useSettingStore().systemThemeType)
  }
}

/**
 * 格式化菜单标题
 * 优先级：后端专门接口下发的i18n对象 > 后端全局starlingMap > 前端多语言文案配置
 * @param title 菜单标题，可以是 i18n 的 key、字符串或 i18n.I18N 对象
 * @param currentLang 当前语言设置 (zh/en/ar)
 * @returns 格式化后的菜单标题
 */
export const formatMenuTitle = (title: string | any, currentLang?: string): string => {
  if (!title) {
    return ''
  }

  // 优先级1: 后端专门接口下发的 i18n.I18N 对象格式 { zh: '', en: '', ar: '' }
  // 这是最高优先级，直接处理
  if (typeof title === 'object' && title !== null) {
    // 如果传入了当前语言，优先使用
    if (currentLang) {
      if (currentLang === 'zh' && title.zh) {
        return title.zh
      } else if (currentLang === 'en' && title.en) {
        return title.en
      } else if (currentLang === 'ar' && title.ar) {
        return title.ar
      }
    }

    // 兜底逻辑：按优先级选择可用语言
    return title.zh || title.en || title.ar || String(title)
  }

  // 优先级2: 如果是字符串类型，检查是否是前端i18n key
  if (typeof title === 'string') {
    // 如果是前端i18n key（以 'menus.' 开头），使用前端多语言配置
    if (title.startsWith('menus.')) {
      return $t(title)
    }
    // 否则直接返回字符串（可能是后端全局接口下发的纯文本）
    return title
  }

  return String(title)
}

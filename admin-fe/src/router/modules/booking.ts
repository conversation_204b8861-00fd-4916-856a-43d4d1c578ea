import type { RouteRecordRaw } from 'vue-router'

const bookingRoutes: RouteRecordRaw[] = [
  {
    path: '/booking',
    name: 'Booking',
    component: () => import('@/layouts/default.vue'),
    meta: {
      title: '预订管理',
      icon: 'Calendar',
      requiresAuth: true
    },
    children: [
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/views/booking/order-management/index.vue'),
        meta: {
          title: '订单管理',
          requiresAuth: true
        }
      },
      {
        path: 'orders/create',
        name: 'CreateOrder',
        component: () => import('@/views/booking/order-management/create-order.vue'),
        meta: {
          title: '创建订单',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: 'orders/:orderId',
        name: 'OrderDetail',
        component: () => import('@/views/booking/order-management/order-detail.vue'),
        meta: {
          title: '订单详情',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: 'form-validation-demo',
        name: 'FormValidationDemo',
        component: () => import('@/views/booking/form-validation-demo.vue'),
        meta: {
          title: '表单验证演示',
          requiresAuth: true,
          hideInMenu: true
        }
      }
    ]
  }
]

export default bookingRoutes
/**
 * 路由别名，方便快速找到页面，同时可以用作路由跳转
 * 这些是路由路径，用于路由配置和跳转
 */
export enum RoutesAlias {
  Home = '/index/index', // 布局容器
  Login = '/auth/login', // 登录
  Register = '/auth/register', // 注册
  ForgetPassword = '/auth/forget-password', // 忘记密码
  Exception403 = '/exception/403', // 403
  Exception404 = '/exception/404', // 404
  Exception500 = '/exception/500', // 500
  Success = '/result/success', // 成功
  Fail = '/result/fail', // 失败
  Dashboard = '/dashboard/console', // 工作台
  Analysis = '/dashboard/analysis', // 分析页
  Ecommerce = '/dashboard/ecommerce', // 电子商务
  IconList = '/widgets/icon-list', // 图标列表
  IconSelector = '/widgets/icon-selector', // 图标选择器
  ImageCrop = '/widgets/image-crop', // 图片裁剪
  Excel = '/widgets/excel', // Excel
  Video = '/widgets/video', // 视频
  CountTo = '/widgets/count-to', // 计数
  WangEditor = '/widgets/wang-editor', // 富文本编辑器
  Watermark = '/widgets/watermark', // 水印
  ContextMenu = '/widgets/context-menu', // 上下文菜单
  Qrcode = '/widgets/qrcode', // 二维码
  Drag = '/widgets/drag', // 拖拽
  TextScroll = '/widgets/text-scroll', // 文字滚动
  Fireworks = '/widgets/fireworks', // 礼花效果
  Chat = '/template/chat', // 聊天
  Cards = '/template/cards', // 卡片
  Banners = '/template/banners', // 横幅
  Charts = '/template/charts', // 图表
  Map = '/template/map', // 地图
  Calendar = '/template/calendar', // 日历
  Pricing = '/template/pricing', // 定价
  ArticleList = '/article/list', // 文章列表
  ArticleDetail = '/article/detail', // 文章详情
  Comment = '/article/comment', // 评论
  ArticlePublish = '/article/publish', // 文章发布
  User = '/system/user', // 账户
  Role = '/system/role', // 角色
  UserCenter = '/system/user-center', // 用户中心
  Menu = '/system/menu', // 菜单
  Starling = '/system/starling', // 多语言配置
  Logs = '/logs', // 日志查看器
  NestedMenu1 = '/system/nested/menu1', // 嵌套菜单1
  NestedMenu21 = '/system/nested/menu2', // 嵌套菜单2-1
  NestedMenu31 = '/system/nested/menu3', // 嵌套菜单3-1
  NestedMenu321 = '/system/nested/menu3/menu3-2', // 嵌套菜单3-2-1
  Server = '/safeguard/server', // 服务器
  ChangeLog = '/change/log', // 更新日志
  ExamplesTabs = '/examples/tabs', // 标签页

  // Entity Management
  EntityList = '/entity/entity-list', // 实体列表
  EntityEdit = '/entity/entity-edit', // 实体编辑

  // User Management
  TenantUsers = '/users/tenant-users', // 租户用户
  CustomerUsers = '/users/customer-users', // 客户用户
  PlatformUsers = '/users/platform-users', // 平台用户
  UserEdit = '/users/user-edit' // 用户编辑
}

/**
 * 组件路径映射，用于动态导入组件
 */
export const ComponentPaths = {
  Home: () => import('@/views/index/index.vue'),
  Login: () => import('@/views/auth/login/index.vue'),
  Register: () => import('@/views/auth/register/index.vue'),
  ForgetPassword: () => import('@/views/auth/forget-password/index.vue'),
  Exception403: () => import('@/views/exception/403/index.vue'),
  Exception404: () => import('@/views/exception/404/index.vue'),
  Exception500: () => import('@/views/exception/500/index.vue'),
  Success: () => import('@/views/result/success/index.vue'),
  Fail: () => import('@/views/result/fail/index.vue'),
  Dashboard: () => import('@/views/dashboard/console/index.vue'),
  Analysis: () => import('@/views/dashboard/analysis/index.vue'),
  Ecommerce: () => import('@/views/dashboard/ecommerce/index.vue'),
  IconList: () => import('@/views/widgets/icon-list/index.vue'),
  IconSelector: () => import('@/views/widgets/icon-selector/index.vue'),
  ImageCrop: () => import('@/views/widgets/image-crop/index.vue'),
  Excel: () => import('@/views/widgets/excel/index.vue'),
  Video: () => import('@/views/widgets/video/index.vue'),
  CountTo: () => import('@/views/widgets/count-to/index.vue'),
  WangEditor: () => import('@/views/widgets/wang-editor/index.vue'),
  Watermark: () => import('@/views/widgets/watermark/index.vue'),
  ContextMenu: () => import('@/views/widgets/context-menu/index.vue'),
  Qrcode: () => import('@/views/widgets/qrcode/index.vue'),
  Drag: () => import('@/views/widgets/drag/index.vue'),
  TextScroll: () => import('@/views/widgets/text-scroll/index.vue'),
  Fireworks: () => import('@/views/widgets/fireworks/index.vue'),
  Chat: () => import('@/views/template/chat/index.vue'),
  Cards: () => import('@/views/template/cards/index.vue'),
  Banners: () => import('@/views/template/banners/index.vue'),
  Charts: () => import('@/views/template/charts/index.vue'),
  Map: () => import('@/views/template/map/index.vue'),
  Calendar: () => import('@/views/template/calendar/index.vue'),
  Pricing: () => import('@/views/template/pricing/index.vue'),
  ArticleList: () => import('@/views/article/list/index.vue'),
  ArticleDetail: () => import('@/views/article/detail/index.vue'),
  Comment: () => import('@/views/article/comment/index.vue'),
  ArticlePublish: () => import('@/views/article/publish/index.vue'),
  User: () => import('@/views/system/user/index.vue'),
  Role: () => import('@/views/system/role/index.vue'),
  UserCenter: () => import('@/views/system/user-center/index.vue'),
  Menu: () => import('@/views/system/menu/index.vue'),
  Starling: () => import('@/views/system/starling/index.vue'),
  Logs: () => import('@/views/system/logs/index.vue'),
  NestedMenu1: () => import('@/views/system/nested/menu1/index.vue'),
  NestedMenu21: () => import('@/views/system/nested/menu2/index.vue'),
  NestedMenu31: () => import('@/views/system/nested/menu3/index.vue'),
  NestedMenu321: () => import('@/views/system/nested/menu3/menu3-2/index.vue'),
  Server: () => import('@/views/safeguard/server/index.vue'),
  ChangeLog: () => import('@/views/change/log/index.vue'),
  ExamplesTabs: () => import('@/views/examples/tabs/index.vue'),

  // Entity Management
  EntityList: () => import('@/views/entity/entity-list/index.vue'),
  EntityEdit: () => import('@/views/entity/entity-edit/index.vue'),

  // User Management
  TenantUsers: () => import('@/views/users/tenant-users/index.vue'),
  CustomerUsers: () => import('@/views/users/customer-users/index.vue'),
  PlatformUsers: () => import('@/views/users/platform-users/index.vue'),
  UserEdit: () => import('@/views/users/user-edit/index.vue')
}

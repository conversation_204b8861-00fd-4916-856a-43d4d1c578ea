<template>
  <div class="i18n-priority-example">
    <el-card class="example-card" shadow="never">
      <template #header>
        <span>多语言优先级处理示例</span>
      </template>

      <!-- 场景1: 后端专门接口下发的i18n对象（最高优先级） -->
      <div class="scenario">
        <h3>场景1: 后端专门接口下发的i18n对象（最高优先级）</h3>
        <p class="description">
          当后端API直接返回i18n对象时，这是最高优先级的多语言处理方式。
          常见于菜单标题、表格列标题、动态内容等。
        </p>
        
        <div class="example-code">
          <h4>示例代码：</h4>
          <pre><code>// 后端返回的菜单数据
const menuItem = {
  title: { zh: '用户管理', en: 'User Management', ar: 'إدارة المستخدمين' },
  path: '/user-management'
}

// 使用方式
const title = getBackendI18nText(menuItem.title)</code></pre>
        </div>

        <div class="example-result">
          <h4>实际效果：</h4>
          <el-tag type="success">{{ getBackendI18nText(mockMenuTitle) }}</el-tag>
          <span class="priority-label">（优先级：最高）</span>
        </div>
      </div>

      <!-- 场景2: 全局StarlingMap -->
      <div class="scenario">
        <h3>场景2: 全局StarlingMap（第二优先级）</h3>
        <p class="description">
          当使用字符串key时，系统会先查找全局StarlingMap中的配置。
          适用于全局通用的多语言文本。
        </p>
        
        <div class="example-code">
          <h4>示例代码：</h4>
          <pre><code>// 使用starling key
const text = getStarlingText('common.button.add')</code></pre>
        </div>

        <div class="example-result">
          <h4>实际效果：</h4>
          <el-tag type="primary">{{ getStarlingText('common.button.add') }}</el-tag>
          <span class="priority-label">（优先级：第二）</span>
        </div>
      </div>

      <!-- 场景3: 前端语言包兜底 -->
      <div class="scenario">
        <h3>场景3: 前端语言包兜底（第三优先级）</h3>
        <p class="description">
          当StarlingMap中没有对应key时，会使用前端语言包作为兜底。
        </p>
        
        <div class="example-code">
          <h4>示例代码：</h4>
          <pre><code>// 使用前端语言包的key
const text = getStarlingText('topBar.search.title')</code></pre>
        </div>

        <div class="example-result">
          <h4>实际效果：</h4>
          <el-tag type="warning">{{ getStarlingText('topBar.search.title') }}</el-tag>
          <span class="priority-label">（优先级：第三）</span>
        </div>
      </div>

      <!-- 场景4: 兜底文本 -->
      <div class="scenario">
        <h3>场景4: 兜底文本（最后保障）</h3>
        <p class="description">
          当所有多语言源都没有对应文本时，使用提供的兜底文本。
        </p>
        
        <div class="example-code">
          <h4>示例代码：</h4>
          <pre><code>// 使用不存在的key，提供兜底文本
const text = getStarlingText('non.existent.key', '兜底文本')</code></pre>
        </div>

        <div class="example-result">
          <h4>实际效果：</h4>
          <el-tag type="info">{{ getStarlingText('non.existent.key', '兜底文本') }}</el-tag>
          <span class="priority-label">（优先级：最后）</span>
        </div>
      </div>

      <!-- 智能处理示例 -->
      <div class="scenario">
        <h3>推荐：智能处理函数</h3>
        <p class="description">
          使用 <code>useSmartI18nText()</code> 可以自动判断输入类型并应用正确的优先级。
        </p>
        
        <div class="example-code">
          <h4>示例代码：</h4>
          <pre><code>// 自动处理i18n对象
const text1 = getSmartI18nText({ zh: '智能处理', en: 'Smart Processing' })

// 自动处理字符串key
const text2 = getSmartI18nText('common.button.save')</code></pre>
        </div>

        <div class="example-result">
          <h4>实际效果：</h4>
          <div>
            <el-tag type="success">{{ getSmartI18nText({ zh: '智能处理', en: 'Smart Processing' }) }}</el-tag>
            <span class="priority-label">（i18n对象 - 最高优先级）</span>
          </div>
          <div style="margin-top: 10px;">
            <el-tag type="primary">{{ getSmartI18nText('common.button.save') }}</el-tag>
            <span class="priority-label">（字符串key - 按优先级查找）</span>
          </div>
        </div>
      </div>

      <!-- 实际应用场景 -->
      <div class="scenario">
        <h3>实际应用场景对比</h3>
        <el-table :data="comparisonData" border style="width: 100%">
          <el-table-column prop="scenario" label="应用场景" width="200" />
          <el-table-column prop="dataSource" label="数据来源" width="200" />
          <el-table-column prop="priority" label="优先级" width="100" />
          <el-table-column prop="example" label="示例" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useBackendI18nText, useStarlingText, useSmartI18nText } from '@/utils/i18n'

const getBackendI18nText = useBackendI18nText()
const getStarlingText = useStarlingText()
const getSmartI18nText = useSmartI18nText()

// 模拟后端返回的菜单标题
const mockMenuTitle = {
  zh: '用户管理',
  en: 'User Management',
  ar: 'إدارة المستخدمين'
}

// 对比数据
const comparisonData = ref([
  {
    scenario: '菜单标题',
    dataSource: '后端API直接返回i18n对象',
    priority: '最高',
    example: 'menuItem.title = { zh: "用户管理", en: "User Management" }'
  },
  {
    scenario: '表格列标题',
    dataSource: '后端BFF Table返回i18n对象',
    priority: '最高',
    example: 'column.title = { zh: "操作", en: "Actions" }'
  },
  {
    scenario: '通用按钮文本',
    dataSource: 'StarlingMap全局配置',
    priority: '第二',
    example: 'getStarlingText("common.button.add")'
  },
  {
    scenario: '页面标题',
    dataSource: '前端语言包',
    priority: '第三',
    example: 't("topBar.search.title")'
  },
  {
    scenario: '错误提示',
    dataSource: '兜底文本',
    priority: '最后',
    example: 'getStarlingText("error.unknown", "未知错误")'
  }
])
</script>

<style scoped>
.i18n-priority-example {
  padding: 20px;
}

.example-card {
  max-width: 1000px;
  margin: 0 auto;
}

.scenario {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.scenario h3 {
  margin-top: 0;
  color: #303133;
}

.description {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.6;
}

.example-code {
  margin-bottom: 15px;
}

.example-code h4 {
  margin-bottom: 10px;
  color: #409eff;
}

.example-code pre {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0;
}

.example-code code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #303133;
}

.example-result h4 {
  margin-bottom: 10px;
  color: #67c23a;
}

.priority-label {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>

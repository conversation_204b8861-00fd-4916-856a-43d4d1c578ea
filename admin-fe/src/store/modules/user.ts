import { defineStore } from 'pinia'
import { LanguageEnum } from '@/enums/appEnum'
import { router } from '@/router'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { AppRouteRecord } from '@/types/router'
import { setPageTitle } from '@/router/utils/utils'
import { resetRouterState } from '@/router/guards/beforeEach'
import { RoutesAlias } from '@/router/routesAlias'

// 用户
export const useUserStore = defineStore('userStore', {
  state: () => {
    // 兼容老数据
    let info = {} as Partial<Api.User.UserInfo>
    try {
      const raw = localStorage.getItem('pinia-userStore')
      if (raw) {
        const parsed = JSON.parse(raw)
        if (parsed.info && !parsed.info.email && parsed.info.key) {
          info = {
            ...parsed.info,
            email: parsed.info.key,
            userName: parsed.info.username || parsed.info.userName || '',
            avatar: parsed.info.profile?.avatar || '',
            phone: parsed.info.profile?.mobile || '',
          }
          console.log('[userStore] 自动兼容老userInfo结构:', info)
        } else if (parsed.info) {
          info = parsed.info
        }
      }
    } catch (e) {
      // ignore
    }
    return {
      language: LanguageEnum.ZH,
      isLogin: false,
      isLock: false,
      lockPassword: '',
      info,
      searchHistory: [] as AppRouteRecord[],
      accessToken: '',
      refreshToken: '',
      sessionId: ''
    }
  },
  getters: {
    getUserInfo: (state) => state.info,
    getUserRoles: (state) => {
      const connections = state.info?.userEntityConnections || [];
      const roles: string[] = [];
      connections.forEach((conn: any) => {
        if (Array.isArray(conn.roles)) {
          conn.roles.forEach((role: any) => {
            if (role && role.name) roles.push(role.name)
          })
        }
      })
      return roles
    },
    getSettingState: () => useSettingStore().$state,
    getWorktabState: () => useWorktabStore().$state
  },
  actions: {
    setUserInfo(newInfo: Api.User.UserInfo) {
      // 合并：后端字段为空时保留本地已有值
      const merged = { ...(this.info as any) }
      for (const k in newInfo as any) {
        if ((newInfo as any)[k] !== undefined && (newInfo as any)[k] !== null && (newInfo as any)[k] !== '') {
          merged[k] = (newInfo as any)[k]
        }
      }
      // profile字段也做深合并
      if ((newInfo as any).profile) {
        merged.profile = { ...(this.info as any)?.profile, ...(newInfo as any).profile }
      }
      this.info = merged
      console.log('[userStore.setUserInfo] merged:', merged)
    },
    setLoginStatus(status: boolean) {
      this.isLogin = status
    },
    setLanguage(lang: LanguageEnum) {
      setPageTitle(router.currentRoute.value)
      this.language = lang
    },
    setSearchHistory(list: AppRouteRecord[]) {
      this.searchHistory = list
    },
    setLockStatus(status: boolean) {
      this.isLock = status
    },
    setLockPassword(password: string) {
      this.lockPassword = password
    },
    setToken(newAccessToken: string, newRefreshToken?: string) {
      this.accessToken = newAccessToken
      if (newRefreshToken) {
        this.refreshToken = newRefreshToken
      }
    },
    setSessionId(sessionId: string) {
      this.sessionId = sessionId
      console.log('[userStore.setSessionId]', sessionId)
    },
    logOut() {
      this.info = {}
      this.isLogin = false
      this.isLock = false
      this.lockPassword = ''
      this.accessToken = ''
      this.refreshToken = ''
      this.sessionId = ''
      useWorktabStore().opened = []
      sessionStorage.removeItem('iframeRoutes')
      resetRouterState()
      router.push(RoutesAlias.Login)
    }
  },
  persist: import.meta.env.MODE !== 'e2e'
})

<template>
  <div class="i18n-test-page">
    <el-card class="test-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>多语言功能测试</span>
          <el-button type="primary" @click="refreshStarlingMap">刷新StarlingMap</el-button>
        </div>
      </template>

      <!-- 语言切换 -->
      <div class="language-switch">
        <span>当前语言: </span>
        <el-radio-group v-model="currentLanguage" @change="handleLanguageChange">
          <el-radio-button label="zh">中文</el-radio-button>
          <el-radio-button label="en">English</el-radio-button>
        </el-radio-group>
      </div>

      <!-- StarlingMap 统计信息 -->
      <div class="starling-stats">
        <h3>StarlingMap 统计信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="总条目数">{{ starlingStats.totalKeys }}</el-descriptions-item>
          <el-descriptions-item label="空间数">{{ starlingStats.spacesCount }}</el-descriptions-item>
          <el-descriptions-item label="应用数">{{ starlingStats.applicationsCount }}</el-descriptions-item>
          <el-descriptions-item label="空间列表">{{ starlingStats.spaces.join(', ') }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 多语言文本测试 -->
      <div class="text-tests">
        <h3>多语言文本测试</h3>
        <el-alert
          title="优先级说明"
          type="info"
          :closable="false"
          style="margin-bottom: 15px;"
        >
          <template #default>
            <p><strong>正确的优先级：</strong></p>
            <ol>
              <li><strong>后端专门接口下发的i18n对象</strong> - 最高优先级（如菜单标题、表格列标题等）</li>
              <li><strong>后端全局starlingMap</strong> - 全局多语言配置</li>
              <li><strong>前端语言包</strong> - 兜底机制</li>
              <li><strong>兜底文本</strong> - 最后保障</li>
            </ol>
          </template>
        </el-alert>
        <el-table :data="testTexts" border style="width: 100%">
          <el-table-column prop="key" label="键名" width="300" />
          <el-table-column prop="starlingText" label="StarlingMap文本" width="200" />
          <el-table-column prop="frontendText" label="前端语言包文本" width="200" />
          <el-table-column prop="unifiedText" label="统一获取文本" width="200" />
        </el-table>
      </div>

      <!-- 后端i18n对象测试 -->
      <div class="backend-i18n-tests">
        <h3>后端i18n对象测试（最高优先级）</h3>
        <el-table :data="backendI18nTests" border style="width: 100%">
          <el-table-column prop="description" label="测试场景" width="300" />
          <el-table-column prop="input" label="输入对象" width="300" />
          <el-table-column prop="result" label="处理结果" width="200" />
          <el-table-column prop="priority" label="优先级" width="150" />
        </el-table>
      </div>

      <!-- 订单页面多语言测试 -->
      <div class="order-tests">
        <h3>订单页面多语言测试</h3>
        <el-table :data="orderTexts" border style="width: 100%">
          <el-table-column prop="key" label="键名" width="300" />
          <el-table-column prop="text" label="显示文本" width="300" />
          <el-table-column prop="source" label="来源" width="150" />
        </el-table>
      </div>

      <!-- 原始StarlingMap数据 -->
      <div class="raw-data">
        <h3>原始StarlingMap数据 (前10条)</h3>
        <el-table :data="rawStarlingData" border style="width: 100%" max-height="300">
          <el-table-column prop="key" label="键名" width="300" />
          <el-table-column prop="zh" label="中文" width="200" />
          <el-table-column prop="en" label="英文" width="200" />
          <el-table-column prop="ar" label="阿拉伯文" width="200" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  getGlobalStarlingMap,
  setGlobalStarlingMap,
  useStarlingText,
  useBackendI18nText,
  useSmartI18nText,
  getStarlingMapStats,
  hasStarlingKey,
  getStarlingAllLanguages
} from '@/utils/i18n'
import { menuApiV2 } from '@/api/menuApiV2'
import { ElMessage } from 'element-plus'

const { t, locale } = useI18n()
const getStarlingText = useStarlingText()
const getBackendI18nText = useBackendI18nText()
const getSmartI18nText = useSmartI18nText()

// 当前语言
const currentLanguage = ref(locale.value)

// StarlingMap统计信息
const starlingStats = ref(getStarlingMapStats())

// 测试用的多语言键名
const testKeys = [
  'common.button.search',
  'common.button.add',
  'common.button.edit',
  'common.button.delete',
  'user.list.title',
  'entity.list.title'
]

// 订单相关的多语言键名
const orderKeys = [
  'booking.order.title',
  'booking.order.orderId',
  'booking.order.hotelName',
  'booking.order.guestName',
  'booking.order.status',
  'booking.order.loadFailed',
  'booking.order.close',
  'booking.order.print'
]

// 测试文本数据
const testTexts = computed(() => {
  const starlingMap = getGlobalStarlingMap()
  
  return testKeys.map(key => {
    const starlingData = starlingMap[key]
    const starlingText = starlingData ? starlingData[currentLanguage.value] || starlingData.zh || starlingData.en : ''
    const frontendText = t(key)
    const unifiedText = getStarlingText(key, frontendText)
    
    return {
      key,
      starlingText,
      frontendText,
      unifiedText
    }
  })
})

// 订单文本数据
const orderTexts = computed(() => {
  return orderKeys.map(key => {
    const starlingText = getStarlingText(key)
    const frontendText = t(key)
    const hasStarling = hasStarlingKey(key)
    
    return {
      key,
      text: starlingText || frontendText || key,
      source: hasStarling ? 'StarlingMap' : 'Frontend'
    }
  })
})

// 后端i18n对象测试数据
const backendI18nTests = computed(() => {
  const testCases = [
    {
      description: '完整的i18n对象',
      input: { zh: '用户管理', en: 'User Management', ar: 'إدارة المستخدمين' },
      priority: '最高'
    },
    {
      description: '只有中英文的i18n对象',
      input: { zh: '订单列表', en: 'Order List' },
      priority: '最高'
    },
    {
      description: '只有中文的i18n对象',
      input: { zh: '系统设置' },
      priority: '最高'
    },
    {
      description: '空的i18n对象',
      input: {},
      priority: '最高'
    }
  ]

  return testCases.map(testCase => ({
    ...testCase,
    input: JSON.stringify(testCase.input),
    result: getBackendI18nText(testCase.input, '兜底文本')
  }))
})

// 原始StarlingMap数据
const rawStarlingData = computed(() => {
  const starlingMap = getGlobalStarlingMap()
  const entries = Object.entries(starlingMap).slice(0, 10)

  return entries.map(([key, value]) => ({
    key,
    zh: value.zh || '',
    en: value.en || '',
    ar: value.ar || ''
  }))
})

// 处理语言切换
const handleLanguageChange = (lang: string) => {
  locale.value = lang
  currentLanguage.value = lang
  ElMessage.success(`语言已切换到: ${lang === 'zh' ? '中文' : 'English'}`)
}

// 刷新StarlingMap
const refreshStarlingMap = async () => {
  try {
    ElMessage.info('正在刷新StarlingMap...')
    const starlingMap = await menuApiV2.getStarlingMap()
    setGlobalStarlingMap(starlingMap)
    starlingStats.value = getStarlingMapStats()
    ElMessage.success(`StarlingMap刷新成功，共${Object.keys(starlingMap).length}条数据`)
  } catch (error) {
    console.error('刷新StarlingMap失败:', error)
    ElMessage.error('刷新StarlingMap失败')
  }
}

onMounted(() => {
  starlingStats.value = getStarlingMapStats()
})
</script>

<style scoped>
.i18n-test-page {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.language-switch {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.starling-stats,
.text-tests,
.order-tests,
.raw-data {
  margin-bottom: 30px;
}

.starling-stats h3,
.text-tests h3,
.order-tests h3,
.raw-data h3 {
  margin-bottom: 15px;
  color: #303133;
}
</style>

<template>
  <div class="json-editor-test">
    <h1>JSON编辑器测试页面</h1>
    
    <div class="test-section">
      <h2>Profile配置</h2>
      <ArtJsonEditor
        v-model="profileJson"
        height="200px"
        placeholder="请输入Profile配置(JSON格式)"
        @validate="onProfileValidate"
        @change="onProfileChange"
      />
      <div class="validation-status">
        <span :class="{ valid: profileValid, invalid: !profileValid }">
          验证状态: {{ profileValid ? '✅ 有效' : '❌ 无效' }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>分销配置</h2>
      <ArtJsonEditor
        v-model="distributionJson"
        height="200px"
        placeholder="请输入分销配置(JSON格式)"
        @validate="onDistributionValidate"
      />
      <div class="validation-status">
        <span :class="{ valid: distributionValid, invalid: !distributionValid }">
          验证状态: {{ distributionValid ? '✅ 有效' : '❌ 无效' }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>财务配置</h2>
      <ArtJsonEditor
        v-model="financeJson"
        height="200px"
        placeholder="请输入财务配置(JSON格式)"
        @validate="onFinanceValidate"
      />
      <div class="validation-status">
        <span :class="{ valid: financeValid, invalid: !financeValid }">
          验证状态: {{ financeValid ? '✅ 有效' : '❌ 无效' }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>供应商配置</h2>
      <ArtJsonEditor
        v-model="supplierJson"
        height="200px"
        placeholder="请输入供应商配置(JSON格式)"
        @validate="onSupplierValidate"
      />
      <div class="validation-status">
        <span :class="{ valid: supplierValid, invalid: !supplierValid }">
          验证状态: {{ supplierValid ? '✅ 有效' : '❌ 无效' }}
        </span>
      </div>
    </div>

    <div class="actions">
      <el-button @click="loadSampleData">加载示例数据</el-button>
      <el-button @click="clearAll">清空所有</el-button>
      <el-button type="primary" @click="showParsedData">显示解析结果</el-button>
    </div>

    <div v-if="showResult" class="result-section">
      <h2>解析结果</h2>
      <pre>{{ parsedResult }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import ArtJsonEditor from '@/components/core/forms/art-json-editor/index.vue'

// JSON数据
const profileJson = ref('')
const distributionJson = ref('')
const financeJson = ref('')
const supplierJson = ref('')

// 验证状态
const profileValid = ref(true)
const distributionValid = ref(true)
const financeValid = ref(true)
const supplierValid = ref(true)

// 结果显示
const showResult = ref(false)
const parsedResult = ref('')

// 验证方法
const onProfileValidate = (isValid: boolean) => {
  profileValid.value = isValid
}

const onDistributionValidate = (isValid: boolean) => {
  distributionValid.value = isValid
}

const onFinanceValidate = (isValid: boolean) => {
  financeValid.value = isValid
}

const onSupplierValidate = (isValid: boolean) => {
  supplierValid.value = isValid
}

const onProfileChange = (value: string) => {
  console.log('Profile配置变化:', value)
}

// 加载示例数据
const loadSampleData = () => {
  profileJson.value = JSON.stringify({
    name: "示例实体",
    contact: {
      email: "<EMAIL>",
      phone: "+86-123-4567-8900",
      address: "北京市朝阳区示例街道123号"
    },
    settings: {
      timezone: "Asia/Shanghai",
      language: "zh-CN",
      currency: "CNY"
    }
  }, null, 2)

  distributionJson.value = JSON.stringify({
    buyerOutAggregatedRuleId: 0,
    buyerInAggregatedRuleId: 1,
    sellerOutAggregatedRuleId: 0,
    sellerInAggregatedRuleId: 0,
    defaultCurrency: "CNY",
    commissionRate: 0.15,
    channels: ["online", "offline", "mobile"]
  }, null, 2)

  financeJson.value = JSON.stringify({
    accountingMethod: "accrual",
    taxRate: 0.06,
    paymentTerms: "net30",
    bankAccount: {
      accountNumber: "**********",
      bankName: "中国银行",
      swiftCode: "BKCHCNBJ"
    }
  }, null, 2)

  supplierJson.value = JSON.stringify({
    preferredSuppliers: ["supplier1", "supplier2"],
    contractTerms: {
      paymentDays: 30,
      cancellationPolicy: "flexible",
      commissionStructure: "tiered"
    },
    integrationSettings: {
      apiTimeout: 30000,
      retryAttempts: 3,
      cacheExpiry: 3600
    }
  }, null, 2)

  ElMessage.success('示例数据已加载')
}

// 清空所有
const clearAll = () => {
  profileJson.value = ''
  distributionJson.value = ''
  financeJson.value = ''
  supplierJson.value = ''
  showResult.value = false
  ElMessage.info('已清空所有数据')
}

// 显示解析结果
const showParsedData = () => {
  try {
    const result = {
      profile: profileJson.value ? JSON.parse(profileJson.value) : null,
      distribution: distributionJson.value ? JSON.parse(distributionJson.value) : null,
      finance: financeJson.value ? JSON.parse(financeJson.value) : null,
      supplier: supplierJson.value ? JSON.parse(supplierJson.value) : null
    }
    
    parsedResult.value = JSON.stringify(result, null, 2)
    showResult.value = true
    ElMessage.success('解析成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法解析')
  }
}
</script>

<style scoped>
.json-editor-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.test-section h2 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 18px;
}

.validation-status {
  margin-top: 10px;
}

.validation-status .valid {
  color: #67c23a;
  font-weight: 500;
}

.validation-status .invalid {
  color: #f56c6c;
  font-weight: 500;
}

.actions {
  text-align: center;
  margin: 30px 0;
}

.actions .el-button {
  margin: 0 10px;
}

.result-section {
  margin-top: 30px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.result-section h2 {
  margin: 0 0 15px 0;
  color: #303133;
}

.result-section pre {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>

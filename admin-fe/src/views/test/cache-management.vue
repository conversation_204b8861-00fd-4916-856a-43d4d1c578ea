<template>
  <div class="cache-management-page">
    <el-card class="management-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>InternalHomepage接口缓存管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshStats">刷新状态</el-button>
            <el-button type="warning" @click="forceRefreshCache">强制刷新缓存</el-button>
            <el-button type="danger" @click="clearCache">清除缓存</el-button>
          </div>
        </div>
      </template>

      <!-- 缓存状态概览 -->
      <div class="cache-overview">
        <h3>缓存状态概览</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="缓存状态" :value="cacheStats.internalHomepageCache.exists ? '有效' : '无效'" />
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="剩余时间" 
              :value="cacheStats.internalHomepageCache.remainingSeconds || 0" 
              suffix="秒"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic title="总缓存数" :value="cacheStats.totalCaches" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="缓存时长" value="10" suffix="分钟" />
          </el-col>
        </el-row>
      </div>

      <!-- 缓存详细信息 -->
      <div class="cache-details">
        <h3>缓存详细信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="缓存是否存在">
            <el-tag :type="cacheStats.internalHomepageCache.exists ? 'success' : 'danger'">
              {{ cacheStats.internalHomepageCache.exists ? '存在' : '不存在' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ cacheStats.internalHomepageCache.timestamp || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ cacheStats.internalHomepageCache.expireTime || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="剩余时间">
            {{ formatRemainingTime(cacheStats.internalHomepageCache.remainingSeconds) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 缓存操作日志 -->
      <div class="cache-logs">
        <h3>操作日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action" :class="log.type">{{ log.action }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <!-- 测试区域 -->
      <div class="test-area">
        <h3>缓存测试</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="primary" @click="testGetMenuList" :loading="testLoading.menu">
              测试获取菜单列表
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="testGetStarlingMap" :loading="testLoading.starling">
              测试获取StarlingMap
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="success" @click="testMultipleRequests" :loading="testLoading.multiple">
              测试多次请求（验证缓存）
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResults.length > 0" class="test-results">
        <h3>测试结果</h3>
        <el-table :data="testResults" border style="width: 100%">
          <el-table-column prop="time" label="时间" width="180" />
          <el-table-column prop="action" label="操作" width="200" />
          <el-table-column prop="duration" label="耗时(ms)" width="120" />
          <el-table-column prop="fromCache" label="来源" width="120">
            <template #default="{ row }">
              <el-tag :type="row.fromCache ? 'success' : 'primary'">
                {{ row.fromCache ? '缓存' : '网络' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="result" label="结果" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { menuApiV2 } from '@/api/menuApiV2'
import { internalHomepageCache } from '@/utils/cache/internalHomepageCache'

// 缓存统计信息
const cacheStats = ref({
  totalCaches: 0,
  internalHomepageCache: {
    exists: false,
    timestamp: '',
    expireTime: '',
    remainingSeconds: 0
  }
})

// 操作日志
const operationLogs = ref<Array<{
  time: string
  action: string
  message: string
  type: string
}>>([])

// 测试加载状态
const testLoading = reactive({
  menu: false,
  starling: false,
  multiple: false
})

// 测试结果
const testResults = ref<Array<{
  time: string
  action: string
  duration: number
  fromCache: boolean
  result: string
}>>([])

// 刷新缓存统计信息
const refreshStats = () => {
  cacheStats.value = menuApiV2.getCacheStats()
  addLog('info', '刷新状态', '已更新缓存统计信息')
}

// 强制刷新缓存
const forceRefreshCache = async () => {
  try {
    addLog('warning', '强制刷新', '开始强制刷新缓存...')
    const startTime = Date.now()
    
    await menuApiV2.forceRefresh()
    
    const duration = Date.now() - startTime
    refreshStats()
    addLog('success', '强制刷新', `缓存刷新完成，耗时 ${duration}ms`)
    ElMessage.success('缓存刷新成功')
  } catch (error) {
    addLog('error', '强制刷新', `刷新失败: ${error}`)
    ElMessage.error('缓存刷新失败')
  }
}

// 清除缓存
const clearCache = () => {
  internalHomepageCache.clearInternalHomepageCache()
  refreshStats()
  addLog('warning', '清除缓存', '已清除所有缓存')
  ElMessage.success('缓存已清除')
}

// 测试获取菜单列表
const testGetMenuList = async () => {
  testLoading.menu = true
  try {
    const startTime = Date.now()
    const wasValidBefore = internalHomepageCache.isCacheValid()
    
    const result = await menuApiV2.getInternalMenuList()
    
    const duration = Date.now() - startTime
    const fromCache = wasValidBefore
    
    addTestResult('获取菜单列表', duration, fromCache, `成功获取 ${result.menuList.length} 个菜单项`)
    addLog('info', '测试', `菜单列表获取完成，耗时 ${duration}ms，来源: ${fromCache ? '缓存' : '网络'}`)
  } catch (error) {
    addTestResult('获取菜单列表', 0, false, `失败: ${error}`)
    addLog('error', '测试', `菜单列表获取失败: ${error}`)
  } finally {
    testLoading.menu = false
  }
}

// 测试获取StarlingMap
const testGetStarlingMap = async () => {
  testLoading.starling = true
  try {
    const startTime = Date.now()
    const wasValidBefore = internalHomepageCache.isCacheValid()
    
    const result = await menuApiV2.getStarlingMap()
    
    const duration = Date.now() - startTime
    const fromCache = wasValidBefore
    
    addTestResult('获取StarlingMap', duration, fromCache, `成功获取 ${Object.keys(result).length} 个多语言条目`)
    addLog('info', '测试', `StarlingMap获取完成，耗时 ${duration}ms，来源: ${fromCache ? '缓存' : '网络'}`)
  } catch (error) {
    addTestResult('获取StarlingMap', 0, false, `失败: ${error}`)
    addLog('error', '测试', `StarlingMap获取失败: ${error}`)
  } finally {
    testLoading.starling = false
  }
}

// 测试多次请求
const testMultipleRequests = async () => {
  testLoading.multiple = true
  try {
    addLog('info', '多次请求测试', '开始连续请求3次，验证缓存效果...')
    
    for (let i = 1; i <= 3; i++) {
      const startTime = Date.now()
      const wasValidBefore = internalHomepageCache.isCacheValid()
      
      await menuApiV2.getInternalMenuList()
      
      const duration = Date.now() - startTime
      const fromCache = wasValidBefore && i > 1
      
      addTestResult(`第${i}次请求`, duration, fromCache, '成功')
      await new Promise(resolve => setTimeout(resolve, 100)) // 短暂延迟
    }
    
    addLog('success', '多次请求测试', '测试完成，请查看测试结果验证缓存效果')
  } catch (error) {
    addLog('error', '多次请求测试', `测试失败: ${error}`)
  } finally {
    testLoading.multiple = false
  }
}

// 添加操作日志
const addLog = (type: string, action: string, message: string) => {
  operationLogs.value.unshift({
    time: new Date().toLocaleString(),
    action,
    message,
    type
  })
  
  // 保持最多50条日志
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 添加测试结果
const addTestResult = (action: string, duration: number, fromCache: boolean, result: string) => {
  testResults.value.unshift({
    time: new Date().toLocaleString(),
    action,
    duration,
    fromCache,
    result
  })
  
  // 保持最多20条结果
  if (testResults.value.length > 20) {
    testResults.value = testResults.value.slice(0, 20)
  }
}

// 格式化剩余时间
const formatRemainingTime = (seconds?: number): string => {
  if (!seconds || seconds <= 0) return '已过期'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`
  } else {
    return `${remainingSeconds}秒`
  }
}

onMounted(() => {
  refreshStats()
  addLog('info', '页面加载', '缓存管理页面已加载')
  
  // 定时刷新统计信息
  setInterval(refreshStats, 5000)
})
</script>

<style scoped>
.cache-management-page {
  padding: 20px;
}

.management-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.cache-overview,
.cache-details,
.cache-logs,
.test-area,
.test-results {
  margin-bottom: 30px;
}

.cache-overview h3,
.cache-details h3,
.cache-logs h3,
.test-area h3,
.test-results h3 {
  margin-bottom: 15px;
  color: #303133;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  width: 150px;
  color: #909399;
  font-size: 12px;
}

.log-action {
  width: 100px;
  font-weight: bold;
  margin-right: 10px;
}

.log-action.info {
  color: #409eff;
}

.log-action.success {
  color: #67c23a;
}

.log-action.warning {
  color: #e6a23c;
}

.log-action.error {
  color: #f56c6c;
}

.log-message {
  flex: 1;
  color: #606266;
}
</style>

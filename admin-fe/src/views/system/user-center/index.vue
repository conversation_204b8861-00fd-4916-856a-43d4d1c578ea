<template>
  <div class="user-center-container">
    <div class="page-header">
      <h2>个人中心</h2>
      <p>管理个人信息、安全设置和偏好配置</p>
    </div>

    <div class="content-container">
      <el-row :gutter="20">
        <!-- 左侧个人信息卡片 -->
        <el-col :span="8">
          <el-card class="profile-card">
            <div class="profile-header">
              <el-avatar :size="80" :src="userInfo.avatar" />
              <div class="profile-info">
                <h3>{{ userInfo.realName }}</h3>
                <p>{{ userInfo.username }}</p>
                <el-tag :type="getRoleTagType(userInfo.role)">{{ getRoleText(userInfo.role) }}</el-tag>
              </div>
            </div>
            
            <div class="profile-stats">
              <div class="stat-item">
                <div class="stat-value">{{ userInfo.loginCount }}</div>
                <div class="stat-label">登录次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userInfo.onlineDays }}</div>
                <div class="stat-label">在线天数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userInfo.lastLoginDays }}</div>
                <div class="stat-label">最后登录</div>
              </div>
            </div>

            <div class="profile-actions">
              <el-button type="primary" @click="activeTab = 'profile'">编辑资料</el-button>
              <el-button @click="activeTab = 'security'">安全设置</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧详细信息 -->
        <el-col :span="16">
          <el-card>
            <el-tabs v-model="activeTab" type="border-card">
              <!-- 个人资料 -->
              <el-tab-pane label="个人资料" name="profile">
                <el-form
                  ref="profileFormRef"
                  :model="profileForm"
                  :rules="profileRules"
                  label-width="100px"
                  size="large"
                >
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="真实姓名" prop="realName">
                        <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="用户名" prop="username">
                        <el-input v-model="profileForm.username" disabled />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="邮箱" prop="email">
                        <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="手机号" prop="phone">
                        <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="性别" prop="gender">
                        <el-radio-group v-model="profileForm.gender">
                          <el-radio label="male">男</el-radio>
                          <el-radio label="female">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="生日" prop="birthday">
                        <el-date-picker
                          v-model="profileForm.birthday"
                          type="date"
                          placeholder="选择生日"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-form-item label="个人简介" prop="bio">
                    <el-input
                      v-model="profileForm.bio"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入个人简介"
                    />
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="handleUpdateProfile">保存修改</el-button>
                    <el-button @click="handleResetProfile">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>

              <!-- 安全设置 -->
              <el-tab-pane label="安全设置" name="security">
                <div class="security-section">
                  <h4>修改密码</h4>
                  <el-form
                    ref="passwordFormRef"
                    :model="passwordForm"
                    :rules="passwordRules"
                    label-width="100px"
                    size="large"
                  >
                    <el-form-item label="当前密码" prop="currentPassword">
                      <el-input
                        v-model="passwordForm.currentPassword"
                        type="password"
                        placeholder="请输入当前密码"
                        show-password
                      />
                    </el-form-item>
                    <el-form-item label="新密码" prop="newPassword">
                      <el-input
                        v-model="passwordForm.newPassword"
                        type="password"
                        placeholder="请输入新密码"
                        show-password
                      />
                    </el-form-item>
                    <el-form-item label="确认密码" prop="confirmPassword">
                      <el-input
                        v-model="passwordForm.confirmPassword"
                        type="password"
                        placeholder="请再次输入新密码"
                        show-password
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" @click="handleChangePassword">修改密码</el-button>
                    </el-form-item>
                  </el-form>
                </div>

                <el-divider />

                <div class="security-section">
                  <h4>登录记录</h4>
                  <el-table :data="loginRecords" stripe>
                    <el-table-column prop="loginTime" label="登录时间" width="180" />
                    <el-table-column prop="ip" label="IP地址" width="150" />
                    <el-table-column prop="location" label="登录地点" />
                    <el-table-column prop="device" label="设备信息" />
                    <el-table-column prop="status" label="状态" width="100">
                      <template #default="{ row }">
                        <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                          {{ row.status === 'success' ? '成功' : '失败' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 偏好设置 -->
              <el-tab-pane label="偏好设置" name="preferences">
                <el-form
                  ref="preferencesFormRef"
                  :model="preferencesForm"
                  label-width="120px"
                  size="large"
                >
                  <el-form-item label="界面语言">
                    <el-select v-model="preferencesForm.language" style="width: 200px">
                      <el-option label="简体中文" value="zh-CN" />
                      <el-option label="English" value="en-US" />
                      <el-option label="العربية" value="ar" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="主题模式">
                    <el-radio-group v-model="preferencesForm.theme">
                      <el-radio label="light">浅色模式</el-radio>
                      <el-radio label="dark">深色模式</el-radio>
                      <el-radio label="auto">跟随系统</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="邮件通知">
                    <el-switch
                      v-model="preferencesForm.emailNotification"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </el-form-item>

                  <el-form-item label="短信通知">
                    <el-switch
                      v-model="preferencesForm.smsNotification"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </el-form-item>

                  <el-form-item label="桌面通知">
                    <el-switch
                      v-model="preferencesForm.desktopNotification"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="handleUpdatePreferences">保存设置</el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('profile')

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const preferencesFormRef = ref<FormInstance>()

// 用户信息
const userInfo = reactive({
  username: '<EMAIL>',
  realName: 'Danceiny',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  role: 'super_admin',
  loginCount: 1256,
  onlineDays: 365,
  lastLoginDays: '今天'
})

// 个人资料表单
const profileForm = reactive({
  username: '<EMAIL>',
  realName: 'Danceiny',
  email: '<EMAIL>',
  phone: '13800138000',
  gender: 'male',
  birthday: '',
  bio: '系统管理员，负责平台的整体运营和技术管理。'
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 偏好设置表单
const preferencesForm = reactive({
  language: 'zh-CN',
  theme: 'light',
  emailNotification: true,
  smsNotification: false,
  desktopNotification: true
})

// 登录记录
const loginRecords = ref([
  {
    loginTime: '2024-01-15 09:30:00',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10',
    status: 'success'
  },
  {
    loginTime: '2024-01-14 18:45:00',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10',
    status: 'success'
  },
  {
    loginTime: '2024-01-14 09:15:00',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10',
    status: 'success'
  }
])

// 表单验证规则
const profileRules: FormRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'super_admin': 'danger',
    'admin': 'warning',
    'operator': 'primary',
    'service': 'success'
  }
  return typeMap[role] || 'info'
}

// 获取角色文本
const getRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    'super_admin': '超级管理员',
    'admin': '系统管理员',
    'operator': '运营人员',
    'service': '客服人员'
  }
  return textMap[role] || role
}

// 更新个人资料
const handleUpdateProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置个人资料
const handleResetProfile = () => {
  if (profileFormRef.value) {
    profileFormRef.value.resetFields()
  }
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()

    ElMessageBox.confirm('确定要修改密码吗？', '确认操作', {
      type: 'warning'
    }).then(() => {
      ElMessage.success('密码修改成功')
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }).catch(() => {
      ElMessage.info('已取消操作')
    })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 更新偏好设置
const handleUpdatePreferences = () => {
  ElMessage.success('偏好设置保存成功')
}

// 组件挂载
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.user-center-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.profile-card {
  height: fit-content;
}

.profile-header {
  text-align: center;
  padding: 20px 0;
}

.profile-info {
  margin-top: 15px;
}

.profile-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.profile-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.profile-actions {
  padding: 20px 0;
  text-align: center;
}

.profile-actions .el-button {
  margin: 0 5px;
}

.security-section {
  margin-bottom: 30px;
}

.security-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}
</style>

<template>
  <div class="log-statistics">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.successCount }}</div>
              <div class="stat-label">成功请求</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon warning">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.warningCount }}</div>
              <div class="stat-label">警告请求</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon error">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.errorCount }}</div>
              <div class="stat-label">错误请求</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon info">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalCount }}</div>
              <div class="stat-label">总请求数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 响应时间分布图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>响应时间分布</span>
          </template>
          <div ref="responseTimeChart" style="height: 300px"></div>
        </el-card>
      </el-col>
      
      <!-- 供应商调用统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>供应商调用统计</span>
          </template>
          <div ref="supplierChart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 错误率趋势 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>错误率趋势</span>
              <el-radio-group v-model="timeRange" size="small" @change="updateErrorTrend">
                <el-radio-button label="1h">最近1小时</el-radio-button>
                <el-radio-button label="6h">最近6小时</el-radio-button>
                <el-radio-button label="24h">最近24小时</el-radio-button>
                <el-radio-button label="7d">最近7天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="errorTrendChart" style="height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { SuccessFilled, WarningFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { logViewerApi } from '@/api/logViewer'
import type { LogItem, GetLogStatisticsResponse, TrendPoint } from '@/api/logViewer/types'

// Props
interface Props {
  logs: LogItem[]
  loading?: boolean
  timeRange?: [string, string] | null
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  timeRange: null
})

// 响应式数据
const responseTimeChart = ref<HTMLElement>()
const supplierChart = ref<HTMLElement>()
const errorTrendChart = ref<HTMLElement>()
const timeRange = ref('1h')

let responseTimeChartInstance: echarts.ECharts | null = null
let supplierChartInstance: echarts.ECharts | null = null
let errorTrendChartInstance: echarts.ECharts | null = null

// 统计数据
const statistics = reactive({
  totalCount: 0,
  successCount: 0,
  warningCount: 0,
  errorCount: 0,
  avgResponseTime: 0
})

// 后端统计数据
const backendStats = ref<GetLogStatisticsResponse | null>(null)

// 生命周期
onMounted(async () => {
  await nextTick()
  initCharts()
  updateStatistics()
  loadBackendStatistics()
})

onUnmounted(() => {
  if (responseTimeChartInstance) {
    responseTimeChartInstance.dispose()
  }
  if (supplierChartInstance) {
    supplierChartInstance.dispose()
  }
  if (errorTrendChartInstance) {
    errorTrendChartInstance.dispose()
  }
})

// 监听数据变化
watch(() => props.logs, () => {
  updateStatistics()
  updateCharts()
}, { deep: true })

watch(() => props.timeRange, () => {
  loadBackendStatistics()
}, { deep: true })

// 方法
const initCharts = () => {
  initResponseTimeChart()
  initSupplierChart()
  initErrorTrendChart()
}

const initResponseTimeChart = () => {
  if (!responseTimeChart.value) return
  
  responseTimeChartInstance = echarts.init(responseTimeChart.value)
  
  const option = {
    title: {
      text: '响应时间分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '响应时间',
      type: 'pie',
      radius: ['40%', '70%'],
      data: [
        { value: 0, name: '< 100ms' },
        { value: 0, name: '100ms - 500ms' },
        { value: 0, name: '500ms - 1s' },
        { value: 0, name: '1s - 5s' },
        { value: 0, name: '> 5s' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  responseTimeChartInstance.setOption(option)
}

const initSupplierChart = () => {
  if (!supplierChart.value) return
  
  supplierChartInstance = echarts.init(supplierChart.value)
  
  const option = {
    title: {
      text: '供应商调用统计',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '调用次数',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }
  
  supplierChartInstance.setOption(option)
}

const initErrorTrendChart = () => {
  if (!errorTrendChart.value) return
  
  errorTrendChartInstance = echarts.init(errorTrendChart.value)
  
  const option = {
    title: {
      text: '错误率趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总请求数', '错误请求数', '错误率'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: '请求数',
        position: 'left'
      },
      {
        type: 'value',
        name: '错误率(%)',
        position: 'right',
        max: 100
      }
    ],
    series: [
      {
        name: '总请求数',
        type: 'bar',
        data: [],
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '错误请求数',
        type: 'bar',
        data: [],
        itemStyle: { color: '#F56C6C' }
      },
      {
        name: '错误率',
        type: 'line',
        yAxisIndex: 1,
        data: [],
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
  
  errorTrendChartInstance.setOption(option)
}

const updateStatistics = () => {
  const logs = props.logs
  
  statistics.totalCount = logs.length
  statistics.successCount = logs.filter(log => log.status === 'success').length
  statistics.warningCount = logs.filter(log => log.status === 'warning').length
  statistics.errorCount = logs.filter(log => log.status === 'error').length
  
  if (logs.length > 0) {
    const totalTime = logs.reduce((sum, log) => sum + log.costTime, 0)
    statistics.avgResponseTime = Math.round(totalTime / logs.length)
  } else {
    statistics.avgResponseTime = 0
  }
}

const updateCharts = () => {
  updateResponseTimeChart()
  updateSupplierChart()
  updateErrorTrend()
}

const updateResponseTimeChart = () => {
  if (!responseTimeChartInstance) return
  
  const logs = props.logs
  const ranges = [
    { name: '< 100ms', count: 0 },
    { name: '100ms - 500ms', count: 0 },
    { name: '500ms - 1s', count: 0 },
    { name: '1s - 5s', count: 0 },
    { name: '> 5s', count: 0 }
  ]
  
  logs.forEach(log => {
    const time = log.costTime
    if (time < 100) ranges[0].count++
    else if (time < 500) ranges[1].count++
    else if (time < 1000) ranges[2].count++
    else if (time < 5000) ranges[3].count++
    else ranges[4].count++
  })
  
  responseTimeChartInstance.setOption({
    series: [{
      data: ranges.map(range => ({
        value: range.count,
        name: range.name
      }))
    }]
  })
}

const updateSupplierChart = () => {
  if (!supplierChartInstance) return
  
  const logs = props.logs
  const supplierStats = new Map<string, number>()
  
  logs.forEach(log => {
    const supplier = log.apiOutSupplier || 'Unknown'
    supplierStats.set(supplier, (supplierStats.get(supplier) || 0) + 1)
  })
  
  const suppliers = Array.from(supplierStats.keys())
  const counts = Array.from(supplierStats.values())
  
  supplierChartInstance.setOption({
    xAxis: {
      data: suppliers
    },
    series: [{
      data: counts
    }]
  })
}

const updateErrorTrend = () => {
  if (!errorTrendChartInstance) return

  // 使用后端数据或前端数据
  if (backendStats.value?.trendData && backendStats.value.trendData.length > 0) {
    const trendData = backendStats.value.trendData
    const times = trendData.map(point => point.time)
    const totalRequests = trendData.map(point => point.totalCount)
    const errorRequests = trendData.map(point => point.errorCount)
    const errorRates = trendData.map(point => point.errorRate)

    errorTrendChartInstance.setOption({
      xAxis: {
        data: times
      },
      series: [
        { data: totalRequests },
        { data: errorRequests },
        { data: errorRates }
      ]
    })
  } else {
    // 生成模拟数据作为后备
    const hours = []
    const totalRequests = []
    const errorRequests = []
    const errorRates = []

    for (let i = 23; i >= 0; i--) {
      const hour = new Date(Date.now() - i * 60 * 60 * 1000).getHours()
      hours.push(`${hour}:00`)

      // 模拟数据
      const total = Math.floor(Math.random() * 100) + 50
      const errors = Math.floor(Math.random() * total * 0.1)
      const rate = total > 0 ? (errors / total * 100).toFixed(1) : '0'

      totalRequests.push(total)
      errorRequests.push(errors)
      errorRates.push(parseFloat(rate))
    }

    errorTrendChartInstance.setOption({
      xAxis: {
        data: hours
      },
      series: [
        { data: totalRequests },
        { data: errorRequests },
        { data: errorRates }
      ]
    })
  }
}

// 加载后端统计数据
const loadBackendStatistics = async () => {
  try {
    const params: any = {}

    if (props.timeRange && props.timeRange.length === 2) {
      params.startTime = props.timeRange[0]
      params.endTime = props.timeRange[1]
    }

    const response = await logViewerApi.getLogStatistics(params)
    backendStats.value = response

    // 更新统计卡片数据
    if (response) {
      statistics.totalCount = response.totalCount
      statistics.successCount = response.successCount
      statistics.warningCount = response.warningCount
      statistics.errorCount = response.errorCount
      statistics.avgResponseTime = response.avgCostTime
    }

    // 更新图表
    updateCharts()
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 失败时使用前端数据
    updateStatistics()
    updateCharts()
  }
}
</script>

<style scoped lang="scss">
.log-statistics {
  .stat-card {
    .stat-item {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        .el-icon {
          font-size: 24px;
          color: white;
        }
        
        &.success {
          background: linear-gradient(135deg, #67C23A, #85CE61);
        }
        
        &.warning {
          background: linear-gradient(135deg, #E6A23C, #EEBE77);
        }
        
        &.error {
          background: linear-gradient(135deg, #F56C6C, #F78989);
        }
        
        &.info {
          background: linear-gradient(135deg, #409EFF, #66B1FF);
        }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          line-height: 1;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          margin-top: 5px;
        }
      }
    }
  }
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="导出日志"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="exportForm" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="导出格式" prop="format">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="csv">CSV格式</el-radio>
          <el-radio label="json">JSON格式</el-radio>
          <el-radio label="excel" disabled>Excel格式 (开发中)</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="导出数量" prop="pageSize">
        <el-input-number
          v-model="exportForm.pageSize"
          :min="1"
          :max="10000"
          :step="100"
          style="width: 200px"
        />
        <span class="form-tip">最多支持导出10,000条记录</span>
      </el-form-item>
      
      <el-form-item label="导出字段" prop="fields" v-if="exportForm.format === 'csv'">
        <el-checkbox-group v-model="exportForm.fields">
          <el-checkbox label="timestamp">时间戳</el-checkbox>
          <el-checkbox label="logId">日志ID</el-checkbox>
          <el-checkbox label="sessionId">会话ID</el-checkbox>
          <el-checkbox label="userId">用户ID</el-checkbox>
          <el-checkbox label="apiInPath">入口API</el-checkbox>
          <el-checkbox label="apiOutSupplier">供应商</el-checkbox>
          <el-checkbox label="apiOutPath">出口API</el-checkbox>
          <el-checkbox label="httpStatusCode">HTTP状态码</el-checkbox>
          <el-checkbox label="costTime">耗时</el-checkbox>
          <el-checkbox label="bizErrorCode">业务错误码</el-checkbox>
        </el-checkbox-group>
        <div class="field-actions">
          <el-button size="small" @click="selectAllFields">全选</el-button>
          <el-button size="small" @click="clearAllFields">清空</el-button>
          <el-button size="small" @click="selectDefaultFields">默认字段</el-button>
        </div>
      </el-form-item>
      
      <el-form-item label="文件名前缀">
        <el-input
          v-model="exportForm.filePrefix"
          placeholder="可选，默认为 logs"
          style="width: 300px"
        />
        <span class="form-tip">文件名格式：前缀_时间戳.格式</span>
      </el-form-item>
      
      <el-alert
        title="导出说明"
        type="info"
        :closable="false"
        show-icon
      >
        <ul class="export-tips">
          <li>导出将基于当前的搜索条件进行</li>
          <li>大量数据导出可能需要较长时间，请耐心等待</li>
          <li>CSV格式适合在Excel中查看，JSON格式保留完整数据结构</li>
          <li>导出文件将自动下载到您的默认下载目录</li>
        </ul>
      </el-alert>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          {{ exporting ? '导出中...' : '开始导出' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { logViewerApi } from '@/api/logViewer'
import type { QueryLogsParams } from '@/api/logViewer/types'

// Props
interface Props {
  visible: boolean
  searchParams: QueryLogsParams
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'export-success': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const exporting = ref(false)

const exportForm = reactive({
  format: 'csv',
  pageSize: 1000,
  fields: ['timestamp', 'logId', 'sessionId', 'apiInPath', 'apiOutSupplier', 'httpStatusCode', 'costTime'],
  filePrefix: 'logs'
})

// 表单验证规则
const rules: FormRules = {
  format: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ],
  pageSize: [
    { required: true, message: '请输入导出数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '导出数量必须在1-10000之间', trigger: 'blur' }
  ],
  fields: [
    { type: 'array', min: 1, message: '请至少选择一个导出字段', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const selectAllFields = () => {
  exportForm.fields = [
    'timestamp', 'logId', 'sessionId', 'userId', 'apiInPath', 
    'apiOutSupplier', 'apiOutPath', 'httpStatusCode', 'costTime', 'bizErrorCode'
  ]
}

const clearAllFields = () => {
  exportForm.fields = []
}

const selectDefaultFields = () => {
  exportForm.fields = ['timestamp', 'logId', 'sessionId', 'apiInPath', 'apiOutSupplier', 'httpStatusCode', 'costTime']
}

const handleExport = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 确认导出
    const confirmResult = await ElMessageBox.confirm(
      `确定要导出 ${exportForm.pageSize} 条日志记录吗？`,
      '确认导出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (confirmResult !== 'confirm') return
    
    exporting.value = true
    
    // 构建导出参数
    const exportParams = {
      ...props.searchParams,
      format: exportForm.format,
      pageSize: exportForm.pageSize,
      fields: exportForm.format === 'csv' ? exportForm.fields : undefined
    }
    
    // 调用导出API
    const response = await logViewerApi.exportLogs(exportParams)
    
    // 处理下载
    if (response.downloadUrl) {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = response.downloadUrl
      link.download = response.fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElMessage.success(`导出成功！文件大小：${formatFileSize(response.fileSize)}`)
      emit('export-success')
      handleClose()
    } else {
      ElMessage.error('导出失败：未获取到下载链接')
    }
  } catch (error: any) {
    console.error('导出失败:', error)
    if (error !== 'cancel') {
      ElMessage.error(error.message || '导出失败')
    }
  } finally {
    exporting.value = false
  }
}

// 工具方法
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped lang="scss">
.form-tip {
  margin-left: 10px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.field-actions {
  margin-top: 10px;
  
  .el-button {
    margin-right: 10px;
  }
}

.export-tips {
  margin: 0;
  padding-left: 20px;
  
  li {
    margin-bottom: 5px;
    color: var(--el-text-color-regular);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>

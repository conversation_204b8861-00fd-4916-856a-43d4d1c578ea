<template>
  <ArtTableFullScreen>
    <div class="starling-config" id="table-full-screen">
      <ArtSearchBar v-model:filter="filters" :items="searchItems" @search="getList" />
      <ElCard class="art-table-card" shadow="never">
        <ArtTableHeader @refresh="getList">
          <template #left>
            <ElButton type="primary" @click="handleCreate">新增配置</ElButton>
            <ElButton 
              type="success" 
              @click="handleExport"
              :loading="exportLoading"
            >
              导出配置
            </ElButton>
            <ElButton 
              type="warning" 
              @click="handleImport"
            >
              导入配置
            </ElButton>
          </template>
        </ArtTableHeader>
        <ArtTable
          :data="tableData"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :pageSize="pagination.pageSize"
          :total="pagination.total"
          @current-change="pageChange"
          @size-change="sizeChange"
        >
          <template #default>
            <ElTableColumn
              v-for="col in columns"
              :key="col.prop"
              :prop="col.prop"
              :label="col.label"
              :min-width="col.minWidth || 120"
            />
            <ElTableColumn label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <ElButton type="primary" size="small" @click="handleEdit(row)">
                  编辑
                </ElButton>
                <ElButton type="danger" size="small" @click="handleDelete(row)">
                  删除
                </ElButton>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>
      </ElCard>

      <!-- 新增/编辑对话框 -->
      <ElDialog
        v-model="dialogVisible"
        :title="dialogType === 'create' ? '新增多语言配置' : '编辑多语言配置'"
        width="600px"
        @close="resetForm"
      >
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <ElFormItem label="应用名称" prop="application">
            <ElSelect v-model="formData.application" placeholder="请选择应用">
              <ElOption
                v-for="app in applicationList"
                :key="app"
                :label="app"
                :value="app"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="空间名称" prop="space">
            <ElInput v-model="formData.space" placeholder="请输入空间名称" />
          </ElFormItem>
          <ElFormItem label="键名" prop="key">
            <ElInput v-model="formData.key" placeholder="请输入键名" />
          </ElFormItem>
          <ElFormItem label="中文" prop="content.zh">
            <ElInput v-model="formData.content.zh" placeholder="请输入中文内容" />
          </ElFormItem>
          <ElFormItem label="英文" prop="content.en">
            <ElInput v-model="formData.content.en" placeholder="请输入英文内容" />
          </ElFormItem>
          <ElFormItem label="阿拉伯文" prop="content.ar">
            <ElInput v-model="formData.content.ar" placeholder="请输入阿拉伯文内容" />
          </ElFormItem>
        </ElForm>
        <template #footer>
          <ElButton @click="dialogVisible = false">取消</ElButton>
          <ElButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </ElButton>
        </template>
      </ElDialog>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from 'vue'
import { ElMessage, ElMessageBox, ElButton, ElTableColumn, ElCard, ElDialog, ElForm, ElFormItem, ElInput, ElSelect, ElOption } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import ArtTable from '@/components/core/tables/ArtTable.vue'
import ArtTableHeader from '@/components/core/tables/ArtTableHeader.vue'
import ArtTableFullScreen from '@/components/core/tables/ArtTableFullScreen.vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { starlingApi, type StarlingItem, type CreateStarlingRequest, type UpdateStarlingRequest } from '@/api/starlingApi'
import type { BffTable } from '@/types/bff'
import type { SearchFormItem } from '@/types'

defineOptions({ name: 'StarlingConfig' })

const loading = ref(false)
const submitLoading = ref(false)
const exportLoading = ref(false)
const dialogVisible = ref(false)
const dialogType = ref<'create' | 'edit'>('create')

// 搜索与分页条件
const filters = reactive({
  application: '',
  space: '',
  key: '',
  keyword: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 搜索项配置
const searchItems: SearchFormItem[] = [
  {
    type: 'input',
    prop: 'keyword',
    label: '关键词',
    placeholder: '搜索键名、中文、英文内容'
  },
  {
    type: 'input',
    prop: 'application',
    label: '应用名称',
    placeholder: '请输入应用名称'
  },
  {
    type: 'input',
    prop: 'space',
    label: '空间名称',
    placeholder: '请输入空间名称'
  }
]

// 表格数据
const tableData = ref<StarlingItem[]>([])
const headerKeys = ref<string[]>([])
const applicationList = ref<string[]>([])

// 表格列配置
const columns = computed(() => [
  { prop: 'id', label: 'ID', minWidth: 80 },
  { prop: 'application', label: '应用名称', minWidth: 120 },
  { prop: 'space', label: '空间名称', minWidth: 120 },
  { prop: 'key', label: '键名', minWidth: 200 },
  { prop: 'content.zh', label: '中文', minWidth: 150 },
  { prop: 'content.en', label: '英文', minWidth: 150 },
  { prop: 'content.ar', label: '阿拉伯文', minWidth: 150 },
  { prop: 'updateTime', label: '更新时间', minWidth: 160 }
])

// 表单数据
const formRef = ref<FormInstance>()
const formData = reactive<CreateStarlingRequest & { id?: number }>({
  id: undefined,
  entityId: 0,
  application: '',
  space: '',
  key: '',
  content: {
    zh: '',
    en: '',
    ar: ''
  }
})

// 表单验证规则
const formRules: FormRules = {
  application: [{ required: true, message: '请选择应用名称', trigger: 'change' }],
  space: [{ required: true, message: '请输入空间名称', trigger: 'blur' }],
  key: [{ required: true, message: '请输入键名', trigger: 'blur' }],
  'content.zh': [{ required: true, message: '请输入中文内容', trigger: 'blur' }],
  'content.en': [{ required: true, message: '请输入英文内容', trigger: 'blur' }]
}

// 获取列表数据
async function getList() {
  loading.value = true
  try {
    const table = await starlingApi.listStarling({
      ...filters,
      page: { pageNum: pagination.currentPage, pageSize: pagination.pageSize }
    })

    if (table) {
      pagination.total = table.total
      tableData.value = table.rows.map(r => r.raw)
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取starling列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 分页处理
function pageChange(page: number) {
  pagination.currentPage = page
  getList()
}

function sizeChange(size: number) {
  pagination.pageSize = size
  getList()
}

// 获取应用列表
async function getApplicationList() {
  try {
    applicationList.value = await starlingApi.getApplicationList()
  } catch (error) {
    console.error('获取应用列表失败:', error)
  }
}

// 新增配置
function handleCreate() {
  dialogType.value = 'create'
  dialogVisible.value = true
}

// 编辑配置
function handleEdit(row: StarlingItem) {
  dialogType.value = 'edit'
  Object.assign(formData, {
    id: row.id,
    entityId: row.entityId,
    application: row.application,
    space: row.space,
    key: row.key,
    content: { ...row.content }
  })
  dialogVisible.value = true
}

// 删除配置
async function handleDelete(row: StarlingItem) {
  try {
    await ElMessageBox.confirm('确定要删除这个配置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await starlingApi.deleteStarling(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除starling配置失败:', error)
    }
  }
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (dialogType.value === 'create') {
      await starlingApi.createStarling(formData)
      ElMessage.success('创建成功')
    } else {
      await starlingApi.updateStarling(formData as UpdateStarlingRequest)
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    getList()
  } catch (error) {
    if (error !== false) { // 表单验证失败时error为false
      ElMessage.error(dialogType.value === 'create' ? '创建失败' : '更新失败')
      console.error('提交starling配置失败:', error)
    }
  } finally {
    submitLoading.value = false
  }
}

// 导出配置
async function handleExport() {
  exportLoading.value = true
  try {
    const result = await starlingApi.exportStarling({
      application: filters.application || undefined,
      space: filters.space || undefined
    })
    
    // 下载文件
    const link = document.createElement('a')
    link.href = result.fileUrl
    link.download = 'starling-config.xlsx'
    link.click()
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出starling配置失败:', error)
  } finally {
    exportLoading.value = false
  }
}

// 导入配置
function handleImport() {
  ElMessage.info('导入功能开发中')
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    id: undefined,
    entityId: 0,
    application: '',
    space: '',
    key: '',
    content: {
      zh: '',
      en: '',
      ar: ''
    }
  })
}

onMounted(() => {
  getList()
  getApplicationList()
})
</script>

<style scoped>
.starling-config {
  height: 100%;
}
</style>

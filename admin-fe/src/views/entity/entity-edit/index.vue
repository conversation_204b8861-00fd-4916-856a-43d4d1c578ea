<template>
  <div class="entity-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑实体' : '新增实体' }}</h2>
      <p>{{ isEdit ? '修改实体信息和配置' : '创建新的实体记录' }}</p>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="large"
      >
        <el-card class="form-card" header="基本信息">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="实体名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入实体名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实体类型" prop="type">
                <el-select
                  v-model="formData.type"
                  placeholder="请选择实体类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in entityTypes"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="根实体ID">
                <el-input
                  v-model="formData.rootEntityId"
                  placeholder="请输入根实体ID"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="父实体ID">
                <el-input
                  v-model="formData.parentEntityId"
                  placeholder="请输入父实体ID"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态">
                <el-select
                  v-model="formData.status"
                  placeholder="请选择状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in statusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 配置信息 -->
        <el-card class="form-card" header="配置信息">
          <el-form-item label="Profile配置">
            <ArtJsonEditor
              v-model="profileJson"
              height="200px"
              placeholder="请输入Profile配置(JSON格式)"
              @validate="onProfileValidate"
            />
          </el-form-item>

          <el-form-item label="分销配置">
            <ArtJsonEditor
              v-model="distributionConfigJson"
              height="200px"
              placeholder="请输入分销配置(JSON格式)"
              @validate="onDistributionValidate"
            />
          </el-form-item>

          <el-form-item label="财务配置">
            <ArtJsonEditor
              v-model="financeConfigJson"
              height="200px"
              placeholder="请输入财务配置(JSON格式)"
              @validate="onFinanceValidate"
            />
          </el-form-item>

          <el-form-item label="供应商配置">
            <ArtJsonEditor
              v-model="supplierConfigJson"
              height="200px"
              placeholder="请输入供应商配置(JSON格式)"
              @validate="onSupplierValidate"
            />
          </el-form-item>
        </el-card>

        <div class="form-actions">
          <el-button size="large" @click="handleCancel">取消</el-button>
          <el-button type="primary" size="large" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { EntityService } from '@/api/entityApi'
import { EntityType, EntityTypeHelper } from '@/types/entity'
import type { Entity, CreateEntityRequest, UpdateEntityRequest } from '@/types/entity'
import ArtJsonEditor from '@/components/core/forms/art-json-editor/index.vue'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据 - 匹配后端Entity结构
const formData = reactive({
  id: '',
  name: '',
  type: EntityType.Tenant, // 默认为租户类型
  rootEntityId: '',
  parentEntityId: '',
  profile: {} as Record<string, any>,
  distributionConfig: {} as Record<string, any>,
  financeConfig: {} as Record<string, any>,
  supplierConfig: {} as Record<string, any>,
  status: 1 // 1为正常，0为禁用
})

// 实体类型选项 - 使用位运算的枚举
const entityTypes = [
  { label: '平台', value: EntityType.Platform },
  { label: '租户', value: EntityType.Tenant },
  { label: '客户', value: EntityType.Customer },
  { label: '外网', value: EntityType.Extranet },
  { label: '供应商', value: EntityType.Supplier }
]

// 状态选项
const statusOptions = [
  { label: '正常', value: 1 },
  { label: '禁用', value: 0 }
]

// JSON配置字符串（用于编辑）
const profileJson = ref('')
const distributionConfigJson = ref('')
const financeConfigJson = ref('')
const supplierConfigJson = ref('')

// JSON验证状态
const jsonValidation = reactive({
  profile: true,
  distribution: true,
  finance: true,
  supplier: true
})

// JSON验证方法
const onProfileValidate = (isValid: boolean) => {
  jsonValidation.profile = isValid
  updateConfigFromJson()
}

const onDistributionValidate = (isValid: boolean) => {
  jsonValidation.distribution = isValid
  updateConfigFromJson()
}

const onFinanceValidate = (isValid: boolean) => {
  jsonValidation.finance = isValid
  updateConfigFromJson()
}

const onSupplierValidate = (isValid: boolean) => {
  jsonValidation.supplier = isValid
  updateConfigFromJson()
}

// 检查所有JSON是否有效
const isAllJsonValid = computed(() => {
  return jsonValidation.profile &&
         jsonValidation.distribution &&
         jsonValidation.finance &&
         jsonValidation.supplier
})

// 监听JSON字符串变化，更新formData
const updateConfigFromJson = () => {
  try {
    if (profileJson.value && jsonValidation.profile) {
      formData.profile = JSON.parse(profileJson.value)
    }
    if (distributionConfigJson.value && jsonValidation.distribution) {
      formData.distributionConfig = JSON.parse(distributionConfigJson.value)
    }
    if (financeConfigJson.value && jsonValidation.finance) {
      formData.financeConfig = JSON.parse(financeConfigJson.value)
    }
    if (supplierConfigJson.value && jsonValidation.supplier) {
      formData.supplierConfig = JSON.parse(supplierConfigJson.value)
    }
  } catch (error) {
    console.warn('JSON配置解析失败:', error)
  }
}

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入实体名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择实体类型', trigger: 'change' }
  ]
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 检查JSON配置是否有效
    if (!isAllJsonValid.value) {
      ElMessage.error('请检查JSON配置格式是否正确')
      return
    }

    // 在提交前更新配置
    updateConfigFromJson()

    ElMessageBox.confirm(
      `确定要${isEdit.value ? '更新' : '创建'}实体吗？`,
      '确认操作',
      { type: 'warning' }
    ).then(async () => {
      try {
        if (isEdit.value) {
          // 更新实体
          const updateData: UpdateEntityRequest = {
            id: formData.id,
            name: formData.name,
            type: formData.type,
            rootEntityId: formData.rootEntityId,
            parentEntityId: formData.parentEntityId,
            profile: formData.profile,
            distributionConfig: formData.distributionConfig,
            financeConfig: formData.financeConfig,
            supplierConfig: formData.supplierConfig
          }
          await EntityService.updateEntity(updateData)
          ElMessage.success('实体更新成功')
        } else {
          // 创建实体
          const createData: CreateEntityRequest = {
            name: formData.name,
            type: formData.type,
            rootEntityId: formData.rootEntityId,
            parentEntityId: formData.parentEntityId,
            profile: formData.profile,
            distributionConfig: formData.distributionConfig,
            financeConfig: formData.financeConfig,
            supplierConfig: formData.supplierConfig
          }
          await EntityService.createEntity(createData)
          ElMessage.success('实体创建成功')
        }
        router.back()
      } catch (error) {
        console.error('保存实体失败:', error)
        ElMessage.error(`实体${isEdit.value ? '更新' : '创建'}失败`)
      }
    }).catch(() => {
      ElMessage.info('已取消操作')
    })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 加载实体数据
const loadEntityData = async (entityId: string) => {
  try {
    const entity = await EntityService.getEntity(entityId)
    console.log('加载的实体数据:', entity)

    // 将后端数据映射到表单
    Object.assign(formData, {
      id: entity.id,
      name: entity.name,
      type: entity.type,
      rootEntityId: entity.rootEntityId,
      parentEntityId: entity.parentEntityId,
      profile: entity.profile || {},
      distributionConfig: entity.distributionConfig || {},
      financeConfig: entity.financeConfig || {},
      supplierConfig: entity.supplierConfig || {},
      status: entity.status
    })

    // 将配置对象转换为JSON字符串用于编辑
    profileJson.value = JSON.stringify(entity.profile || {}, null, 2)
    distributionConfigJson.value = JSON.stringify(entity.distributionConfig || {}, null, 2)
    financeConfigJson.value = JSON.stringify(entity.financeConfig || {}, null, 2)
    supplierConfigJson.value = JSON.stringify(entity.supplierConfig || {}, null, 2)
  } catch (error) {
    console.error('加载实体数据失败:', error)
    ElMessage.error('加载实体数据失败')
  }
}

// 组件挂载
onMounted(() => {
  if (isEdit.value) {
    // 编辑模式，加载实体数据
    const entityId = route.params.id as string
    if (entityId) {
      loadEntityData(entityId)
    }
  }
})
</script>

<style scoped>
.entity-edit-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-container {
  max-width: 800px;
}

.form-card {
  margin-bottom: 20px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

/* JSON编辑器样式优化 */
:deep(.art-json-editor) {
  border-radius: 6px;
  transition: border-color 0.3s ease;
}

:deep(.art-json-editor:hover) {
  border-color: var(--el-color-primary);
}

:deep(.art-json-editor .editor-container) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 配置信息卡片样式 */
.form-card .el-form-item {
  margin-bottom: 24px;
}

.form-card .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}
</style>

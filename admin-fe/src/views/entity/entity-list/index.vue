<template>
  <ArtTableFullScreen>
    <div class="entity-list-container" id="table-full-screen">
      <ArtSearchBar v-model:filter="filters" :items="searchItems" @search="getList" />
      <ElCard class="art-table-card" shadow="never">
        <ArtTableHeader v-model:columns="tableColumns" @refresh="getList">
          <template #left>
            <ElButton type="primary" @click="handleAdd">新增实体</ElButton>
            <ElButton @click="handleBatchImport">批量导入</ElButton>
            <ElButton @click="handleExport">导出数据</ElButton>
          </template>
        </ArtTableHeader>
        <BffTable
          :data="bffTableData"
          :loading="loading"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          @current-change="pageChange"
          @size-change="sizeChange"
          @action-click="handleActionClick"
        />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, ElButton, ElCard } from 'element-plus'
import { useRouter } from 'vue-router'
import BffTable from '@/components/core/tables/BffTable.vue'
import ArtTableHeader from '@/components/core/tables/ArtTableHeader.vue'
import ArtTableFullScreen from '@/components/core/tables/ArtTableFullScreen.vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { EntityService } from '@/api/entityApi'
import type { BffTable as BffTableType } from '@/types/bff'
import type { SearchFormItem } from '@/types'

const loading = ref(false)
const router = useRouter()

// 搜索与分页条件
const filters = reactive({
  parentEntityIds: [] as number[]
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 搜索项（后续补充）
const searchItems: SearchFormItem[] = []

// BFF 表格数据
const bffTableData = ref<BffTableType<any> | undefined>()

// 表格列配置（用于 ArtTableHeader）
const tableColumns = ref<any[]>([])

async function getList() {
  loading.value = true
  try {
    const table = await EntityService.listEntity({
      parentEntityIds: filters.parentEntityIds,
      page: { pageNum: pagination.currentPage, pageSize: pagination.pageSize }
    })

    if (table) {
      console.log('[DEBUG] 后端返回的实体表格数据:', table)

      // 直接使用 BFF Table 数据，让 BffTable 组件处理数据转换
      bffTableData.value = table
      pagination.total = table.total

      // 为 ArtTableHeader 生成列配置
      tableColumns.value = table.headerKeys.map((key) => ({
        prop: key,
        label: key,
        checked: true
      }))
    }
  } catch (error) {
    console.error('获取实体列表失败:', error)
    ElMessage.error('获取实体列表失败')
  } finally {
    loading.value = false
  }
}

function pageChange(page: number) {
  pagination.currentPage = page
  getList()
}

function sizeChange(size: number) {
  pagination.pageSize = size
  getList()
}

function handleAdd() {
  router.push('/entity/entity-edit/new')
}

function handleBatchImport() {
  ElMessage.info('批量导入功能开发中')
}

function handleExport() {
  ElMessage.info('导出数据功能开发中')
}

function editEntity(row: any) {
  console.log('编辑实体:', row)
  // 从BFF Table的原始数据中获取实体ID
  const entityId = row._raw?.id || row.key
  router.push(`/entity/entity-edit/${entityId}`)
}

function deleteEntity(row: any) {
  const entityName = row._raw?.name || '未知实体'
  const entityId = row._raw?.id || row.key

  ElMessageBox.confirm(`确定要删除实体 ${entityName} 吗？此操作不可恢复！`, '确认删除', {
    type: 'error'
  }).then(async () => {
    try {
      await EntityService.deleteEntity(entityId)
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    } catch (error) {
      console.error('删除实体失败:', error)
      ElMessage.error('删除实体失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

function handleActionClick(action: string, row: any) {
  console.log('[DEBUG] 操作点击:', action, row)

  // 根据 actionId 执行相应的操作
  switch (action) {
    case 'edit':
      editEntity(row)
      break
    case 'delete':
      deleteEntity(row)
      break
    default:
      ElMessage.warning(`未知操作: ${action}`)
  }
}

// 组件挂载
onMounted(() => {
  getList()
})
</script>

<style scoped>
.entity-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.art-table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.art-table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
}
</style>

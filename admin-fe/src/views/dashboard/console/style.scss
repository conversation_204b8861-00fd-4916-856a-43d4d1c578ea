@use '@styles/variables.scss' as *;

.console {
  --card-spacing: 20px;

  // 卡片头部
  :deep(.card-header) {
    display: flex;
    justify-content: space-between;
    padding: 20px 25px 5px 0;

    .title {
      h4 {
        font-size: 18px;
        font-weight: 500;
        color: var(--art-gray-900) !important;
      }

      p {
        margin-top: 3px;
        font-size: 13px;
        color: var(--art-gray-600) !important;

        span {
          margin-left: 10px;
          color: #52c41a;
        }
      }
    }
  }

  // 设置卡片背景色、圆角、间隙
  :deep(.card-list .card),
  .card {
    margin-bottom: var(--card-spacing);
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;
  }

  @media screen and (max-width: $device-phone) {
    --card-spacing: 15px;
  }
}

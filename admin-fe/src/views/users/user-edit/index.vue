<template>
  <div class="user-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑用户' : '新增用户' }}</h2>
      <p>{{ isEdit ? '修改用户信息和权限设置' : '创建新的用户账户' }}</p>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="large"
      >
        <el-card class="form-card" header="基本信息">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="formData.username"
                  placeholder="请输入用户名"
                  :disabled="isEdit"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="真实姓名" prop="realName">
                <el-input
                  v-model="formData.realName"
                  placeholder="请输入真实姓名"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入邮箱地址"
                  type="email"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="formData.phone"
                  placeholder="请输入手机号"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="!isEdit">
            <el-col :span="12">
              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="formData.password"
                  placeholder="请输入密码"
                  type="password"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="formData.confirmPassword"
                  placeholder="请再次输入密码"
                  type="password"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="form-card" header="角色权限">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户类型" prop="userType">
                <el-select
                  v-model="formData.userType"
                  placeholder="请选择用户类型"
                  style="width: 100%"
                  @change="handleUserTypeChange"
                >
                  <el-option label="租户用户" value="tenant" />
                  <el-option label="客户用户" value="customer" />
                  <el-option label="平台用户" value="platform" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色" prop="role">
                <el-select
                  v-model="formData.role"
                  placeholder="请选择角色"
                  style="width: 100%"
                >
                  <el-option
                    v-for="role in availableRoles"
                    :key="role.value"
                    :label="role.label"
                    :value="role.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="formData.userType === 'tenant'">
            <el-col :span="12">
              <el-form-item label="租户名称" prop="tenantName">
                <el-input
                  v-model="formData.tenantName"
                  placeholder="请输入租户名称"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="formData.userType === 'customer'">
            <el-col :span="12">
              <el-form-item label="公司名称" prop="company">
                <el-input
                  v-model="formData.company"
                  placeholder="请输入公司名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户等级" prop="level">
                <el-select
                  v-model="formData.level"
                  placeholder="请选择客户等级"
                  style="width: 100%"
                >
                  <el-option label="VIP客户" value="vip" />
                  <el-option label="普通客户" value="normal" />
                  <el-option label="新客户" value="new" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="formData.userType === 'platform'">
            <el-col :span="12">
              <el-form-item label="部门" prop="department">
                <el-select
                  v-model="formData.department"
                  placeholder="请选择部门"
                  style="width: 100%"
                >
                  <el-option label="技术部" value="tech" />
                  <el-option label="运营部" value="operation" />
                  <el-option label="客服部" value="service" />
                  <el-option label="财务部" value="finance" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="权限设置" prop="permissions">
            <el-checkbox-group v-model="formData.permissions">
              <el-checkbox
                v-for="permission in availablePermissions"
                :key="permission.value"
                :label="permission.value"
                :disabled="permission.disabled"
              >
                {{ permission.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>

        <el-card class="form-card" header="其他设置">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                  <el-radio label="active">激活</el-radio>
                  <el-radio label="disabled">禁用</el-radio>
                  <el-radio label="pending">待激活</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-card>

        <div class="form-actions">
          <el-button size="large" @click="handleCancel">取消</el-button>
          <el-button type="primary" size="large" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const formData = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  userType: '',
  role: '',
  tenantName: '',
  company: '',
  level: '',
  department: '',
  permissions: [] as string[],
  status: 'active',
  remark: ''
})

// 可用角色选项
const availableRoles = computed(() => {
  const roleMap: Record<string, Array<{label: string, value: string}>> = {
    tenant: [
      { label: '租户管理员', value: 'tenant_admin' },
      { label: '租户用户', value: 'tenant_user' }
    ],
    customer: [
      { label: '企业管理员', value: 'company_admin' },
      { label: '普通用户', value: 'normal_user' }
    ],
    platform: [
      { label: '超级管理员', value: 'super_admin' },
      { label: '系统管理员', value: 'admin' },
      { label: '运营人员', value: 'operator' },
      { label: '客服人员', value: 'service' }
    ]
  }
  return roleMap[formData.userType] || []
})

// 可用权限选项
const availablePermissions = computed(() => {
  const basePermissions = [
    { label: '用户管理', value: 'user_management', disabled: false },
    { label: '订单管理', value: 'order_management', disabled: false },
    { label: '数据统计', value: 'data_statistics', disabled: false },
    { label: '系统设置', value: 'system_settings', disabled: formData.userType !== 'platform' },
    { label: '权限管理', value: 'permission_management', disabled: formData.role !== 'super_admin' },
    { label: '日志管理', value: 'log_management', disabled: formData.userType !== 'platform' }
  ]
  return basePermissions
})

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  userType: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 用户类型变化处理
const handleUserTypeChange = () => {
  formData.role = ''
  formData.permissions = []
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    ElMessageBox.confirm(
      `确定要${isEdit.value ? '更新' : '创建'}用户吗？`,
      '确认操作',
      { type: 'warning' }
    ).then(() => {
      // 这里应该调用API保存数据
      console.log('提交数据:', formData)
      ElMessage.success(`用户${isEdit.value ? '更新' : '创建'}成功`)
      router.back()
    }).catch(() => {
      ElMessage.info('已取消操作')
    })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 组件挂载
onMounted(() => {
  if (isEdit.value) {
    // 编辑模式，加载用户数据
    const userId = route.params.id
    console.log('加载用户数据:', userId)
    
    // 模拟加载数据
    Object.assign(formData, {
      username: 'test_user',
      realName: '测试用户',
      email: '<EMAIL>',
      phone: '13800138000',
      userType: 'tenant',
      role: 'tenant_user',
      tenantName: '测试租户',
      permissions: ['user_management', 'order_management'],
      status: 'active',
      remark: '这是一个测试用户'
    })
  }
})
</script>

<style scoped>
.user-edit-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-container {
  max-width: 800px;
}

.form-card {
  margin-bottom: 20px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}
</style>

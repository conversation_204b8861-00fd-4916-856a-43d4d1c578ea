<template>
  <ArtTableFullScreen>
    <div class="customer-users-container" id="table-full-screen">
      <ArtSearchBar v-model:filter="filters" :items="searchItems" @search="getList" />
      <ElCard class="art-table-card" shadow="never">
        <ArtTableHeader v-model:columns="tableColumns" @refresh="getList">
          <template #left>
            <ElButton type="primary" @click="handleInvite">邀请用户</ElButton>
          </template>
        </ArtTableHeader>
        <BffTable
          :data="bffTableData"
          :loading="loading"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          @current-change="pageChange"
          @size-change="sizeChange"
          @action-click="handleActionClick"
        />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElButton, ElCard } from 'element-plus'
import BffTable from '@/components/core/tables/BffTable.vue'
import ArtTableHeader from '@/components/core/tables/ArtTableHeader.vue'
import ArtTableFullScreen from '@/components/core/tables/ArtTableFullScreen.vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { UserService } from '@/api/usersApi'
import type { BffTable as BffTableType } from '@/types/bff'
import type { SearchFormItem } from '@/types'

const loading = ref(false)

// 搜索与分页条件
const filters = reactive({
  activated: true,
  entityIds: [] as number[]
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 搜索项（后续补充）
const searchItems: SearchFormItem[] = []

// BFF 表格数据
const bffTableData = ref<BffTableType<any> | undefined>()

// 表格列配置（用于 ArtTableHeader）
const tableColumns = ref<any[]>([])

async function getList() {
  loading.value = true
  try {
    const table = (await UserService.listCustomerUsers({
      entityIds: filters.entityIds,
      activated: filters.activated,
      page: { pageNum: pagination.currentPage, pageSize: pagination.pageSize }
    })) as unknown as BffTableType<any>

    if (table) {
      // 直接使用 BFF Table 数据
      bffTableData.value = table
      pagination.total = table.total

      // 为 ArtTableHeader 生成列配置
      tableColumns.value = table.headerKeys.map((key) => ({
        prop: key,
        label: key,
        checked: true
      }))
    }
  } finally {
    loading.value = false
  }
}

function pageChange(page: number) {
  pagination.currentPage = page
  getList()
}
function sizeChange(size: number) {
  pagination.pageSize = size
  getList()
}

function handleInvite() {
  ElMessage.info('邀请功能开发中')
}

function editUser(row: any) {
  console.log('编辑用户:', row)
  router.push(`/users/user-edit/${row.id || row.key}`)
}

function handleActionClick(action: string, row: any) {
  console.log('[DEBUG] 操作点击:', action, row)

  // 根据 actionId 执行相应的操作
  switch (action) {
    case 'edit':
      editUser(row)
      break
    case 'delete':
      deleteUser(row)
      break
    case 'user:freeze':
      freezeUser(row)
      break
    case 'user:unFreeze':
      unfreezeUser(row)
      break
    case 'user:detail':
      viewUserDetail(row)
      break
    case 'user:invite':
      inviteUser(row)
      break
    case 'user:reSendInvitation':
      resendInvitation(row)
      break
    case 'user:resetPassword':
      resetPassword(row)
      break
    default:
      ElMessage.warning(`未知操作: ${action}`)
  }
}

function deleteUser(row: any) {
  console.log('删除用户:', row)
  ElMessage.warning('删除用户功能开发中...')
}

function freezeUser(row: any) {
  console.log('冻结用户:', row)
  ElMessage.warning('冻结用户功能开发中...')
}

function unfreezeUser(row: any) {
  console.log('解冻用户:', row)
  ElMessage.warning('解冻用户功能开发中...')
}

function viewUserDetail(row: any) {
  console.log('查看用户详情:', row)
  ElMessage.warning('查看用户详情功能开发中...')
}

function inviteUser(row: any) {
  console.log('邀请用户:', row)
  ElMessage.warning('邀请用户功能开发中...')
}

function resendInvitation(row: any) {
  console.log('重新发送邀请:', row)
  ElMessage.warning('重新发送邀请功能开发中...')
}

function resetPassword(row: any) {
  console.log('重置密码:', row)
  ElMessage.warning('重置密码功能开发中...')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.customer-users-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.art-table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>

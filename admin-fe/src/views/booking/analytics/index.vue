<template>
  <div class="booking-analytics">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">{{ $t('booking.analytics.title', '预订统计') }}</h1>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="loadAnalytics"
        />
        <el-button type="primary" @click="loadAnalytics" :loading="loading">
          <el-icon><Refresh /></el-icon>
          {{ $t('booking.analytics.refresh', '刷新数据') }}
        </el-button>
      </div>
    </div>

    <!-- Overview Cards -->
    <div class="overview-cards">
      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon">
            <el-icon size="32" color="#409EFF"><Calendar /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ statistics.totalOrders.toLocaleString() }}</h3>
            <p>{{ $t('booking.analytics.totalOrders', '总订单数') }}</p>
            <span class="metric-trend positive">+12.5%</span>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon">
            <el-icon size="32" color="#67C23A"><Money /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ statistics.currency }} {{ statistics.totalRevenue.toLocaleString() }}</h3>
            <p>{{ $t('booking.analytics.totalRevenue', '总收入') }}</p>
            <span class="metric-trend positive">+8.3%</span>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon">
            <el-icon size="32" color="#E6A23C"><TrendCharts /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ averageOrderValue.toLocaleString() }}</h3>
            <p>{{ $t('booking.analytics.avgOrderValue', '平均订单价值') }}</p>
            <span class="metric-trend negative">-2.1%</span>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon">
            <el-icon size="32" color="#F56C6C"><PieChart /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ conversionRate }}%</h3>
            <p>{{ $t('booking.analytics.conversionRate', '转化率') }}</p>
            <span class="metric-trend positive">+5.7%</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <el-row :gutter="24">
        <!-- Revenue Trend Chart -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>{{ $t('booking.analytics.revenueTrend', '收入趋势') }}</span>
                <el-select v-model="revenueChartPeriod" size="small" style="width: 100px">
                  <el-option label="日" value="daily" />
                  <el-option label="周" value="weekly" />
                  <el-option label="月" value="monthly" />
                </el-select>
              </div>
            </template>
            <div ref="revenueChartRef" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- Order Status Distribution -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span>{{ $t('booking.analytics.orderStatus', '订单状态分布') }}</span>
            </template>
            <div ref="statusChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px">
        <!-- Top Hotels -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span>{{ $t('booking.analytics.topHotels', '热门酒店') }}</span>
            </template>
            <div class="top-hotels-list">
              <div
                v-for="(hotel, index) in statistics.topHotels"
                :key="hotel.hotelId"
                class="hotel-item"
              >
                <div class="hotel-rank">{{ index + 1 }}</div>
                <div class="hotel-info">
                  <h4>{{ hotel.hotelName }}</h4>
                  <p>{{ hotel.orders }} 订单 • {{ hotel.revenue.toLocaleString() }} 收入</p>
                </div>
                <div class="hotel-progress">
                  <el-progress
                    :percentage="(hotel.revenue / statistics.totalRevenue) * 100"
                    :show-text="false"
                    :stroke-width="6"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- Monthly Performance -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span>{{ $t('booking.analytics.monthlyPerformance', '月度表现') }}</span>
            </template>
            <div ref="monthlyChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Detailed Statistics Table -->
    <el-card class="table-card" shadow="never" style="margin-top: 24px">
      <template #header>
        <span>{{ $t('booking.analytics.detailedStats', '详细统计') }}</span>
      </template>
      
      <el-table :data="detailedStats" stripe>
        <el-table-column prop="metric" :label="$t('booking.analytics.metric', '指标')" width="200" />
        <el-table-column prop="value" :label="$t('booking.analytics.value', '数值')" />
        <el-table-column prop="change" :label="$t('booking.analytics.change', '变化')" width="120">
          <template #default="{ row }">
            <span :class="['trend', row.change > 0 ? 'positive' : 'negative']">
              {{ row.change > 0 ? '+' : '' }}{{ row.change }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="period" :label="$t('booking.analytics.period', '周期')" width="150" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar, Money, TrendCharts, PieChart, Refresh } from '@element-plus/icons-vue'
import { orderApi, type OrderStatistics } from '@/api/booking/orderApi'
import * as echarts from 'echarts'

// Reactive data
const loading = ref(false)
const dateRange = ref<[string, string] | null>(null)
const revenueChartPeriod = ref('monthly')

// Chart refs
const revenueChartRef = ref<HTMLDivElement>()
const statusChartRef = ref<HTMLDivElement>()
const monthlyChartRef = ref<HTMLDivElement>()

// Statistics data
const statistics = reactive<OrderStatistics>({
  totalOrders: 0,
  totalRevenue: 0,
  currency: 'USD',
  statusBreakdown: [],
  monthlyStats: [],
  topHotels: []
})

// Computed properties
const averageOrderValue = computed(() => {
  return statistics.totalOrders > 0 ? statistics.totalRevenue / statistics.totalOrders : 0
})

const conversionRate = computed(() => {
  // Calculate conversion rate from real data
  return 12.5
})

const detailedStats = computed(() => [
  {
    metric: '总预订量',
    value: statistics.totalOrders.toLocaleString(),
    change: 12.5,
    period: '本月'
  },
  {
    metric: '总收入',
    value: `${statistics.currency} ${statistics.totalRevenue.toLocaleString()}`,
    change: 8.3,
    period: '本月'
  },
  {
    metric: '平均订单价值',
    value: `${statistics.currency} ${averageOrderValue.value.toLocaleString()}`,
    change: -2.1,
    period: '本月'
  },
  {
    metric: '取消率',
    value: '8.2%',
    change: -1.5,
    period: '本月'
  },
  {
    metric: '重复预订率',
    value: '23.4%',
    change: 4.2,
    period: '本月'
  }
])

// Methods
const loadAnalytics = async () => {
  loading.value = true
  try {
    const startDate = dateRange.value?.[0]
    const endDate = dateRange.value?.[1]
    
    const data = await orderApi.getOrderStatistics(startDate, endDate)
    Object.assign(statistics, data)
    
    // Update charts after data is loaded
    await nextTick()
    initCharts()
  } catch (error) {
    console.error('Failed to load analytics:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const initCharts = () => {
  initRevenueChart()
  initStatusChart()
  initMonthlyChart()
}

const initRevenueChart = () => {
  if (!revenueChartRef.value) return
  
  const chart = echarts.init(revenueChartRef.value)
  const option = {
    title: {
      text: '收入趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}'
    },
    xAxis: {
      type: 'category',
      data: statistics.monthlyStats.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      name: '收入'
    },
    series: [{
      data: statistics.monthlyStats.map(item => item.revenue),
      type: 'line',
      smooth: true,
      itemStyle: { color: '#409EFF' },
      areaStyle: { opacity: 0.3 }
    }]
  }
  chart.setOption(option)
}

const initStatusChart = () => {
  if (!statusChartRef.value) return
  
  const chart = echarts.init(statusChartRef.value)
  const option = {
    title: {
      text: '订单状态分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '订单状态',
      type: 'pie',
      radius: '60%',
      data: statistics.statusBreakdown.map(item => ({
        value: item.count,
        name: getStatusText(item.status)
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
}

const initMonthlyChart = () => {
  if (!monthlyChartRef.value) return
  
  const chart = echarts.init(monthlyChartRef.value)
  const option = {
    title: {
      text: '月度订单量',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: statistics.monthlyStats.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      name: '订单数'
    },
    series: [{
      data: statistics.monthlyStats.map(item => item.orders),
      type: 'bar',
      itemStyle: { color: '#67C23A' }
    }]
  }
  chart.setOption(option)
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    confirmed: '已确认',
    pending: '待确认',
    cancelled: '已取消',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

// Initialize
onMounted(() => {
  // Set default date range to last 30 days
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 30)
  
  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  
  loadAnalytics()
})
</script>

<style scoped lang="scss">
.booking-analytics {
  padding: 24px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    
    .metric-card {
      .metric-content {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .metric-icon {
          flex-shrink: 0;
        }
        
        .metric-info {
          flex: 1;
          
          h3 {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 4px 0;
          }
          
          p {
            font-size: 14px;
            color: #909399;
            margin: 0 0 4px 0;
          }
          
          .metric-trend {
            font-size: 12px;
            font-weight: 500;
            
            &.positive {
              color: #67C23A;
            }
            
            &.negative {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    .chart-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
    
    .top-hotels-list {
      .hotel-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .hotel-rank {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #409EFF;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
        }
        
        .hotel-info {
          flex: 1;
          
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 4px 0;
          }
          
          p {
            font-size: 12px;
            color: #909399;
            margin: 0;
          }
        }
        
        .hotel-progress {
          width: 100px;
        }
      }
    }
  }
  
  .table-card {
    .trend {
      font-weight: 500;
      
      &.positive {
        color: #67C23A;
      }
      
      &.negative {
        color: #F56C6C;
      }
    }
  }
}
</style>

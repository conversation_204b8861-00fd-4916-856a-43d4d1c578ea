<template>
  <div class="simple-test">
    <el-card>
      <template #header>
        <h2>简单自动完成测试</h2>
      </template>
      
      <div class="test-section">
        <h3>基础自动完成测试</h3>
        <div style="width: 300px; margin: 20px 0;">
          <el-autocomplete
            v-model="inputValue"
            :fetch-suggestions="querySearch"
            placeholder="输入任何内容测试"
            :debounce="300"
            clearable
            @select="handleSelect"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </div>
        
        <div v-if="selectedItem">
          <h4>选择的项目：</h4>
          <pre>{{ JSON.stringify(selectedItem, null, 2) }}</pre>
        </div>
      </div>
      
      <div class="test-section">
        <h3>城市搜索API测试</h3>
        <div style="width: 300px; margin: 20px 0;">
          <el-input
            v-model="apiTestInput"
            placeholder="输入城市名称"
          />
          <el-button @click="testCityAPI" style="margin-top: 10px;">测试API</el-button>
        </div>
        
        <div v-if="apiResults.length > 0">
          <h4>API返回结果：</h4>
          <pre>{{ JSON.stringify(apiResults, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElCard, ElAutocomplete, ElInput, ElButton, ElMessage } from 'element-plus'
import { hotelSearchApi } from '@/api/booking/searchApi'

// 基础测试数据
const inputValue = ref('')
const selectedItem = ref(null)

// API测试数据
const apiTestInput = ref('')
const apiResults = ref([])

// 基础自动完成测试
const querySearch = (queryString: string, callback: (results: any[]) => void) => {
  console.log('querySearch called with:', queryString)
  
  const results = queryString ? [
    { value: `${queryString} - 选项1` },
    { value: `${queryString} - 选项2` },
    { value: `${queryString} - 选项3` }
  ] : []
  
  console.log('Returning results:', results)
  callback(results)
}

const handleSelect = (item: any) => {
  selectedItem.value = item
  console.log('Selected:', item)
  ElMessage.success(`选择了: ${item.value}`)
}

// API测试
const testCityAPI = async () => {
  if (!apiTestInput.value.trim()) {
    ElMessage.warning('请输入城市名称')
    return
  }

  try {
    console.log('Testing API with:', apiTestInput.value)
    const response = await hotelSearchApi.searchCities({
      keyword: apiTestInput.value.trim()
    })
    
    console.log('API response:', response)
    apiResults.value = response.candidates || []
    ElMessage.success(`API返回 ${apiResults.value.length} 个结果`)
  } catch (error) {
    console.error('API test failed:', error)
    ElMessage.error('API测试失败')
    apiResults.value = []
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>

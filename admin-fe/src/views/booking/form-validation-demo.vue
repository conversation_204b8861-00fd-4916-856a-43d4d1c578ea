<template>
  <div class="form-validation-demo">
    <div class="demo-container">
      <h1>改进后的表单验证演示</h1>
      <p class="demo-description">
        现在每个字段都会实时显示验证状态，用户可以立即看到哪些字段需要填写或修正。
      </p>

      <el-form
        ref="demoFormRef"
        :model="demoForm"
        :rules="demoRules"
        label-width="120px"
        class="demo-form"
      >
        <!-- 基本信息 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span class="section-title">基本信息</span>
          </template>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item 
                label="名字" 
                prop="firstName" 
                :validate-status="getFieldValidationStatus('firstName')" 
                :error="getFieldErrorMessage('firstName')"
              >
                <el-input 
                  v-model="demoForm.firstName" 
                  placeholder="请输入名字"
                  :class="getFieldInputClass('firstName')"
                >
                  <template #suffix>
                    <el-icon v-if="getFieldValidationStatus('firstName') === 'success'" class="validation-icon success">
                      <Check />
                    </el-icon>
                    <el-icon v-else-if="getFieldValidationStatus('firstName') === 'error'" class="validation-icon error">
                      <Close />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item 
                label="姓氏" 
                prop="lastName" 
                :validate-status="getFieldValidationStatus('lastName')" 
                :error="getFieldErrorMessage('lastName')"
              >
                <el-input 
                  v-model="demoForm.lastName" 
                  placeholder="请输入姓氏"
                  :class="getFieldInputClass('lastName')"
                >
                  <template #suffix>
                    <el-icon v-if="getFieldValidationStatus('lastName') === 'success'" class="validation-icon success">
                      <Check />
                    </el-icon>
                    <el-icon v-else-if="getFieldValidationStatus('lastName') === 'error'" class="validation-icon error">
                      <Close />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item 
                label="邮箱" 
                prop="email" 
                :validate-status="getFieldValidationStatus('email')" 
                :error="getFieldErrorMessage('email')"
              >
                <el-input 
                  v-model="demoForm.email" 
                  placeholder="请输入邮箱地址"
                  :class="getFieldInputClass('email')"
                >
                  <template #suffix>
                    <el-icon v-if="getFieldValidationStatus('email') === 'success'" class="validation-icon success">
                      <Check />
                    </el-icon>
                    <el-icon v-else-if="getFieldValidationStatus('email') === 'error'" class="validation-icon error">
                      <Close />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item 
                label="电话" 
                prop="phone" 
                :validate-status="getFieldValidationStatus('phone')" 
                :error="getFieldErrorMessage('phone')"
              >
                <el-input 
                  v-model="demoForm.phone" 
                  placeholder="请输入电话号码"
                  :class="getFieldInputClass('phone')"
                >
                  <template #suffix>
                    <el-icon v-if="getFieldValidationStatus('phone') === 'success'" class="validation-icon success">
                      <Check />
                    </el-icon>
                    <el-icon v-else-if="getFieldValidationStatus('phone') === 'error'" class="validation-icon error">
                      <Close />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item 
            label="国籍" 
            prop="nationality" 
            :validate-status="getFieldValidationStatus('nationality')" 
            :error="getFieldErrorMessage('nationality')"
          >
            <el-select 
              v-model="demoForm.nationality" 
              placeholder="请选择国籍" 
              style="width: 100%"
              :class="getFieldInputClass('nationality')"
            >
              <el-option label="中国" value="CN" />
              <el-option label="美国" value="US" />
              <el-option label="英国" value="UK" />
              <el-option label="日本" value="JP" />
              <el-option label="韩国" value="KR" />
            </el-select>
          </el-form-item>
        </el-card>

        <!-- 支付信息 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span class="section-title">支付信息</span>
          </template>
          
          <el-form-item 
            label="支付方式" 
            prop="paymentMethod" 
            :validate-status="getFieldValidationStatus('paymentMethod')" 
            :error="getFieldErrorMessage('paymentMethod')"
          >
            <div class="payment-method-wrapper">
              <el-radio-group v-model="demoForm.paymentMethod" :class="getFieldInputClass('paymentMethod')">
                <el-radio value="credit_card">信用卡</el-radio>
                <el-radio value="debit_card">借记卡</el-radio>
                <el-radio value="paypal">PayPal</el-radio>
                <el-radio value="alipay">支付宝</el-radio>
              </el-radio-group>
              <el-icon v-if="getFieldValidationStatus('paymentMethod') === 'success'" class="validation-icon success">
                <Check />
              </el-icon>
              <el-icon v-else-if="getFieldValidationStatus('paymentMethod') === 'error'" class="validation-icon error">
                <Close />
              </el-icon>
            </div>
          </el-form-item>
        </el-card>

        <!-- 条款同意 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span class="section-title">条款与条件</span>
          </template>
          
          <div class="terms-content">
            <div class="terms-item" :class="{ 'has-error': getFieldValidationStatus('agreeToTerms') === 'error' }">
              <el-checkbox v-model="demoForm.agreeToTerms" :class="getFieldInputClass('agreeToTerms')">
                我已阅读并同意预订条款和隐私政策
              </el-checkbox>
              <el-icon v-if="getFieldValidationStatus('agreeToTerms') === 'success'" class="validation-icon success">
                <Check />
              </el-icon>
              <el-icon v-else-if="getFieldValidationStatus('agreeToTerms') === 'error'" class="validation-icon error">
                <Close />
              </el-icon>
              <div v-if="getFieldErrorMessage('agreeToTerms')" class="field-error-message">
                {{ getFieldErrorMessage('agreeToTerms') }}
              </div>
            </div>
          </div>
        </el-card>

        <!-- 状态摘要 -->
        <el-card v-if="!isFormComplete" class="form-section validation-summary" shadow="never">
          <div class="summary-content">
            <el-icon class="summary-icon warning">
              <WarningFilled />
            </el-icon>
            <div class="summary-text">
              <h4>请完善以下信息</h4>
              <p>{{ validationMessage }}</p>
            </div>
          </div>
        </el-card>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <el-button size="large" @click="resetForm">重置</el-button>
          <el-button
            type="primary"
            size="large"
            :disabled="!isFormComplete"
            @click="submitForm"
          >
            {{ isFormComplete ? '提交表单' : '请完善信息' }}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Check, Close, WarningFilled } from '@element-plus/icons-vue'

// Form ref
const demoFormRef = ref<FormInstance>()

// Demo form data
const demoForm = reactive({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  nationality: '',
  paymentMethod: '',
  agreeToTerms: false
})

// Form validation rules
const demoRules = {
  firstName: [
    { required: true, message: '请输入名字', trigger: 'blur' }
  ],
  lastName: [
    { required: true, message: '请输入姓氏', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' }
  ],
  nationality: [
    { required: true, message: '请选择国籍', trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ]
}

// 检查表单是否完整填写
const isFormComplete = computed(() => {
  return demoForm.firstName &&
         demoForm.lastName &&
         demoForm.email &&
         demoForm.phone &&
         demoForm.nationality &&
         demoForm.paymentMethod &&
         demoForm.agreeToTerms
})

// 验证消息
const validationMessage = computed(() => {
  const missing = []

  if (!demoForm.firstName) missing.push('名字')
  if (!demoForm.lastName) missing.push('姓氏')
  if (!demoForm.email) missing.push('邮箱')
  if (!demoForm.phone) missing.push('电话')
  if (!demoForm.nationality) missing.push('国籍')
  if (!demoForm.paymentMethod) missing.push('支付方式')
  if (!demoForm.agreeToTerms) missing.push('同意条款')

  if (missing.length > 0) {
    return `请填写：${missing.join('、')}`
  }

  return '所有信息已完善，可以提交表单'
})

// 字段验证状态
const getFieldValidationStatus = (fieldName: string) => {
  switch (fieldName) {
    case 'firstName':
      if (!demoForm.firstName) return 'error'
      return demoForm.firstName.trim() ? 'success' : 'error'

    case 'lastName':
      if (!demoForm.lastName) return 'error'
      return demoForm.lastName.trim() ? 'success' : 'error'

    case 'email':
      if (!demoForm.email) return 'error'
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(demoForm.email) ? 'success' : 'error'

    case 'phone':
      if (!demoForm.phone) return 'error'
      return demoForm.phone.trim() ? 'success' : 'error'

    case 'nationality':
      return demoForm.nationality ? 'success' : 'error'

    case 'paymentMethod':
      return demoForm.paymentMethod ? 'success' : 'error'

    case 'agreeToTerms':
      return demoForm.agreeToTerms ? 'success' : 'error'

    default:
      return ''
  }
}

// 字段错误信息
const getFieldErrorMessage = (fieldName: string) => {
  switch (fieldName) {
    case 'firstName':
      if (!demoForm.firstName) return '请输入名字'
      if (!demoForm.firstName.trim()) return '名字不能为空'
      return ''

    case 'lastName':
      if (!demoForm.lastName) return '请输入姓氏'
      if (!demoForm.lastName.trim()) return '姓氏不能为空'
      return ''

    case 'email':
      if (!demoForm.email) return '请输入邮箱地址'
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(demoForm.email)) return '请输入正确的邮箱地址'
      return ''

    case 'phone':
      if (!demoForm.phone) return '请输入电话号码'
      if (!demoForm.phone.trim()) return '电话号码不能为空'
      return ''

    case 'nationality':
      return demoForm.nationality ? '' : '请选择国籍'

    case 'paymentMethod':
      return demoForm.paymentMethod ? '' : '请选择支付方式'

    case 'agreeToTerms':
      return demoForm.agreeToTerms ? '' : '请同意预订条款和隐私政策'

    default:
      return ''
  }
}

// 字段输入框样式
const getFieldInputClass = (fieldName: string) => {
  const status = getFieldValidationStatus(fieldName)
  return {
    'field-success': status === 'success',
    'field-error': status === 'error'
  }
}

// 提交表单
const submitForm = async () => {
  if (!demoFormRef.value) return

  try {
    const valid = await demoFormRef.value.validate()
    if (!valid) return

    if (!demoForm.agreeToTerms) {
      ElMessage.error('请同意预订条款和隐私政策')
      return
    }

    ElMessage.success('表单提交成功！')
  } catch (error) {
    console.error('Form validation failed:', error)
    ElMessage.error('表单验证失败，请检查输入信息')
  }
}

// 重置表单
const resetForm = () => {
  if (demoFormRef.value) {
    demoFormRef.value.resetFields()
  }
  Object.assign(demoForm, {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    nationality: '',
    paymentMethod: '',
    agreeToTerms: false
  })
}
</script>

<style scoped lang="scss">
.form-validation-demo {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  .demo-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 16px 0;
      text-align: center;
    }

    .demo-description {
      font-size: 16px;
      color: #606266;
      text-align: center;
      margin: 0 0 32px 0;
      line-height: 1.6;
    }

    .demo-form {
      .form-section {
        margin-bottom: 24px;
        border: 1px solid #ebeef5;
        border-radius: 8px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .terms-content {
        .terms-item {
          position: relative;
          display: flex;
          align-items: flex-start;
          gap: 8px;

          &.has-error {
            .el-checkbox {
              color: #f56c6c;
            }
          }

          .field-error-message {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 4px;
            line-height: 1.4;
          }
        }
      }

      .payment-method-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .form-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-top: 32px;
      }

      // 验证状态样式
      .validation-icon {
        font-size: 16px;

        &.success {
          color: #67c23a;
        }

        &.error {
          color: #f56c6c;
        }

        &.warning {
          color: #e6a23c;
        }
      }

      .field-success {
        .el-input__wrapper {
          border-color: #67c23a;
          box-shadow: 0 0 0 1px #67c23a inset;
        }
      }

      .field-error {
        .el-input__wrapper {
          border-color: #f56c6c;
          box-shadow: 0 0 0 1px #f56c6c inset;
        }
      }

      .validation-summary {
        .summary-content {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 16px;
          background: #fef0f0;
          border: 1px solid #fbc4c4;
          border-radius: 8px;

          .summary-icon {
            font-size: 20px;
            margin-top: 2px;

            &.warning {
              color: #e6a23c;
            }
          }

          .summary-text {
            flex: 1;

            h4 {
              margin: 0 0 4px 0;
              font-size: 14px;
              font-weight: 600;
              color: #f56c6c;
            }

            p {
              margin: 0;
              font-size: 13px;
              color: #606266;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}
</style>

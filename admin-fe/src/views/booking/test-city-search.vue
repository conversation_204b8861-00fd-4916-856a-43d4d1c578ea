<template>
  <div class="test-city-search">
    <el-card>
      <template #header>
        <h2>城市搜索测试页面</h2>
      </template>
      
      <div class="test-section">
        <h3>城市搜索组件测试</h3>
        <div style="width: 300px; margin: 20px 0;">
          <CitySearchInput
            v-model="selectedCityName"
            placeholder="输入城市名称进行搜索"
            @select="handleCitySelect"
            @clear="handleCityClear"
            ref="citySearchRef"
          />
        </div>
        
        <div v-if="selectedCityInfo" class="selected-city-info">
          <h4>选择的城市信息：</h4>
          <pre>{{ JSON.stringify(selectedCityInfo, null, 2) }}</pre>
        </div>
        
        <div class="test-buttons">
          <el-button @click="testSearch">测试搜索</el-button>
          <el-button @click="clearSelection">清空选择</el-button>
        </div>
      </div>
      
      <div class="test-section" v-if="searchResults.length > 0">
        <h3>搜索结果：</h3>
        <div class="search-results">
          <div 
            v-for="(item, index) in searchResults" 
            :key="index"
            class="result-item"
            @click="selectResult(item)"
          >
            <div class="result-type">{{ item.type }}</div>
            <div class="result-name">{{ item.region?.name || item.hotel?.name || 'Unknown' }}</div>
            <div class="result-full-name">{{ item.region?.nameFull || 'N/A' }}</div>
            <div class="result-id">ID: {{ item.region?.id || item.hotel?.id || 'N/A' }}</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElCard, ElButton, ElMessage } from 'element-plus'
import CitySearchInput from '@/components/booking/CitySearchInput.vue'
import { hotelSearchApi } from '@/api/booking/searchApi'
import type { SearchItem } from '@/api/booking/searchApi'

// 响应式数据
const selectedCityName = ref('')
const selectedCityInfo = ref<any>(null)
const searchResults = ref<SearchItem[]>([])
const citySearchRef = ref<InstanceType<typeof CitySearchInput>>()

// 处理城市选择
const handleCitySelect = (city: any) => {
  selectedCityInfo.value = city
  console.log('Selected city:', city)
  ElMessage.success(`选择了城市: ${city.name}`)
}

// 处理城市清空
const handleCityClear = () => {
  selectedCityInfo.value = null
  searchResults.value = []
  console.log('City cleared')
  ElMessage.info('已清空城市选择')
}

// 测试搜索功能
const testSearch = async () => {
  if (!selectedCityName.value.trim()) {
    ElMessage.warning('请输入城市名称')
    return
  }

  try {
    console.log('Testing search with keyword:', selectedCityName.value.trim())
    const response = await hotelSearchApi.searchCities({
      keyword: selectedCityName.value.trim()
    })

    console.log('Raw API response:', response)
    searchResults.value = response.candidates || []
    ElMessage.success(`找到 ${searchResults.value.length} 个结果`)
    console.log('Processed search results:', searchResults.value)
  } catch (error) {
    console.error('Search failed:', error)
    ElMessage.error('搜索失败')
  }
}

// 选择搜索结果
const selectResult = (item: SearchItem) => {
  if (item.region) {
    const cityOption = {
      value: item.region.name,
      name: item.region.name,
      nameFull: item.region.nameFull || item.region.name,
      regionId: item.region.id,
      countryCode: item.region.countryCode,
      type: item.region.type,
      region: item.region
    }
    
    citySearchRef.value?.setCity(cityOption)
    handleCitySelect(cityOption)
  }
}

// 清空选择
const clearSelection = () => {
  citySearchRef.value?.clear()
  selectedCityName.value = ''
  selectedCityInfo.value = null
  searchResults.value = []
}
</script>

<style scoped>
.test-city-search {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.selected-city-info {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.selected-city-info pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
}

.test-buttons {
  margin-top: 20px;
}

.test-buttons .el-button {
  margin-right: 10px;
}

.search-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.result-item {
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.result-type {
  font-size: 12px;
  color: #909399;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.result-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.result-full-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.result-id {
  font-size: 12px;
  color: #909399;
}
</style>

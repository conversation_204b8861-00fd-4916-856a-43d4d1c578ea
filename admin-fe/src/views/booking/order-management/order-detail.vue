<template>
  <div class="order-detail" v-loading="loading">
    <div class="detail-container">
      <!-- Header -->
      <div class="detail-header">
        <h1>订单详情</h1>
        <div class="header-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回订单列表
          </el-button>
          <el-button 
            v-if="canCancel" 
            type="danger" 
            @click="handleCancelOrder"
            :loading="cancelling"
          >
            取消订单
          </el-button>
        </div>
      </div>

      <el-row :gutter="24" v-if="orderDetail">
        <!-- Order Summary -->
        <el-col :span="16">
          <el-card class="detail-section" shadow="never">
            <template #header>
              <span class="section-title">订单摘要</span>
            </template>
            
            <div class="order-summary">
              <div class="summary-item">
                <span class="label">订单号:</span>
                <span class="value">{{ orderDetail.summary.id }}</span>
              </div>
              <div class="summary-item">
                <span class="label">订单状态:</span>
                <el-tag :type="getStatusType(orderDetail.summary.status)">
                  {{ getStatusText(orderDetail.summary.status) }}
                </el-tag>
              </div>
              <div class="summary-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDateTime(orderDetail.summary.createTime) }}</span>
              </div>
              <div class="summary-item" v-if="orderDetail.summary.confirmNumber">
                <span class="label">确认号:</span>
                <span class="value">{{ orderDetail.summary.confirmNumber }}</span>
              </div>
            </div>
          </el-card>

          <!-- Hotel Information -->
          <el-card class="detail-section" shadow="never" v-if="orderDetail.subOrders?.length">
            <template #header>
              <span class="section-title">酒店信息</span>
            </template>
            
            <div class="hotel-info" v-for="subOrder in orderDetail.subOrders" :key="subOrder.hotel?.hotelId">
              <h4>{{ subOrder.hotel?.hotelName || '未知酒店' }}</h4>
              <p v-if="subOrder.hotel?.address">{{ subOrder.hotel.address }}</p>
            </div>
          </el-card>

          <!-- Booking Details -->
          <el-card class="detail-section" shadow="never" v-if="orderDetail.subOrders?.length">
            <template #header>
              <span class="section-title">预订详情</span>
            </template>
            
            <div class="booking-details" v-for="subOrder in orderDetail.subOrders" :key="subOrder.hotel?.hotelId">
              <el-table 
                :data="subOrder.booking?.rows || []" 
                style="width: 100%"
                :show-header="false"
              >
                <el-table-column prop="raw.id" label="订单ID" />
                <el-table-column prop="raw.status" label="状态">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.raw.status)">
                      {{ getStatusText(row.raw.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="raw.createTime" label="创建时间">
                  <template #default="{ row }">
                    {{ formatDateTime(row.raw.createTime) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>

        <!-- Account Information -->
        <el-col :span="8">
          <el-card class="detail-section" shadow="never">
            <template #header>
              <span class="section-title">账户信息</span>
            </template>
            
            <div class="account-info" v-if="orderDetail.account?.orderAccount">
              <div class="account-item">
                <span class="label">销售币种:</span>
                <span class="value">{{ orderDetail.account.orderAccount.salesCurrency }}</span>
              </div>
              <div class="account-item">
                <span class="label">销售金额:</span>
                <span class="value">{{ formatAmount(orderDetail.account.orderAccount.salesAmount) }}</span>
              </div>
              <div class="account-item">
                <span class="label">供应币种:</span>
                <span class="value">{{ orderDetail.account.orderAccount.supplyCurrency }}</span>
              </div>
              <div class="account-item">
                <span class="label">供应金额:</span>
                <span class="value">{{ formatAmount(orderDetail.account.orderAccount.supplyAmount) }}</span>
              </div>
              <div class="account-item">
                <span class="label">平台收益:</span>
                <span class="value">{{ formatAmount(orderDetail.account.orderAccount.platformRevenueAmountUsd) }} USD</span>
              </div>
              <div class="account-item">
                <span class="label">租户收益:</span>
                <span class="value">{{ formatAmount(orderDetail.account.orderAccount.tenantRevenueAmountUsd) }} USD</span>
              </div>
            </div>
          </el-card>

          <!-- Revenue Information -->
          <el-card class="detail-section" shadow="never" v-if="orderDetail.subOrders?.length">
            <template #header>
              <span class="section-title">收益信息</span>
            </template>
            
            <div class="revenue-info" v-for="subOrder in orderDetail.subOrders" :key="subOrder.hotel?.hotelId">
              <el-table 
                :data="subOrder.gain?.rows || []" 
                style="width: 100%"
                :show-header="false"
              >
                <el-table-column label="平台收益">
                  <template #default="{ row }">
                    {{ formatAmount(row.raw.platformRevenue) }} USD
                  </template>
                </el-table-column>
                <el-table-column label="租户收益">
                  <template #default="{ row }">
                    {{ formatAmount(row.raw.tenantRevenue) }} USD
                  </template>
                </el-table-column>
                <el-table-column label="客户收益">
                  <template #default="{ row }">
                    {{ formatAmount(row.raw.customerRevenue) }} USD
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { orderApi, type OrderDetail } from '@/api/booking/orderApi'
import { bookingApi } from '@/api/booking/bookingApi'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// Reactive data
const loading = ref(false)
const cancelling = ref(false)
const orderDetail = ref<OrderDetail | null>(null)

// Computed properties
const canCancel = computed(() => {
  if (!orderDetail.value?.summary) return false
  // Can cancel if status is Created or Submitted
  return orderDetail.value.summary.status === 0 || orderDetail.value.summary.status === 1
})

// Methods
const loadOrderDetail = async () => {
  const orderId = route.params.orderId as string
  if (!orderId) {
    ElMessage.error(t('booking.order.orderIdRequired', '订单ID是必需的'))
    goBack()
    return
  }

  loading.value = true
  try {
    const response = await orderApi.getOrderDetail(orderId)
    orderDetail.value = response
  } catch (error) {
    console.error('Failed to load order detail:', error)
    ElMessage.error(t('booking.order.loadDetailFailed', '加载订单详情失败'))
  } finally {
    loading.value = false
  }
}

const handleCancelOrder = async () => {
  if (!orderDetail.value?.summary?.id) return

  try {
    await ElMessageBox.confirm(
      '确定要取消这个订单吗？取消后无法恢复。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cancelling.value = true
    await bookingApi.cancelBooking({ orderId: orderDetail.value.summary.id })
    
    ElMessage.success('订单取消成功')
    await loadOrderDetail() // Reload to get updated status
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Cancel order failed:', error)
      ElMessage.error('取消订单失败')
    }
  } finally {
    cancelling.value = false
  }
}

const goBack = () => {
  router.push('/booking/order-management')
}

const getStatusType = (status: number) => {
  switch (status) {
    case 1: return 'info'     // OrderStateCreated
    case 2: return 'primary'  // OrderStatePaid
    case 3: return 'warning'  // OrderStateNeedSupplierConfirmed
    case 4: return 'success'  // OrderStateConfirmed
    case 5: return 'success'  // OrderStateCompleted
    case 6: return 'danger'   // OrderStateCancelled
    case 7: return 'warning'  // OrderStateNeedCancel
    case 8: return 'warning'  // OrderStateNeedRefund
    default: return 'info'
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '已创建'
    case 2: return '已支付'
    case 3: return '待确认'
    case 4: return '已确认'
    case 5: return '已完成'
    case 6: return '已取消'
    case 7: return '取消中'
    case 8: return '退款中'
    default: return '未知状态'
  }
}

const formatDateTime = (dateTime: string | Date) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN')
}

const formatAmount = (amount: number) => {
  if (amount === undefined || amount === null) return '0'
  return (amount / 100).toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

// Initialize
onMounted(() => {
  loadOrderDetail()
})
</script>

<style scoped lang="scss">
.order-detail {
  padding: 24px;
  
  .detail-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .detail-section {
    margin-bottom: 24px;
    
    .section-title {
      font-weight: 600;
      color: #303133;
    }
  }
  
  .order-summary {
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .label {
        color: #606266;
        font-weight: 500;
      }
      
      .value {
        color: #303133;
        font-weight: 600;
      }
    }
  }
  
  .hotel-info {
    h4 {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #606266;
      margin: 0;
    }
  }
  
  .account-info {
    .account-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      
      .label {
        color: #606266;
      }
      
      .value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}
</style>

<template>
  <div class="create-order">
    <el-page-header @back="goBack" title="订单管理">
      <template #content>
        <span class="page-title">创建订单</span>
      </template>
    </el-page-header>

    <el-card class="form-card" shadow="never">
      <el-form
        ref="formRef"
        :model="orderForm"
        :rules="formRules"
        label-width="120px"
        size="default"
      >
        <!-- 酒店搜索选择 -->
        <el-card class="section-card" shadow="never">
          <template #header>
            <span>选择酒店</span>
          </template>

          <el-form-item label="搜索酒店" prop="hotelSearch">
            <el-input
              v-model="hotelSearchKeyword"
              placeholder="输入酒店名称或城市搜索"
              @input="searchHotels"
              clearable
            >
              <template #append>
                <el-button @click="searchHotels" :icon="Search">搜索</el-button>
              </template>
            </el-input>
          </el-form-item>

          <!-- 酒店搜索结果 -->
          <div v-if="searchResults.length > 0" class="hotel-results">
            <el-table
              :data="searchResults"
              @row-click="selectHotel"
              highlight-current-row
              max-height="300"
            >
              <el-table-column prop="name" label="酒店名称" min-width="200" />
              <el-table-column prop="address" label="地址" min-width="250" />
              <el-table-column prop="rating" label="评分" width="80">
                <template #default="{ row }">
                  <el-rate :model-value="row.rating" disabled show-score />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button size="small" type="primary" @click="selectHotel(row)">
                    选择
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 已选择的酒店 -->
          <div v-if="selectedHotel" class="selected-hotel">
            <el-alert
              :title="`已选择：${selectedHotel.name}`"
              type="success"
              show-icon
              :closable="false"
            >
              <template #default>
                <div class="hotel-info">
                  <p><strong>地址：</strong>{{ selectedHotel.address }}</p>
                  <p><strong>评分：</strong>{{ selectedHotel.rating }}/5</p>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>

        <!-- 房型选择 -->
        <el-card v-if="selectedHotel" class="section-card" shadow="never">
          <template #header>
            <span>选择房型</span>
          </template>

          <el-form-item label="入住日期" prop="checkIn" required>
            <el-date-picker
              v-model="orderForm.checkIn"
              type="date"
              placeholder="选择入住日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledCheckInDate"
              @change="loadRoomTypes"
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item label="离店日期" prop="checkOut" required>
            <el-date-picker
              v-model="orderForm.checkOut"
              type="date"
              placeholder="选择离店日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledCheckOutDate"
              @change="loadRoomTypes"
              style="width: 200px"
            />
          </el-form-item>

          <!-- 房型列表 -->
          <div v-if="roomTypes.length > 0" class="room-types">
            <el-table
              :data="roomTypes"
              @row-click="selectRoomType"
              highlight-current-row
            >
              <el-table-column prop="name" label="房型名称" min-width="150" />
              <el-table-column prop="description" label="描述" min-width="200" />
              <el-table-column prop="maxOccupancy" label="最大入住人数" width="120" />
              <el-table-column prop="price" label="价格/晚" width="120">
                <template #default="{ row }">
                  <span class="price">¥{{ row.price }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button size="small" type="primary" @click="selectRoomType(row)">
                    选择
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 已选择的房型 -->
          <div v-if="selectedRoomType" class="selected-room">
            <el-alert
              :title="`已选择：${selectedRoomType.name}`"
              type="success"
              show-icon
              :closable="false"
            >
              <template #default>
                <div class="room-info">
                  <p><strong>价格：</strong>¥{{ selectedRoomType.price }}/晚</p>
                  <p><strong>入住人数：</strong>最多{{ selectedRoomType.maxOccupancy }}人</p>
                  <p><strong>晚数：</strong>{{ nights }}晚</p>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>

        <!-- 预订信息 -->
        <el-card v-if="selectedRoomType" class="section-card" shadow="never">
          <template #header>
            <span>预订信息</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="房间数" prop="rooms" required>
                <el-input-number
                  v-model="orderForm.rooms"
                  :min="1"
                  :max="10"
                  @change="calculateTotal"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="成人数" prop="adults" required>
                <el-input-number
                  v-model="orderForm.adults"
                  :min="1"
                  :max="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="儿童数" prop="children">
                <el-input-number
                  v-model="orderForm.children"
                  :min="0"
                  :max="10"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 客人信息 -->
        <el-card v-if="selectedRoomType" class="section-card" shadow="never">
          <template #header>
            <div class="section-header">
              <span>客人信息</span>
              <el-button size="small" @click="addGuest">添加客人</el-button>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="主客人姓名" prop="guestName" required>
                <el-input v-model="orderForm.guestName" placeholder="请输入主客人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="guestPhone">
                <el-input v-model="orderForm.guestPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮箱" prop="guestEmail">
                <el-input v-model="orderForm.guestEmail" placeholder="请输入邮箱" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 客人列表 -->
          <div v-if="orderForm.guests.length > 0" class="guests-section">
            <h4>客人列表</h4>
            <el-table :data="orderForm.guests" size="small">
              <el-table-column label="姓名" width="150">
                <template #default="{ row, $index }">
                  <el-input v-model="row.firstName" placeholder="名" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="姓氏" width="150">
                <template #default="{ row, $index }">
                  <el-input v-model="row.lastName" placeholder="姓" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="国籍" width="120">
                <template #default="{ row, $index }">
                  <el-select v-model="row.nationalityCode" size="small">
                    <el-option label="中国" value="CN" />
                    <el-option label="美国" value="US" />
                    <el-option label="英国" value="GB" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="房间号" width="100">
                <template #default="{ row, $index }">
                  <el-input-number v-model="row.roomIndex" :min="1" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="{ row, $index }">
                  <el-button size="small" type="danger" @click="removeGuest($index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>

        <!-- 费用信息 -->
        <el-card v-if="selectedRoomType" class="section-card" shadow="never">
          <template #header>
            <span>费用信息</span>
          </template>

          <div class="price-summary">
            <div class="price-item">
              <span>房费小计：</span>
              <span class="amount">¥{{ roomAmount.toFixed(2) }}</span>
            </div>
            <div class="price-item">
              <span>税费：</span>
              <span class="amount">¥{{ taxAmount.toFixed(2) }}</span>
            </div>
            <div class="price-item">
              <span>服务费：</span>
              <span class="amount">¥{{ serviceAmount.toFixed(2) }}</span>
            </div>
            <el-divider />
            <div class="price-item total">
              <span>订单总额：</span>
              <span class="amount">¥{{ totalAmount.toFixed(2) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 备注 -->
        <el-card v-if="selectedRoomType" class="section-card" shadow="never">
          <template #header>
            <span>备注信息</span>
          </template>

          <el-form-item label="特殊要求" prop="remarkToHotel">
            <el-input
              v-model="orderForm.remarkToHotel"
              type="textarea"
              :rows="3"
              placeholder="请输入特殊要求或备注信息"
            />
          </el-form-item>
        </el-card>

        <!-- 预订状态检查 -->
        <el-card v-if="selectedHotel" class="section-card" shadow="never">
          <template #header>
            <span>预订状态检查</span>
          </template>
          <div class="validation-status">
            <div class="status-item" :class="{ 'completed': selectedHotel }">
              <el-icon><Check v-if="selectedHotel" /><Close v-else /></el-icon>
              <span>选择酒店</span>
            </div>
            <div class="status-item" :class="{ 'completed': orderForm.checkIn }">
              <el-icon><Check v-if="orderForm.checkIn" /><Close v-else /></el-icon>
              <span>入住日期</span>
            </div>
            <div class="status-item" :class="{ 'completed': orderForm.checkOut }">
              <el-icon><Check v-if="orderForm.checkOut" /><Close v-else /></el-icon>
              <span>离店日期</span>
            </div>
            <div class="status-item" :class="{ 'completed': selectedRoomType }">
              <el-icon><Check v-if="selectedRoomType" /><Close v-else /></el-icon>
              <span>选择房型</span>
            </div>
            <div class="status-item" :class="{ 'completed': orderForm.guestName }">
              <el-icon><Check v-if="orderForm.guestName" /><Close v-else /></el-icon>
              <span>主客人姓名</span>
            </div>
            <div class="status-item" :class="{ 'completed': orderForm.rooms >= 1 }">
              <el-icon><Check v-if="orderForm.rooms >= 1" /><Close v-else /></el-icon>
              <span>房间数量</span>
            </div>
            <div class="status-item" :class="{ 'completed': orderForm.adults >= 1 }">
              <el-icon><Check v-if="orderForm.adults >= 1" /><Close v-else /></el-icon>
              <span>成人数量</span>
            </div>
          </div>
        </el-card>

        <!-- 提交按钮 -->
        <div v-if="selectedHotel" class="submit-section">
          <el-button size="large" @click="goBack">取消</el-button>
          <el-button
            type="primary"
            size="large"
            @click="submitOrder"
            :loading="submitting"
            :disabled="!isFormValid"
          >
            {{ submitButtonText }}
          </el-button>

          <!-- 验证提示 -->
          <div v-if="!isFormValid" class="validation-hint">
            <p style="color: #f56c6c; margin-top: 10px; font-size: 14px;">
              {{ validationMessage }}
            </p>
          </div>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Search, Check, Close } from '@element-plus/icons-vue'
import { hotelSearchApi, type Hotel } from '@/api/booking/searchApi'
import { bookingApi, type BookingRequest, type Guest } from '@/api/booking/bookingApi'

const router = useRouter()

// Reactive data
const formRef = ref<FormInstance>()
const submitting = ref(false)
const hotelSearchKeyword = ref('')
const searchResults = ref<Hotel[]>([])
const selectedHotel = ref<Hotel | null>(null)
const roomTypes = ref<any[]>([])
const selectedRoomType = ref<any>(null)

const orderForm = reactive({
  checkIn: '',
  checkOut: '',
  rooms: 1,
  adults: 1,
  children: 0,
  guestName: '',
  guestPhone: '',
  guestEmail: '',
  guests: [] as Guest[],
  remarkToHotel: ''
})

// Form validation rules
const formRules: FormRules = {
  checkIn: [{ required: true, message: '请选择入住日期', trigger: 'change' }],
  checkOut: [{ required: true, message: '请选择离店日期', trigger: 'change' }],
  rooms: [{ required: true, message: '请输入房间数', trigger: 'blur' }],
  adults: [{ required: true, message: '请输入成人数', trigger: 'blur' }],
  guestName: [{ required: true, message: '请输入主客人姓名', trigger: 'blur' }],
  guestPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  guestEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// Computed properties
const nights = computed(() => {
  if (!orderForm.checkIn || !orderForm.checkOut) return 0
  const checkIn = new Date(orderForm.checkIn)
  const checkOut = new Date(orderForm.checkOut)
  const diffTime = checkOut.getTime() - checkIn.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

const roomAmount = computed(() => {
  if (!selectedRoomType.value || nights.value <= 0) return 0
  return selectedRoomType.value.price * nights.value * orderForm.rooms
})

const taxAmount = computed(() => {
  return roomAmount.value * 0.1 // 10% 税费
})

const serviceAmount = computed(() => {
  return roomAmount.value * 0.05 // 5% 服务费
})

const totalAmount = computed(() => {
  return roomAmount.value + taxAmount.value + serviceAmount.value
})

// 表单验证状态
const isFormValid = computed(() => {
  return selectedHotel.value &&
         selectedRoomType.value &&
         orderForm.checkIn &&
         orderForm.checkOut &&
         orderForm.guestName &&
         orderForm.guestName.trim() !== '' &&
         orderForm.rooms >= 1 &&
         orderForm.adults >= 1 &&
         (!orderForm.guestPhone || /^1[3-9]\d{9}$/.test(orderForm.guestPhone)) &&
         (!orderForm.guestEmail || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orderForm.guestEmail))
})

// 按钮文本
const submitButtonText = computed(() => {
  if (submitting.value) return '提交中...'
  if (!isFormValid.value) return '请完善信息'
  return '确认预订'
})

// 验证提示信息
const validationMessage = computed(() => {
  const missing = []

  if (!selectedHotel.value) missing.push('选择酒店')
  if (!selectedRoomType.value) missing.push('选择房型')
  if (!orderForm.checkIn) missing.push('入住日期')
  if (!orderForm.checkOut) missing.push('离店日期')
  if (!orderForm.guestName || orderForm.guestName.trim() === '') missing.push('主客人姓名')
  if (!orderForm.rooms || orderForm.rooms < 1) missing.push('房间数量')
  if (!orderForm.adults || orderForm.adults < 1) missing.push('成人数量')

  if (orderForm.guestPhone && !/^1[3-9]\d{9}$/.test(orderForm.guestPhone)) {
    missing.push('正确的手机号格式')
  }

  if (orderForm.guestEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orderForm.guestEmail)) {
    missing.push('正确的邮箱格式')
  }

  if (missing.length === 0) return ''
  if (missing.length === 1) return `请${missing[0]}`
  return `请完善：${missing.join('、')}`
})

// Date validation
const disabledCheckInDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const disabledCheckOutDate = (time: Date) => {
  if (!orderForm.checkIn) return time.getTime() < Date.now()
  const checkInTime = new Date(orderForm.checkIn).getTime()
  return time.getTime() <= checkInTime
}

// Methods
const searchHotels = async () => {
  if (!hotelSearchKeyword.value.trim()) return

  try {
    const response = await hotelSearchApi.searchHotels({
      regionName: hotelSearchKeyword.value,
      checkIn: orderForm.checkIn || new Date().toISOString().split('T')[0],
      checkOut: orderForm.checkOut || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      adultCount: orderForm.adults,
      childrenCount: orderForm.children,
      roomCount: orderForm.rooms
    })

    searchResults.value = response.list || []
  } catch (error) {
    console.error('Search hotels failed:', error)
    ElMessage.error('搜索酒店失败')
  }
}

const selectHotel = (hotel: Hotel) => {
  selectedHotel.value = hotel
  selectedRoomType.value = null
  roomTypes.value = []
  
  if (orderForm.checkIn && orderForm.checkOut) {
    loadRoomTypes()
  }
}

const loadRoomTypes = async () => {
  if (!selectedHotel.value || !orderForm.checkIn || !orderForm.checkOut) return

  try {
    // 使用酒店房型API获取房型信息
    const response = await hotelSearchApi.getHotelRates(selectedHotel.value.id, {
      checkIn: orderForm.checkIn,
      checkOut: orderForm.checkOut,
      adultCount: orderForm.adults,
      childrenCount: orderForm.children,
      roomCount: orderForm.rooms,
      currency: 'USD',
      language: 'en'
    })

    if (response.rooms && response.rooms.length > 0) {
      roomTypes.value = response.rooms.map(room => ({
        id: room.roomTypeId,
        name: room.roomName?.en || room.roomName || '标准房',
        description: room.roomDesc?.en || room.roomDesc || '',
        maxOccupancy: room.occupancy || 2,
        price: room.rates?.[0]?.rate?.amount || 0,
        ratePkgId: room.rates?.[0]?.ratePkgId
      }))
      console.log('Loaded room types:', roomTypes.value)
    } else {
      roomTypes.value = []
      ElMessage.warning('该酒店暂无可用房型')
    }
  } catch (error) {
    console.error('Load room types failed:', error)
    ElMessage.error('加载房型失败')
    roomTypes.value = []
  }
}

const selectRoomType = (roomType: any) => {
  console.log('Selecting room type:', roomType)
  selectedRoomType.value = roomType
  calculateTotal()
}

const calculateTotal = () => {
  // Trigger computed properties recalculation
}

const addGuest = () => {
  orderForm.guests.push({
    firstName: '',
    lastName: '',
    nationalityCode: 'CN',
    roomIndex: orderForm.guests.length + 1
  })
}

const removeGuest = (index: number) => {
  orderForm.guests.splice(index, 1)
  // 重新分配房间号
  orderForm.guests.forEach((guest, idx) => {
    guest.roomIndex = idx + 1
  })
}

const submitOrder = async () => {
  if (!formRef.value) return

  // 详细验证并提供具体的错误信息
  const validationErrors = []

  // 检查酒店选择
  if (!selectedHotel.value) {
    validationErrors.push('请选择酒店')
  }

  // 检查房型选择
  if (!selectedRoomType.value) {
    validationErrors.push('请选择房型')
  }

  // 检查必填字段
  if (!orderForm.checkIn) {
    validationErrors.push('请选择入住日期')
  }

  if (!orderForm.checkOut) {
    validationErrors.push('请选择离店日期')
  }

  if (!orderForm.guestName || orderForm.guestName.trim() === '') {
    validationErrors.push('请输入主客人姓名')
  }

  if (!orderForm.rooms || orderForm.rooms < 1) {
    validationErrors.push('请输入有效的房间数')
  }

  if (!orderForm.adults || orderForm.adults < 1) {
    validationErrors.push('请输入有效的成人数')
  }

  // 验证手机号格式（如果填写了）
  if (orderForm.guestPhone && !/^1[3-9]\d{9}$/.test(orderForm.guestPhone)) {
    validationErrors.push('请输入正确的手机号码格式')
  }

  // 验证邮箱格式（如果填写了）
  if (orderForm.guestEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orderForm.guestEmail)) {
    validationErrors.push('请输入正确的邮箱地址格式')
  }

  // 如果有验证错误，显示详细信息
  if (validationErrors.length > 0) {
    ElMessage.error({
      message: `请完善以下信息：\n${validationErrors.join('\n')}`,
      duration: 5000,
      showClose: true
    })
    return
  }

  try {
    // 执行表单验证
    await formRef.value.validate()

    submitting.value = true

    const bookingRequest: BookingRequest = {
      ratePkgId: selectedRoomType.value.ratePkgId,
      guests: [
        {
          firstName: orderForm.guestName.split(' ')[0] || orderForm.guestName,
          lastName: orderForm.guestName.split(' ')[1] || '',
          nationalityCode: 'CN',
          roomIndex: 1
        },
        ...orderForm.guests
      ],
      booker: {
        firstName: orderForm.guestName.split(' ')[0] || orderForm.guestName,
        lastName: orderForm.guestName.split(' ')[1] || '',
        email: orderForm.guestEmail,
        phone: {
          number: orderForm.guestPhone
        }
      },
      remarkToHotel: orderForm.remarkToHotel
    }
    
    const response = await bookingApi.createBooking(bookingRequest)

    ElMessage.success('订单创建成功')
    // Use platformOrderId for subsequent order queries (recommended)
    router.push({
      path: '/booking/orders',
      query: {
        orderId: response.supplierOrderId, // Keep for backward compatibility
        platformOrderId: response.platformOrderId // New parameter for better order tracking
      }
    })
  } catch (error) {
    console.error('Create order failed:', error)
    ElMessage.error('创建订单失败')
  } finally {
    submitting.value = false
  }
}

const goBack = () => {
  router.back()
}

// Watch for date changes to reload room types
watch([() => orderForm.checkIn, () => orderForm.checkOut], () => {
  if (selectedHotel.value && orderForm.checkIn && orderForm.checkOut) {
    loadRoomTypes()
  }
})
</script>

<style scoped>
.create-order {
  padding: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
}

.form-card {
  margin-top: 20px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.section-card {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hotel-results,
.room-types {
  margin-top: 15px;
}

.selected-hotel,
.selected-room {
  margin-top: 15px;
}

.hotel-info,
.room-info {
  margin-top: 10px;
}

.hotel-info p,
.room-info p {
  margin: 5px 0;
  color: #606266;
}

.price {
  font-weight: 600;
  color: #f56c6c;
}

.guests-section {
  margin-top: 20px;
}

.guests-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.price-summary {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}

.price-item.total {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.amount {
  font-weight: 600;
  color: #f56c6c;
}

.price-item.total .amount {
  color: #e6a23c;
  font-size: 20px;
}

.submit-section {
  text-align: center;
  margin-top: 30px;
  padding: 20px 0;
}

.submit-section .el-button {
  margin: 0 10px;
  min-width: 120px;
}

.validation-status {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #909399;
  font-size: 14px;
}

.status-item.completed {
  background-color: #f0f9ff;
  color: #67c23a;
}

.status-item .el-icon {
  font-size: 16px;
}

.validation-hint {
  text-align: center;
  margin-top: 10px;
}
</style>

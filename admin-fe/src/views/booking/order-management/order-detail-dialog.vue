<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('booking.orders.orderDetail', '订单详情')"
    width="800px"
    @close="handleClose"
  >
    <div v-loading="loading" class="order-detail">
      <template v-if="orderDetail">
        <!-- Order Header -->
        <div class="order-header">
          <div class="order-info">
            <h3>{{ $t('booking.order.orderId') }}: {{ orderDetail.orderId }}</h3>
            <p>{{ $t('booking.order.confirmationNumber') }}: {{ orderDetail.confirmationNumber }}</p>
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(orderDetail.status)" size="large">
              {{ getStatusText(orderDetail.status) }}
            </el-tag>
          </div>
        </div>

        <!-- Hotel Information -->
        <el-card class="section-card" shadow="never">
          <template #header>
            <span class="section-title">酒店信息</span>
          </template>
          <div class="hotel-info">
            <div class="hotel-details">
              <h4>{{ orderDetail.hotel.name }}</h4>
              <p class="hotel-address">
                <el-icon><Location /></el-icon>
                {{ orderDetail.hotel.address }}, {{ orderDetail.hotel.city }}, {{ orderDetail.hotel.country }}
              </p>
              <div class="hotel-rating">
                <el-rate v-model="orderDetail.hotel.rating" disabled show-score />
              </div>
            </div>
            <div class="room-info">
              <h5>房间信息</h5>
              <p>{{ getI18nText(orderDetail.room.roomName) }}</p>
              <p class="room-type">{{ orderDetail.room.type }}</p>
            </div>
          </div>
        </el-card>

        <!-- Booking Details -->
        <el-card class="section-card" shadow="never">
          <template #header>
            <span class="section-title">预订详情</span>
          </template>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="detail-item">
                <label>入住日期:</label>
                <span>{{ orderDetail.checkIn }}</span>
              </div>
              <div class="detail-item">
                <label>退房日期:</label>
                <span>{{ orderDetail.checkOut }}</span>
              </div>
              <div class="detail-item">
                <label>住宿天数:</label>
                <span>{{ orderDetail.nights }} 晚</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>成人数量:</label>
                <span>{{ orderDetail.adults }} 人</span>
              </div>
              <div class="detail-item">
                <label>儿童数量:</label>
                <span>{{ orderDetail.children }} 人</span>
              </div>
              <div class="detail-item">
                <label>房间数量:</label>
                <span>{{ orderDetail.rooms }} 间</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- Guest Information -->
        <el-card class="section-card" shadow="never">
          <template #header>
            <span class="section-title">客人信息</span>
          </template>
          <div class="guests-list">
            <div v-for="(guest, index) in orderDetail.guests" :key="index" class="guest-item">
              <h5>客人 {{ index + 1 }}</h5>
              <div class="guest-details">
                <div class="detail-item">
                  <label>姓名:</label>
                  <span>{{ guest.firstName }} {{ guest.lastName }}</span>
                </div>
                <div class="detail-item">
                  <label>邮箱:</label>
                  <span>{{ guest.email }}</span>
                </div>
                <div class="detail-item">
                  <label>电话:</label>
                  <span>{{ guest.phone }}</span>
                </div>
                <div v-if="guest.nationality" class="detail-item">
                  <label>国籍:</label>
                  <span>{{ guest.nationality }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Special Requests -->
        <el-card v-if="orderDetail.specialRequests" class="section-card" shadow="never">
          <template #header>
            <span class="section-title">特殊要求</span>
          </template>
          <p>{{ orderDetail.specialRequests }}</p>
        </el-card>

        <!-- Payment Information -->
        <el-card class="section-card" shadow="never">
          <template #header>
            <span class="section-title">费用信息</span>
          </template>
          <div class="payment-info">
            <div class="price-breakdown">
              <div class="price-item">
                <span>房费小计:</span>
                <span>{{ orderDetail.currency }} {{ orderDetail.totalPrice }}</span>
              </div>
              <div class="price-item total">
                <span>总计:</span>
                <span>{{ orderDetail.currency }} {{ orderDetail.totalPrice }}</span>
              </div>
            </div>
            
            <div v-if="orderDetail.paymentInfo" class="payment-details">
              <h5>支付信息</h5>
              <div class="detail-item">
                <label>支付方式:</label>
                <span>{{ orderDetail.paymentInfo.method }}</span>
              </div>
              <div class="detail-item">
                <label>支付状态:</label>
                <span>{{ orderDetail.paymentInfo.status }}</span>
              </div>
              <div v-if="orderDetail.paymentInfo.transactionId" class="detail-item">
                <label>交易号:</label>
                <span>{{ orderDetail.paymentInfo.transactionId }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Cancellation Policy -->
        <el-card class="section-card" shadow="never">
          <template #header>
            <span class="section-title">取消政策</span>
          </template>
          <p>{{ orderDetail.cancellationPolicy }}</p>
        </el-card>

        <!-- Order History -->
        <el-card v-if="orderDetail.history" class="section-card" shadow="never">
          <template #header>
            <span class="section-title">订单历史</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in orderDetail.history"
              :key="index"
              :timestamp="formatDateTime(item.timestamp)"
              placement="top"
            >
              <div class="history-item">
                <span class="status">{{ getStatusText(item.status) }}</span>
                <span v-if="item.note" class="note">{{ item.note }}</span>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </template>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('booking.order.close') }}</el-button>
        <el-button
          v-if="orderDetail && (orderDetail.status === 'confirmed' || orderDetail.status === 'pending')"
          type="danger"
          @click="handleCancel"
        >
          {{ $t('booking.order.cancel') }}
        </el-button>
        <el-button type="primary" @click="handlePrint">
          {{ $t('booking.order.print') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { orderApi, type OrderDetailResponse } from '@/api/booking/orderApi'
import { bookingApi } from '@/api/booking/bookingApi'
import { useI18nText, useStarlingText } from '@/utils/i18n'
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
  orderId: string
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()
const getI18nText = useI18nText()
const getStarlingText = useStarlingText()

// Reactive data
const dialogVisible = ref(false)
const loading = ref(false)
const orderDetail = ref<OrderDetailResponse | null>(null)

// Watch for visibility changes
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.orderId) {
    loadOrderDetail()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const loadOrderDetail = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    orderDetail.value = await orderApi.getOrderDetail(props.orderId)
  } catch (error) {
    console.error('Failed to load order detail:', error)
    ElMessage.error(getStarlingText('booking.order.loadDetailFailed', t('booking.order.loadDetailFailed')))
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  orderDetail.value = null
}

const handleCancel = async () => {
  if (!orderDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 ${orderDetail.value.orderId} 吗？`,
      '取消订单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await bookingApi.cancelBooking({
      orderId: orderDetail.value.orderId,
      reason: '管理员取消'
    })
    
    ElMessage.success('订单取消成功')
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Cancel order failed:', error)
      ElMessage.error('取消订单失败')
    }
  }
}

const handlePrint = () => {
  window.print()
}

// Helper methods
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    created: 'info',
    paid: 'primary',
    pending: 'warning',
    confirmed: 'success',
    completed: 'success',
    cancelled: 'danger',
    cancelling: 'warning',
    refunding: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    created: '已创建',
    paid: '已支付',
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消',
    cancelling: '取消中',
    refunding: '退款中'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped lang="scss">
.order-detail {
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    
    .order-info {
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }
      
      p {
        color: #606266;
        margin: 0;
      }
    }
  }
  
  .section-card {
    margin-bottom: 16px;
    
    .section-title {
      font-weight: 600;
      color: #303133;
    }
    
    .hotel-info {
      display: flex;
      gap: 24px;
      
      .hotel-details {
        flex: 1;
        
        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .hotel-address {
          color: #606266;
          margin: 0 0 8px 0;
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
      
      .room-info {
        width: 200px;
        
        h5 {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        p {
          margin: 0 0 4px 0;
          
          &.room-type {
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }
    
    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      
      label {
        color: #606266;
        font-weight: 500;
      }
      
      span {
        color: #303133;
      }
    }
    
    .guests-list {
      .guest-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #f5f7fa;
        border-radius: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h5 {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 12px 0;
        }
        
        .guest-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
        }
      }
    }
    
    .payment-info {
      .price-breakdown {
        margin-bottom: 16px;
        
        .price-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          &.total {
            font-weight: 600;
            font-size: 16px;
            color: #E6A23C;
            border-top: 1px solid #ebeef5;
            padding-top: 8px;
          }
        }
      }
      
      .payment-details {
        h5 {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 12px 0;
        }
      }
    }
    
    .history-item {
      .status {
        font-weight: 600;
        color: #303133;
      }
      
      .note {
        color: #606266;
        margin-left: 8px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

<template>
  <div class="hotel-search">
    <!-- Search Header -->
    <div class="search-header">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">{{ $t('booking.search.title', 'Hotel Search') }}</h1>
          <p class="page-description">{{ $t('booking.search.description', 'Search hotels worldwide, find the perfect accommodation') }}</p>
        </div>
        <div class="header-decoration">
          <div class="decoration-circle"></div>
          <div class="decoration-circle"></div>
          <div class="decoration-circle"></div>
        </div>
      </div>
    </div>

    <!-- Search Form -->
    <div class="search-form-container">
      <el-card class="search-form-card" shadow="hover">
      <el-form :model="searchForm" :rules="searchRules" ref="searchFormRef" label-width="90px">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item :label="$t('booking.search.destination', '目的地')" prop="destination">
              <CitySearchInput
                v-model="searchForm.destination"
                :placeholder="$t('booking.search.destinationPlaceholder', '输入城市或酒店名称')"
                @select="handleCitySelect"
                @clear="handleCityClear"
                ref="citySearchRef"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item :label="$t('booking.search.checkIn', '入住')" prop="checkIn">
              <el-date-picker
                v-model="searchForm.checkIn"
                type="date"
                :placeholder="$t('booking.search.selectDate', '选择日期')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledCheckInDate"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item :label="$t('booking.search.checkOut', '退房')" prop="checkOut">
              <el-date-picker
                v-model="searchForm.checkOut"
                type="date"
                :placeholder="$t('booking.search.selectDate', '选择日期')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledCheckOutDate"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item :label="$t('booking.search.guests', '客人')" prop="guests">
              <el-popover placement="bottom" width="300" trigger="click">
                <template #reference>
                  <el-input
                    :value="guestsText"
                    readonly
                    :placeholder="$t('booking.search.selectGuests', '选择客人数量')"
                  >
                    <template #prefix>
                      <el-icon><User /></el-icon>
                    </template>
                  </el-input>
                </template>

                <div class="guests-selector">
                  <div class="guest-item">
                    <span>{{ $t('booking.search.adults', '成人') }}</span>
                    <el-input-number v-model="searchForm.adults" :min="1" :max="10" size="small" />
                  </div>
                  <div class="guest-item">
                    <span>{{ $t('booking.search.children', '儿童') }}</span>
                    <el-input-number v-model="searchForm.children" :min="0" :max="10" size="small" />
                  </div>
                  <div class="guest-item">
                    <span>{{ $t('booking.search.rooms', '房间') }}</span>
                    <el-input-number v-model="searchForm.rooms" :min="1" :max="5" size="small" />
                  </div>
                </div>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" class="search-action-row">
          <el-col :xs="24" :sm="8" :md="6" :lg="4">
            <el-form-item>
              <div class="supplier-filter-compact">
                <el-switch
                  v-model="searchForm.onlyAvailableSuppliers"
                  :active-text="$t('booking.search.onlyAvailableSuppliers', '仅可用')"
                  inline-prompt
                  size="small"
                />
                <el-tooltip
                  :content="$t('booking.search.supplierHint', '开启后仅显示已接入完整数据的供应商酒店')"
                  placement="top"
                >
                  <el-icon class="supplier-hint-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="18" :lg="20">
            <el-form-item class="search-button-item">
              <el-button type="primary" @click="handleSearch" :loading="searching" size="large" class="search-button">
                <el-icon><Search /></el-icon>
                {{ $t('booking.search.searchButton', '搜索酒店') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      </el-card>
    </div>

    <!-- Search Results -->
    <div v-if="searchResults.length > 0" class="search-results">
      <div class="results-header">
        <h2>{{ $t('menus.booking.search.results', 'Search Results') }}</h2>
        <span class="results-count">{{ $t('menus.booking.search.found', 'Found') }} {{ totalResults }} {{ $t('menus.booking.search.hotels', 'hotels') }}</span>
      </div>
      
      <!-- Filters -->
      <div class="filters-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="filters.priceRange" :placeholder="$t('booking.search.priceRange', '价格范围')" clearable>
              <el-option label="$0 - $100" value="0-100" />
              <el-option label="$100 - $300" value="100-300" />
              <el-option label="$300 - $500" value="300-500" />
              <el-option label="$500+" value="500+" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.rating" :placeholder="$t('menus.booking.search.hotelRating', 'Hotel Rating')" clearable>
              <el-option :label="$t('booking.search.rating5Star', '5星级')" value="5" />
              <el-option :label="$t('booking.search.rating4Star', '4星级')" value="4" />
              <el-option :label="$t('booking.search.rating3Star', '3星级')" value="3" />
              <el-option :label="$t('booking.search.rating2Star', '2星级及以下')" value="2" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.sortBy" :placeholder="$t('booking.search.sortBy', '排序方式')">
              <el-option :label="$t('menus.booking.search.sortOptions.priceLowToHigh', 'Price: Low to High')" value="price-asc" />
              <el-option :label="$t('booking.search.sortOptions.priceHighToLow', '价格从高到低')" value="price-desc" />
              <el-option :label="$t('booking.search.sortOptions.ratingHighToLow', '评级从高到低')" value="rating-desc" />
              <el-option :label="$t('booking.search.sortOptions.distanceNearest', '距离最近')" value="distance-asc" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button @click="applyFilters">{{ $t('booking.search.applyFilters', '应用筛选') }}</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- Hotel List -->
      <div class="hotel-list" v-loading="searching">
        <div v-for="hotel in searchResults" :key="hotel.id" class="hotel-item">
          <HotelCard
            :hotel="hotel"
            @view-details="viewHotelDetail"
            @book-now="bookNow"
            @view-gallery="viewGallery"
          />
        </div>
      </div>

      <!-- Pagination -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="totalResults"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="hasSearched && !searching" class="empty-state">
      <el-empty :description="$t('booking.search.noResults', '未找到符合条件的酒店')">
        <el-button type="primary" @click="resetForm">
          {{ $t('booking.search.searchAgain', '重新搜索') }}
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Location, User, Search, QuestionFilled } from '@element-plus/icons-vue'
import { hotelSearchApi, type HotelSearchRequest, type Hotel } from '@/api/booking/searchApi'
import HotelCard from '@/components/booking/HotelCard.vue'
import CitySearchInput from '@/components/booking/CitySearchInput.vue'
import type { Region } from '@/api/booking/searchApi'
import { useI18nText } from '@/utils/i18n'

const router = useRouter()
const { t } = useI18n()
const getI18nText = useI18nText()

// Form refs
const searchFormRef = ref<FormInstance>()
const citySearchRef = ref<InstanceType<typeof CitySearchInput>>()

// Reactive data
const searching = ref(false)
const hasSearched = ref(false)
const searchResults = ref<Hotel[]>([])
const totalResults = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// Search form
const searchForm = reactive<HotelSearchRequest>({
  destination: '',
  checkIn: '',
  checkOut: '',
  adults: 2,
  children: 0,
  rooms: 1,
  currency: 'USD',
  language: 'zh',
  onlyAvailableSuppliers: true // 默认勾选仅可用供应商
})

// Selected city info
const selectedCity = ref<{
  regionId: number
  name: string
  countryCode: string
  region: Region
} | null>(null)

// Filters
const filters = reactive({
  priceRange: '',
  rating: '',
  sortBy: 'price-asc'
})

// Form validation rules
const searchRules = computed<FormRules>(() => ({
  checkIn: [
    { required: true, message: t('booking.search.validation.checkInRequired'), trigger: 'change' }
  ],
  checkOut: [
    { required: true, message: t('booking.search.validation.checkOutRequired'), trigger: 'change' }
  ]
}))

// Computed properties
const guestsText = computed(() => {
  const parts = []

  // Adults text
  const adultKey = searchForm.adults === 1 ? 'booking.search.guestText.adult' : 'booking.search.guestText.adults'
  parts.push(`${searchForm.adults} ${t(adultKey)}`)

  // Children text
  if (searchForm.children > 0) {
    const childKey = searchForm.children === 1 ? 'booking.search.guestText.child' : 'booking.search.guestText.children'
    parts.push(`${searchForm.children} ${t(childKey)}`)
  }

  // Rooms text
  const roomKey = searchForm.rooms === 1 ? 'booking.search.guestText.room' : 'booking.search.guestText.rooms'
  parts.push(`${searchForm.rooms} ${t(roomKey)}`)

  return parts.join(', ')
})

// Date validation
const disabledCheckInDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // Disable past dates
}

const disabledCheckOutDate = (time: Date) => {
  if (!searchForm.checkIn) return false
  return time.getTime() <= new Date(searchForm.checkIn).getTime()
}

// Methods
const handleSearch = async () => {
  if (!searchFormRef.value) return

  try {
    await searchFormRef.value.validate()

    // Check if destination is provided (skip validation when using supplier filter)
    if (!searchForm.onlyAvailableSuppliers && !selectedCity.value && !searchForm.destination.trim()) {
      ElMessage.warning('请选择目的地城市')
      return
    }

    searching.value = true
    hasSearched.value = true

    // Convert form data to backend format
    const searchParams: any = {
      checkIn: searchForm.checkIn,
      checkOut: searchForm.checkOut,
      adultCount: searchForm.adults,
      childrenCount: searchForm.children,
      roomCount: searchForm.rooms,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      currency: searchForm.currency,
      language: searchForm.language
    }

    // Add supplier filter if enabled
    if (searchForm.onlyAvailableSuppliers) {
      searchParams.internalSuppliers = [6] // 6 = Supplier_Dida
      console.log('Filtering by available suppliers:', searchParams.internalSuppliers)
      console.log('Clearing region filters when using supplier filter')
      // 当选择仅显示可用供应商时，不限制地区，置空 regionId 和 regionName
    } else {
      // 只有在未开启供应商过滤时才使用地区信息
      // Use regionId if city is selected, otherwise use regionName
      if (selectedCity.value) {
        searchParams.regionId = selectedCity.value.regionId // Already a string, safe for big numbers
        console.log('Searching with regionId:', searchParams.regionId)
      } else if (searchForm.destination) {
        searchParams.regionName = searchForm.destination
        console.log('Searching with regionName:', searchParams.regionName)
      }
    }

    const response = await hotelSearchApi.searchHotels(searchParams)

    // Convert backend response to frontend format with proper i18n handling
    if (response.list) {
      searchResults.value = response.list.map(hotel => ({
        id: hotel.id?.toString() || '',
        name: getI18nText(hotel.name) || '',
        address: getI18nText(hotel.address) || '',
        city: getI18nText(hotel.citySummary?.city?.name) || '',
        country: getI18nText(hotel.citySummary?.country?.name) || '',
        rating: hotel.rating || 0,
        images: hotel.hotelPictures || [],
        amenities: [],
        description: '',
        location: {
          latitude: hotel.latlngCoordinator?.latitude || 0,
          longitude: hotel.latlngCoordinator?.longitude || 0
        },
        minPrice: hotel.minPrice?.amount || 0,
        currency: hotel.minPrice?.currency || 'USD'
      }))
    } else {
      searchResults.value = []
    }

    totalResults.value = response.total || 0

    ElMessage.success(`${t('menus.booking.search.found', 'Found')} ${response.total || 0} ${t('menus.booking.search.hotels', 'hotels')}`)
  } catch (error) {
    console.error('Search failed:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searching.value = false
  }
}

// Handle city selection
const handleCitySelect = (city: any) => {
  selectedCity.value = {
    regionId: city.regionId,
    name: city.name,
    countryCode: city.countryCode,
    region: city.region
  }
  searchForm.destination = city.name
  console.log('Selected city:', selectedCity.value)
}

// Handle city clear
const handleCityClear = () => {
  selectedCity.value = null
  searchForm.destination = ''
  console.log('City cleared')
}

const resetForm = () => {
  searchFormRef.value?.resetFields()
  citySearchRef.value?.clear()
  selectedCity.value = null
  searchResults.value = []
  totalResults.value = 0
  hasSearched.value = false
  currentPage.value = 1
}

const applyFilters = () => {
  // Apply filters to search results
  handleSearch()
}

const viewHotelDetail = (hotel: Hotel) => {
  router.push({
    path: '/booking/hotel-detail',
    query: {
      hotelId: hotel.id,
      checkIn: searchForm.checkIn,
      checkOut: searchForm.checkOut,
      adults: searchForm.adults.toString(),
      children: searchForm.children.toString(),
      rooms: searchForm.rooms.toString()
    }
  })
}

const bookNow = (hotel: Hotel) => {
  // Navigate to booking page with hotel and search params
  router.push({
    path: '/booking/hotel-search/book',
    query: {
      hotelId: hotel.id,
      checkIn: searchForm.checkIn,
      checkOut: searchForm.checkOut,
      adults: searchForm.adults.toString(),
      children: searchForm.children.toString(),
      rooms: searchForm.rooms.toString()
    }
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  handleSearch()
}

const viewGallery = (hotel: Hotel) => {
  // TODO: Implement gallery view
  ElMessage.info(`查看 ${hotel.name} 的图片库`)
}

// Initialize with default dates
onMounted(() => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  searchForm.checkIn = today.toISOString().split('T')[0]
  searchForm.checkOut = tomorrow.toISOString().split('T')[0]
})
</script>

<style scoped lang="scss">
.hotel-search {
  padding: 24px;
  
  .search-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px 32px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 2;
    }

    .header-text {
      flex: 1;

      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #ffffff;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .page-description {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        line-height: 1.5;
      }
    }

    .header-decoration {
      display: flex;
      gap: 16px;

      .decoration-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:nth-child(1) {
          animation: float 3s ease-in-out infinite;
        }

        &:nth-child(2) {
          animation: float 3s ease-in-out infinite 1s;
          width: 60px;
          height: 60px;
        }

        &:nth-child(3) {
          animation: float 3s ease-in-out infinite 2s;
          width: 40px;
          height: 40px;
        }
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: rotate 20s linear infinite;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .search-form-container {
    margin-bottom: 20px;

    .search-form-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
      }

      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }

  .search-form-card {

    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
      font-size: 13px;
      white-space: nowrap;
    }

    :deep(.el-form-item) {
      margin-bottom: 16px;
    }

    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.is-focus {
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }
      }
    }

    :deep(.el-date-editor) {
      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.is-focus {
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }
      }
    }

    :deep(.el-button--primary) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-weight: 600;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }

    .guests-selector {
      .guest-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 0;

        &:last-child {
          margin-bottom: 0;
        }

        span {
          font-weight: 500;
          color: #303133;
        }
      }
    }

    .search-action-row {
      margin-top: 12px;
      align-items: center;

      .el-col {
        &:first-child {
          margin-bottom: 12px;

          @media (min-width: 768px) {
            margin-bottom: 0;
          }
        }
      }
    }

    .supplier-filter-compact {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      font-size: 12px;

      .supplier-hint-icon {
        color: var(--el-color-info);
        cursor: help;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }

    .search-button-item {
      .search-button {
        width: 200px;
        height: 40px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;

        @media (max-width: 768px) {
          width: 100%;
        }
      }
    }
  }
  
  .search-results {
    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h2 {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }
      
      .results-count {
        color: #606266;
        font-size: 14px;
      }
    }
    
    .filters-section {
      margin-bottom: 24px;
      padding: 16px;
      background: #f5f7fa;
      border-radius: 8px;
    }
    
    .hotel-list {
      .hotel-item {
        margin-bottom: 16px;
        
        .hotel-card {
          .hotel-content {
            display: flex;
            gap: 16px;
            
            .hotel-image {
              width: 200px;
              height: 150px;
              flex-shrink: 0;
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8px;
              }
            }
            
            .hotel-info {
              flex: 1;
              
              .hotel-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 8px;
                
                .hotel-name {
                  font-size: 18px;
                  font-weight: 600;
                  color: #303133;
                  margin: 0;
                }
              }
              
              .hotel-address {
                color: #606266;
                font-size: 14px;
                margin: 0 0 8px 0;
                display: flex;
                align-items: center;
                gap: 4px;
              }
              
              .hotel-amenities {
                margin-bottom: 8px;
                
                .el-tag {
                  margin-right: 8px;
                }
                
                .more-amenities {
                  color: #409EFF;
                  font-size: 12px;
                }
              }
              
              .hotel-description {
                color: #606266;
                font-size: 14px;
                line-height: 1.5;
                margin: 0;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }
            
            .hotel-pricing {
              width: 200px;
              flex-shrink: 0;
              text-align: right;
              
              .price-info {
                margin-bottom: 16px;
                
                .price-label {
                  color: #909399;
                  font-size: 12px;
                  display: block;
                  margin-bottom: 4px;
                }
                
                .price {
                  .currency {
                    font-size: 14px;
                    color: #606266;
                  }
                  
                  .amount {
                    font-size: 24px;
                    font-weight: 600;
                    color: #E6A23C;
                  }
                  
                  .period {
                    font-size: 12px;
                    color: #909399;
                  }
                }
              }
              
              .action-buttons {
                display: flex;
                flex-direction: column;
                gap: 8px;
              }
            }
          }
        }
      }
    }
    
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 64px 0;
  }

  // Responsive design fixes
  @media (max-width: 768px) {
    .search-form-container {
      .search-form-card {
        :deep(.el-card__body) {
          padding: 16px;
        }
      }
    }

    .search-form-card {
      :deep(.el-form-item__label) {
        font-size: 13px;
        margin-bottom: 4px;
      }

      :deep(.el-col) {
        margin-bottom: 16px;
      }

      :deep(.el-date-editor) {
        width: 100% !important;
      }

      :deep(.el-input) {
        width: 100% !important;
      }
    }

    .hero-section {
      padding: 40px 0;

      h1 {
        font-size: 28px !important;
      }

      p {
        font-size: 16px !important;
      }
    }
  }

  @media (max-width: 480px) {
    .search-form-container {
      margin-bottom: 16px;
    }

    .search-form-card {
      :deep(.el-form-item__label) {
        font-size: 12px;
      }

      :deep(.el-button--primary) {
        width: 100%;
        margin-top: 8px;
      }
    }
  }
}
</style>

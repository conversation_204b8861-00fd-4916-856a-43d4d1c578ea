<template>
  <div class="hotel-book" v-loading="loading">
    <div class="booking-container">
      <!-- Booking Header -->
      <div class="booking-header">
        <h1>完成预订</h1>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回酒店详情
        </el-button>
      </div>

      <el-row :gutter="24">
        <!-- Booking Form -->
        <el-col :span="16">
          <el-form
            ref="bookingFormRef"
            :model="bookingForm"
            :rules="bookingRules"
            label-width="120px"
            class="booking-form"
          >
            <!-- Guest Information -->
            <el-card class="form-section" shadow="never">
              <template #header>
                <span class="section-title">主要联系人信息</span>
              </template>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="名字" prop="primaryGuest.firstName" :validate-status="getFieldValidationStatus('firstName')" :error="getFieldErrorMessage('firstName')">
                    <el-input
                      v-model="bookingForm.primaryGuest.firstName"
                      placeholder="请输入名字"
                      data-cy="first-name"
                      :class="getFieldInputClass('firstName')"
                    >
                      <template #suffix>
                        <el-icon v-if="getFieldValidationStatus('firstName') === 'success'" class="validation-icon success">
                          <Check />
                        </el-icon>
                        <el-icon v-else-if="getFieldValidationStatus('firstName') === 'error'" class="validation-icon error">
                          <Close />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="姓氏" prop="primaryGuest.lastName" :validate-status="getFieldValidationStatus('lastName')" :error="getFieldErrorMessage('lastName')">
                    <el-input
                      v-model="bookingForm.primaryGuest.lastName"
                      placeholder="请输入姓氏"
                      data-cy="last-name"
                      :class="getFieldInputClass('lastName')"
                    >
                      <template #suffix>
                        <el-icon v-if="getFieldValidationStatus('lastName') === 'success'" class="validation-icon success">
                          <Check />
                        </el-icon>
                        <el-icon v-else-if="getFieldValidationStatus('lastName') === 'error'" class="validation-icon error">
                          <Close />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="primaryGuest.email" :validate-status="getFieldValidationStatus('email')" :error="getFieldErrorMessage('email')">
                    <el-input
                      v-model="bookingForm.primaryGuest.email"
                      placeholder="请输入邮箱地址"
                      data-cy="email"
                      :class="getFieldInputClass('email')"
                    >
                      <template #suffix>
                        <el-icon v-if="getFieldValidationStatus('email') === 'success'" class="validation-icon success">
                          <Check />
                        </el-icon>
                        <el-icon v-else-if="getFieldValidationStatus('email') === 'error'" class="validation-icon error">
                          <Close />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="电话" prop="primaryGuest.phone" :validate-status="getFieldValidationStatus('phone')" :error="getFieldErrorMessage('phone')">
                    <el-input
                      v-model="bookingForm.primaryGuest.phone"
                      placeholder="请输入电话号码"
                      data-cy="phone"
                      :class="getFieldInputClass('phone')"
                    >
                      <template #suffix>
                        <el-icon v-if="getFieldValidationStatus('phone') === 'success'" class="validation-icon success">
                          <Check />
                        </el-icon>
                        <el-icon v-else-if="getFieldValidationStatus('phone') === 'error'" class="validation-icon error">
                          <Close />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="国籍" prop="primaryGuest.nationality" :validate-status="getFieldValidationStatus('nationality')" :error="getFieldErrorMessage('nationality')">
                <el-select
                  v-model="bookingForm.primaryGuest.nationality"
                  placeholder="请选择国籍"
                  style="width: 100%"
                  data-cy="nationality"
                  :class="getFieldInputClass('nationality')"
                >
                  <el-option label="中国" value="CN" />
                  <el-option label="美国" value="US" />
                  <el-option label="英国" value="UK" />
                  <el-option label="日本" value="JP" />
                  <el-option label="韩国" value="KR" />
                  <template #suffix>
                    <el-icon v-if="getFieldValidationStatus('nationality') === 'success'" class="validation-icon success">
                      <Check />
                    </el-icon>
                    <el-icon v-else-if="getFieldValidationStatus('nationality') === 'error'" class="validation-icon error">
                      <Close />
                    </el-icon>
                  </template>
                </el-select>
              </el-form-item>
            </el-card>

            <!-- Additional Guests -->
            <el-card v-if="needsAdditionalGuests" class="form-section" shadow="never">
              <template #header>
                <span class="section-title">其他客人信息</span>
              </template>
              
              <div v-for="(guest, index) in bookingForm.additionalGuests" :key="index" class="guest-form">
                <h4>客人 {{ index + 2 }}</h4>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item :label="`名字`" :prop="`additionalGuests.${index}.firstName`">
                      <el-input v-model="guest.firstName" placeholder="请输入名字" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="`姓氏`" :prop="`additionalGuests.${index}.lastName`">
                      <el-input v-model="guest.lastName" placeholder="请输入姓氏" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>

            <!-- Special Requests -->
            <el-card class="form-section" shadow="never">
              <template #header>
                <span class="section-title">特殊要求</span>
              </template>
              
              <el-form-item label="特殊要求">
                <el-input
                  v-model="bookingForm.specialRequests"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入特殊要求（可选）"
                />
              </el-form-item>
              
              <div class="special-options">
                <el-checkbox v-model="bookingForm.preferences.earlyCheckIn">提前入住</el-checkbox>
                <el-checkbox v-model="bookingForm.preferences.lateCheckOut">延迟退房</el-checkbox>
                <el-checkbox v-model="bookingForm.preferences.highFloor">高楼层房间</el-checkbox>
                <el-checkbox v-model="bookingForm.preferences.quietRoom">安静房间</el-checkbox>
              </div>
            </el-card>

            <!-- Payment Information -->
            <el-card class="form-section" shadow="never">
              <template #header>
                <span class="section-title">支付信息</span>
              </template>

              <el-form-item label="支付方式" prop="paymentMethod" :validate-status="getFieldValidationStatus('paymentMethod')" :error="getFieldErrorMessage('paymentMethod')">
                <div class="payment-method-wrapper">
                  <el-radio-group v-model="bookingForm.paymentMethod" :class="getFieldInputClass('paymentMethod')">
                    <el-radio value="credit_card">信用卡</el-radio>
                    <el-radio value="debit_card">借记卡</el-radio>
                    <el-radio value="paypal">PayPal</el-radio>
                    <el-radio value="alipay">支付宝</el-radio>
                  </el-radio-group>
                  <el-icon v-if="getFieldValidationStatus('paymentMethod') === 'success'" class="validation-icon success">
                    <Check />
                  </el-icon>
                  <el-icon v-else-if="getFieldValidationStatus('paymentMethod') === 'error'" class="validation-icon error">
                    <Close />
                  </el-icon>
                </div>
              </el-form-item>
              
              <div v-if="bookingForm.paymentMethod === 'credit_card' || bookingForm.paymentMethod === 'debit_card'" class="card-info">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="卡号" prop="cardInfo.number">
                      <el-input v-model="bookingForm.cardInfo.number" placeholder="请输入卡号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="持卡人姓名" prop="cardInfo.holderName">
                      <el-input v-model="bookingForm.cardInfo.holderName" placeholder="请输入持卡人姓名" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="有效期" prop="cardInfo.expiry">
                      <el-input v-model="bookingForm.cardInfo.expiry" placeholder="MM/YY" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="CVV" prop="cardInfo.cvv">
                      <el-input v-model="bookingForm.cardInfo.cvv" placeholder="CVV" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>

            <!-- Terms and Conditions -->
            <el-card class="form-section" shadow="never">
              <template #header>
                <span class="section-title">条款与条件</span>
              </template>

              <div class="terms-content">
                <div class="terms-item" :class="{ 'has-error': getFieldValidationStatus('agreeToTerms') === 'error' }">
                  <el-checkbox v-model="bookingForm.agreeToTerms" :required="true" :class="getFieldInputClass('agreeToTerms')">
                    我已阅读并同意 <a href="#" @click.prevent="showTerms">预订条款</a> 和 <a href="#" @click.prevent="showPrivacy">隐私政策</a>
                  </el-checkbox>
                  <el-icon v-if="getFieldValidationStatus('agreeToTerms') === 'success'" class="validation-icon success">
                    <Check />
                  </el-icon>
                  <el-icon v-else-if="getFieldValidationStatus('agreeToTerms') === 'error'" class="validation-icon error">
                    <Close />
                  </el-icon>
                  <div v-if="getFieldErrorMessage('agreeToTerms')" class="field-error-message">
                    {{ getFieldErrorMessage('agreeToTerms') }}
                  </div>
                </div>

                <el-checkbox v-model="bookingForm.agreeToMarketing">
                  我同意接收酒店的营销邮件和优惠信息
                </el-checkbox>
              </div>
            </el-card>

            <!-- 预订状态摘要 -->
            <el-card v-if="!isFormValidAndAvailable" class="form-section validation-summary" shadow="never">
              <div class="summary-content">
                <el-icon class="summary-icon warning">
                  <WarningFilled />
                </el-icon>
                <div class="summary-text">
                  <h4>请完善以下信息</h4>
                  <p>{{ validationMessage }}</p>
                </div>
              </div>
            </el-card>

            <!-- Submit Button -->
            <div class="form-actions">
              <el-button size="large" @click="goBack">取消</el-button>
              <el-button
                type="primary"
                size="large"
                :loading="submitting"
                @click="submitBooking"
                data-cy="submit-booking"
                :disabled="!canSubmitBooking"
              >
                {{ submitButtonText }}
              </el-button>
            </div>
          </el-form>
        </el-col>

        <!-- Booking Summary -->
        <el-col :span="8">
          <el-card class="booking-summary" shadow="always" :body-style="{ padding: '20px' }">
            <template #header>
              <span>预订摘要</span>
            </template>
            
            <div v-if="bookingData" class="summary-content">
              <!-- Hotel Info -->
              <div class="hotel-summary" v-if="bookingData">
                <h3>{{ bookingData.hotel.name }}</h3>
                <p>{{ bookingData.hotel.address }}</p>
                <div class="rating">
                  <el-rate :model-value="bookingData.hotel.rating" disabled show-score />
                </div>
              </div>

              <el-divider />

              <!-- Room Info -->
              <div class="room-summary" v-if="bookingData">
                <h4>{{ getI18nText(bookingData.room.roomName) }}</h4>
                <p>{{ getI18nText(bookingData.room.roomDesc) }}</p>
                <div class="room-features" v-if="availabilityData?.roomRatePkg">
                  <el-tag v-if="availabilityData.roomRatePkg.meal" type="success" size="small">含早餐</el-tag>
                  <el-tag v-if="availabilityData.roomRatePkg.cancelPolicy" type="info" size="small">可退款</el-tag>
                  <el-tag :type="isRoomAvailable ? 'success' : 'danger'" size="small">
                    {{ isRoomAvailable ? '可预订' : '不可预订' }}
                  </el-tag>
                </div>
              </div>

              <!-- Availability Check -->
              <div class="availability-check">
                <el-button
                  @click="checkAvailability"
                  :loading="checkingAvailability"
                  type="primary"
                  size="small"
                  data-cy="check-availability"
                >
                  重新检查可用性
                </el-button>
                <p v-if="availabilityData" class="availability-status" data-cy="availability-status">
                  <span :class="{ 'available': isRoomAvailable, 'unavailable': !isRoomAvailable }">
                    {{ isRoomAvailable ? '✓ 房间可预订' : '✗ 房间不可预订' }}
                  </span>
                </p>
              </div>
              
              <el-divider />
              
              <!-- Stay Details -->
              <div class="stay-details">
                <div class="detail-item">
                  <span>入住日期:</span>
                  <span>{{ bookingParams.checkIn }}</span>
                </div>
                <div class="detail-item">
                  <span>退房日期:</span>
                  <span>{{ bookingParams.checkOut }}</span>
                </div>
                <div class="detail-item">
                  <span>住宿天数:</span>
                  <span>{{ nightsCount }} 晚</span>
                </div>
                <div class="detail-item">
                  <span>客人数量:</span>
                  <span>{{ bookingParams.adults }} 成人, {{ bookingParams.children }} 儿童</span>
                </div>
                <div class="detail-item">
                  <span>房间数量:</span>
                  <span>{{ bookingParams.rooms }} 间</span>
                </div>
              </div>
              
              <el-divider />
              
              <!-- Price Breakdown -->
              <div class="price-breakdown" v-if="availabilityData?.data?.roomRatePkg?.rate">
                <div class="price-item">
                  <span>房费 ({{ nightsCount }} 晚):</span>
                  <span>{{ currency }} {{ (baseRoomRate * nightsCount).toLocaleString() }}</span>
                </div>
                <div class="price-item">
                  <span>税费:</span>
                  <span>{{ currency }} {{ taxAmount.toLocaleString() }}</span>
                </div>
                <div class="price-item total">
                  <span>总计:</span>
                  <span>{{ currency }} {{ totalPrice.toLocaleString() }}</span>
                </div>
              </div>

              <!-- Cancellation Policy -->
              <div class="cancellation-policy" v-if="availabilityData?.data?.roomRatePkg?.cancelPolicy">
                <h5>取消政策</h5>
                <p v-if="availabilityData.data.roomRatePkg.cancelPolicy.refundable">
                  可退款 - {{ availabilityData.data.roomRatePkg.cancelPolicy.refundableMode || '请联系酒店了解详情' }}
                </p>
                <p v-else>
                  不可退款
                </p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { ArrowLeft, Check, Close, WarningFilled, QuestionFilled } from '@element-plus/icons-vue'
import { bookingApi, type BookingRequest, type Guest } from '@/api/booking/bookingApi'
import { hotelSearchApi, type AvailabilityRequest, type HotelSearchRequest } from '@/api/booking/searchApi'
import { useI18nText } from '@/utils/i18n'

const route = useRoute()
const router = useRouter()
const getI18nText = useI18nText()

// Form ref
const bookingFormRef = ref<FormInstance>()

// Reactive data
const loading = ref(false)
const submitting = ref(false)

// Booking parameters from route
const bookingParams = reactive({
  ratePkgId: route.query.ratePkgId as string,
  hotelId: route.query.hotelId as string,
  roomId: route.query.roomId as string,
  rateId: route.query.rateId as string,
  checkIn: route.query.checkIn as string,
  checkOut: route.query.checkOut as string,
  adults: parseInt(route.query.adults as string) || 2,
  children: parseInt(route.query.children as string) || 0,
  rooms: parseInt(route.query.rooms as string) || 1
})

// Booking data from API
const bookingData = ref<any>(null)
const availabilityData = ref<any>(null)
const checkingAvailability = ref(false)

// Booking form
const bookingForm = reactive({
  primaryGuest: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    nationality: ''
  },
  additionalGuests: [] as Guest[],
  specialRequests: '',
  preferences: {
    earlyCheckIn: false,
    lateCheckOut: false,
    highFloor: false,
    quietRoom: false
  },
  paymentMethod: 'credit_card',
  cardInfo: {
    number: '',
    holderName: '',
    expiry: '',
    cvv: ''
  },
  agreeToTerms: false,
  agreeToMarketing: false
})

// Form validation rules
const bookingRules = {
  'primaryGuest.firstName': [
    { required: true, message: '请输入名字', trigger: 'blur' }
  ],
  'primaryGuest.lastName': [
    { required: true, message: '请输入姓氏', trigger: 'blur' }
  ],
  'primaryGuest.email': [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  'primaryGuest.phone': [
    { required: true, message: '请输入电话号码', trigger: 'blur' }
  ],
  'primaryGuest.nationality': [
    { required: true, message: '请选择国籍', trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ]
}

// Computed properties
const nightsCount = computed(() => {
  if (!bookingParams.checkIn || !bookingParams.checkOut) return 0
  const checkIn = new Date(bookingParams.checkIn)
  const checkOut = new Date(bookingParams.checkOut)
  return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
})

const needsAdditionalGuests = computed(() => {
  return bookingParams.adults > 1
})



// 计算房间是否可用 - 根据实际API响应结构
const isRoomAvailable = computed(() => {
  if (!availabilityData.value) return false
  return availabilityData.value.status === 1 &&
         availabilityData.value.roomRatePkg?.available === true
})

// 计算基础房费
const baseRoomRate = computed(() => {
  return availabilityData.value?.roomRatePkg?.rate?.finalRate?.amount || 0
})

// 计算总价格
const totalPrice = computed(() => {
  return baseRoomRate.value
})

// 计算货币
const currency = computed(() => {
  const apiCurrency = availabilityData.value?.roomRatePkg?.rate?.finalRate?.currency
  // 如果API返回的货币为空字符串或undefined，使用默认货币
  return (apiCurrency && apiCurrency.trim()) ? apiCurrency : 'USD'
})

// 计算税费 (从API响应中获取或计算)
const taxAmount = computed(() => {
  return availabilityData.value?.roomRatePkg?.tax?.total?.amount || 0
})

// 计算总金额
const totalAmount = computed(() => {
  return totalPrice.value + taxAmount.value
})

// 检查表单是否完整填写
const isFormComplete = computed(() => {
  return bookingForm.primaryGuest.firstName &&
         bookingForm.primaryGuest.lastName &&
         bookingForm.primaryGuest.email &&
         bookingForm.primaryGuest.phone &&
         bookingForm.primaryGuest.nationality &&
         bookingForm.paymentMethod &&
         bookingForm.agreeToTerms
})

// 检查是否可以提交预订
const canSubmitBooking = computed(() => {
  // 基本表单验证
  if (!isFormComplete.value) return false

  // 如果还没有检查可用性，不允许提交
  if (!availabilityData.value) return false

  // 房间必须可用
  return isRoomAvailable.value
})

// 按钮文本
const submitButtonText = computed(() => {
  if (submitting.value) return '提交中...'

  if (!isFormComplete.value) {
    return '请完善信息'
  }

  if (!availabilityData.value) {
    return '检查可用性'
  }

  if (!isRoomAvailable.value) {
    return '房间不可用'
  }

  return '确认预订'
})

// 验证消息
const validationMessage = computed(() => {
  const missing = []

  if (!bookingForm.primaryGuest.firstName) missing.push('名字')
  if (!bookingForm.primaryGuest.lastName) missing.push('姓氏')
  if (!bookingForm.primaryGuest.email) missing.push('邮箱')
  if (!bookingForm.primaryGuest.phone) missing.push('电话')
  if (!bookingForm.primaryGuest.nationality) missing.push('国籍')
  if (!bookingForm.paymentMethod) missing.push('支付方式')
  if (!bookingForm.agreeToTerms) missing.push('同意条款')

  if (missing.length > 0) {
    return `请填写：${missing.join('、')}`
  }

  // 如果正在检查可用性，显示加载状态
  if (checkingAvailability.value) {
    return '正在检查房间可用性...'
  }

  if (!availabilityData.value) {
    return '请先检查房间可用性'
  }

  if (!isRoomAvailable.value) {
    return '抱歉，该房间当前不可用'
  }

  return '所有信息已完善，可以提交预订'
})

// 字段验证状态
const getFieldValidationStatus = (fieldName: string) => {
  switch (fieldName) {
    case 'firstName':
      if (!bookingForm.primaryGuest.firstName) return 'error'
      return bookingForm.primaryGuest.firstName.trim() ? 'success' : 'error'

    case 'lastName':
      if (!bookingForm.primaryGuest.lastName) return 'error'
      return bookingForm.primaryGuest.lastName.trim() ? 'success' : 'error'

    case 'email':
      if (!bookingForm.primaryGuest.email) return 'error'
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(bookingForm.primaryGuest.email) ? 'success' : 'error'

    case 'phone':
      if (!bookingForm.primaryGuest.phone) return 'error'
      return bookingForm.primaryGuest.phone.trim() ? 'success' : 'error'

    case 'nationality':
      return bookingForm.primaryGuest.nationality ? 'success' : 'error'

    case 'paymentMethod':
      return bookingForm.paymentMethod ? 'success' : 'error'

    case 'agreeToTerms':
      return bookingForm.agreeToTerms ? 'success' : 'error'

    default:
      return ''
  }
}

// 字段错误信息
const getFieldErrorMessage = (fieldName: string) => {
  switch (fieldName) {
    case 'firstName':
      if (!bookingForm.primaryGuest.firstName) return '请输入名字'
      if (!bookingForm.primaryGuest.firstName.trim()) return '名字不能为空'
      return ''

    case 'lastName':
      if (!bookingForm.primaryGuest.lastName) return '请输入姓氏'
      if (!bookingForm.primaryGuest.lastName.trim()) return '姓氏不能为空'
      return ''

    case 'email':
      if (!bookingForm.primaryGuest.email) return '请输入邮箱地址'
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(bookingForm.primaryGuest.email)) return '请输入正确的邮箱地址'
      return ''

    case 'phone':
      if (!bookingForm.primaryGuest.phone) return '请输入电话号码'
      if (!bookingForm.primaryGuest.phone.trim()) return '电话号码不能为空'
      return ''

    case 'nationality':
      return bookingForm.primaryGuest.nationality ? '' : '请选择国籍'

    case 'paymentMethod':
      return bookingForm.paymentMethod ? '' : '请选择支付方式'

    case 'agreeToTerms':
      return bookingForm.agreeToTerms ? '' : '请同意预订条款和隐私政策'

    default:
      return ''
  }
}

// 字段输入框样式
const getFieldInputClass = (fieldName: string) => {
  const status = getFieldValidationStatus(fieldName)
  return {
    'field-success': status === 'success',
    'field-error': status === 'error'
  }
}

// Methods
const initializeAdditionalGuests = () => {
  if (needsAdditionalGuests.value) {
    const guestCount = bookingParams.adults - 1
    bookingForm.additionalGuests = Array.from({ length: guestCount }, () => ({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      nationality: ''
    }))
  }
}

// Check availability and get latest price
const checkAvailability = async () => {
  if (!bookingParams.ratePkgId) {
    ElMessage.error('缺少房价包ID')
    return
  }

  checkingAvailability.value = true
  try {
    const request: AvailabilityRequest = {
      ratePkgId: bookingParams.ratePkgId,
      hotelId: bookingParams.hotelId ? parseInt(bookingParams.hotelId) : undefined,
      roomId: bookingParams.roomId ? parseInt(bookingParams.roomId) : undefined
    }

    // Prepare search params for headers
    const searchParams: HotelSearchRequest = {
      checkIn: bookingParams.checkIn,
      checkOut: bookingParams.checkOut,
      adultCount: bookingParams.adults,
      childrenCount: bookingParams.children,
      roomCount: bookingParams.rooms,
      currency: 'USD', // Default currency
      language: 'en'   // Default language
    }

    const response = await hotelSearchApi.checkAvailability(request, searchParams)
    availabilityData.value = response

    if (response.status !== 1 || !response.roomRatePkg?.available) {
      ElMessage.warning('该房型当前不可预订，请选择其他房型')
      return
    }

    ElMessage.success('房间可用性检查完成')
  } catch (error) {
    console.error('Check availability failed:', error)
    ElMessage.error('检查房间可用性失败')
  } finally {
    checkingAvailability.value = false
  }
}

// Load booking data from backend APIs
const loadBookingData = async () => {
  try {
    console.log('Loading booking data for hotel:', bookingParams.hotelId, 'room:', bookingParams.roomId)

    // Call hotel detail API to get hotel information
    const hotel = await hotelSearchApi.getHotelDetail(bookingParams.hotelId)
    console.log('Hotel detail loaded:', hotel)

    // Call hotel rates API to get room information
    const searchParams: HotelSearchRequest = {
      checkIn: bookingParams.checkIn,
      checkOut: bookingParams.checkOut,
      adultCount: bookingParams.adults,
      childrenCount: bookingParams.children,
      roomCount: bookingParams.rooms,
      currency: 'USD',
      language: 'zh'
    }
    const hotelRates = await hotelSearchApi.getHotelRates(parseInt(bookingParams.hotelId), searchParams)
    console.log('Hotel rates loaded:', hotelRates)
    console.log('Available rooms:', hotelRates.rooms)
    console.log('Looking for roomId:', bookingParams.roomId)
    console.log('Room IDs in response:', hotelRates.rooms.map(r => ({ roomTypeId: r.roomTypeId, id: r.id })))

    // Try multiple ways to find the room
    let room = hotelRates.rooms.find(r => r.roomTypeId === bookingParams.roomId)
    if (!room) {
      // Try with string comparison
      room = hotelRates.rooms.find(r => String(r.roomTypeId) === String(bookingParams.roomId))
    }
    if (!room) {
      // Try with id field if it exists
      room = hotelRates.rooms.find(r => r.id === bookingParams.roomId)
    }
    if (!room) {
      // Try with string comparison on id
      room = hotelRates.rooms.find(r => String(r.id) === String(bookingParams.roomId))
    }

    console.log('Found room:', room)

    if (!room) {
      console.error('Room not found. Available rooms:', hotelRates.rooms)
      console.error('Searching for roomId:', bookingParams.roomId)

      // Use the first room as fallback if available
      if (hotelRates.rooms.length > 0) {
        console.warn('Using first available room as fallback')
        room = hotelRates.rooms[0]
      } else {
        throw new Error('房间信息未找到')
      }
    }

    // Extract text from I18N objects (prefer zh, fallback to en, then any available)
    const getI18nText = (i18nObj: any) => {
      if (typeof i18nObj === 'string') return i18nObj
      if (!i18nObj) return ''
      return i18nObj.zh || i18nObj.en || Object.values(i18nObj)[0] || ''
    }

    bookingData.value = {
      hotel: {
        id: hotel.id,
        name: getI18nText(hotel.name),
        address: getI18nText(hotel.address),
        rating: hotel.star || hotel.rating || 0
      },
      room: {
        id: room.roomTypeId,
        name: getI18nText(room.roomName),
        description: getI18nText(room.roomDesc)
      }
    }

    console.log('Booking data set:', bookingData.value)
  } catch (error) {
    console.error('Failed to load booking data:', error)
    ElMessage.error('加载预订信息失败')

    // Set basic booking data with available parameters to show something
    bookingData.value = {
      hotel: {
        id: bookingParams.hotelId,
        name: '酒店信息加载中...',
        address: '地址信息加载中...',
        rating: 0
      },
      room: {
        id: bookingParams.roomId,
        name: '房间信息加载中...',
        description: '房间详情加载中...'
      }
    }

    // Don't throw error to prevent page crash
    console.log('Set fallback booking data:', bookingData.value)
  }
}

const submitBooking = async () => {
  if (!bookingFormRef.value) return

  try {
    const valid = await bookingFormRef.value.validate()
    if (!valid) return

    if (!bookingForm.agreeToTerms) {
      ElMessage.error('请同意预订条款和隐私政策')
      return
    }

    if (!isRoomAvailable.value) {
      ElMessage.error('房间不可用，请重新检查可用性')
      return
    }

    submitting.value = true

    // Prepare guests data according to backend API format
    const guests = [
      {
        firstName: bookingForm.primaryGuest.firstName,
        lastName: bookingForm.primaryGuest.lastName,
        mobilePhone: bookingForm.primaryGuest.phone,
        nationalityCode: bookingForm.primaryGuest.nationality,
        roomIndex: 0
      },
      ...bookingForm.additionalGuests.map((guest, index) => ({
        firstName: guest.firstName,
        lastName: guest.lastName,
        roomIndex: 0
      }))
    ]

    const bookingRequest: BookingRequest = {
      ratePkgId: bookingParams.ratePkgId,
      guests: guests,
      booker: {
        firstName: bookingForm.primaryGuest.firstName,
        lastName: bookingForm.primaryGuest.lastName,
        email: bookingForm.primaryGuest.email,
        phone: {
          number: bookingForm.primaryGuest.phone
        }
      },
      remarkToHotel: bookingForm.specialRequests
    }

    const response = await bookingApi.createBooking(bookingRequest)

    ElMessage.success('预订成功！')

    // Redirect to order management or confirmation page
    // Use platformOrderId for subsequent order queries (recommended)
    router.push({
      path: '/booking/orders',
      query: {
        orderId: response.supplierOrderId, // Keep for backward compatibility
        platformOrderId: response.platformOrderId // New parameter for better order tracking
      }
    })

  } catch (error) {
    console.error('Booking failed:', error)
    ElMessage.error('预订失败，请重试')
  } finally {
    submitting.value = false
  }
}

const goBack = () => {
  router.back()
}

const showTerms = () => {
  ElMessageBox.alert('这里是预订条款的详细内容...', '预订条款', {
    confirmButtonText: '确定'
  })
}

const showPrivacy = () => {
  ElMessageBox.alert('这里是隐私政策的详细内容...', '隐私政策', {
    confirmButtonText: '确定'
  })
}

// Initialize
onMounted(async () => {
  if (!bookingParams.ratePkgId) {
    ElMessage.error('缺少房价包ID')
    router.push('/booking/hotel-search')
    return
  }

  loading.value = true
  try {
    // Load booking data and check availability
    // Don't use Promise.all to avoid one failure affecting the other
    await loadBookingData()

    try {
      await checkAvailability()
    } catch (availError) {
      console.error('Availability check failed:', availError)
      ElMessage.warning('房间可用性检查失败，请手动检查')
    }

    initializeAdditionalGuests()
  } catch (error) {
    console.error('Failed to initialize booking page:', error)
    ElMessage.error('页面初始化失败，但您仍可以尝试预订')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped lang="scss">
.hotel-book {
  padding: 24px;
  
  .booking-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }
  
  .booking-form {
    .form-section {
      margin-bottom: 24px;
      
      .section-title {
        font-weight: 600;
        color: #303133;
      }
      
      .guest-form {
        margin-bottom: 24px;
        padding: 16px;
        background: #f5f7fa;
        border-radius: 8px;
        
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 16px 0;
        }
      }
      
      .special-options {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-top: 16px;
      }
      
      .card-info {
        margin-top: 16px;
        padding: 16px;
        background: #f5f7fa;
        border-radius: 8px;
      }
      
      .terms-content {
        .el-checkbox {
          display: block;
          margin-bottom: 12px;

          a {
            color: #409EFF;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }

        .terms-item {
          position: relative;
          display: flex;
          align-items: flex-start;
          gap: 8px;

          &.has-error {
            .el-checkbox {
              color: #f56c6c;
            }
          }

          .field-error-message {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 4px;
            line-height: 1.4;
          }
        }
      }

      .payment-method-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      margin-top: 32px;
    }

    // 验证状态样式
    .validation-icon {
      font-size: 16px;

      &.success {
        color: #67c23a;
      }

      &.error {
        color: #f56c6c;
      }

      &.warning {
        color: #e6a23c;
      }
    }

    .field-success {
      .el-input__wrapper {
        border-color: #67c23a;
        box-shadow: 0 0 0 1px #67c23a inset;
      }
    }

    .field-error {
      .el-input__wrapper {
        border-color: #f56c6c;
        box-shadow: 0 0 0 1px #f56c6c inset;
      }
    }

    .validation-summary {
      .summary-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: #fef0f0;
        border: 1px solid #fbc4c4;
        border-radius: 8px;

        .summary-icon {
          font-size: 20px;
          margin-top: 2px;

          &.warning {
            color: #e6a23c;
          }
        }

        .summary-text {
          flex: 1;

          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #f56c6c;
          }

          p {
            margin: 0;
            font-size: 13px;
            color: #606266;
            line-height: 1.4;
          }
        }
      }
    }
  }
  
  .booking-summary {
    position: sticky;
    top: 24px;
    
    .summary-content {
      .hotel-summary {
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        p {
          color: #606266;
          margin: 0 0 8px 0;
        }
      }
      
      .room-summary {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        p {
          color: #606266;
          margin: 0 0 8px 0;
        }
        
        .room-features {
          display: flex;
          gap: 6px;
        }
      }
      
      .stay-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          span:first-child {
            color: #606266;
          }
          
          span:last-child {
            color: #303133;
            font-weight: 500;
          }
        }
      }
      
      .price-breakdown {
        .price-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          &.total {
            font-weight: 600;
            font-size: 16px;
            color: #E6A23C;
            border-top: 1px solid #ebeef5;
            padding-top: 8px;
            margin-top: 8px;
          }
        }
      }
      
      .availability-check {
        margin: 16px 0;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        text-align: center;

        .availability-status {
          margin: 8px 0 0 0;
          font-size: 14px;

          .available {
            color: #67c23a;
            font-weight: 600;
          }

          .unavailable {
            color: #f56c6c;
            font-weight: 600;
          }
        }
      }

      .cancellation-policy {
        margin-top: 16px;

        h5 {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }

        p {
          font-size: 12px;
          color: #606266;
          margin: 0;
          line-height: 1.4;
        }
      }
    }
  }
}
</style>

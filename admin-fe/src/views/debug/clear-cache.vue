<template>
  <div class="clear-cache-page">
    <div class="container">
      <h1>缓存清理工具</h1>
      <p>用于清理前端缓存，解决菜单结构更新后的显示问题</p>
      
      <div class="button-group">
        <el-button type="primary" @click="clearMenuCache" :loading="loading">
          清除菜单缓存
        </el-button>
        <el-button type="warning" @click="clearAllCache" :loading="loading">
          清除所有缓存
        </el-button>
        <el-button type="success" @click="testMenuAPI" :loading="loading">
          测试菜单API
        </el-button>
        <el-button type="info" @click="goToLogin">
          返回登录页
        </el-button>
      </div>
      
      <div class="status-section" v-if="statusMessage">
        <el-alert
          :title="statusMessage"
          :type="statusType"
          :closable="false"
          show-icon
        />
      </div>
      
      <div class="info-section">
        <h3>当前状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前URL">
            {{ currentUrl }}
          </el-descriptions-item>
          <el-descriptions-item label="当前Hash">
            {{ currentHash }}
          </el-descriptions-item>
          <el-descriptions-item label="菜单缓存">
            {{ menuCacheStatus }}
          </el-descriptions-item>
          <el-descriptions-item label="localStorage项目数">
            {{ localStorageCount }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="log-section" v-if="logs.length > 0">
        <h3>操作日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { internalHomepageCache } from '@/utils/cache/internalHomepageCache'
import { menuApiV2 } from '@/api/menuApiV2'

defineOptions({ name: 'ClearCache' })

const router = useRouter()
const loading = ref(false)
const statusMessage = ref('')
const statusType = ref<'success' | 'warning' | 'error' | 'info'>('info')
const logs = ref<Array<{ time: string, message: string }>>([])

const currentUrl = computed(() => window.location.href)
const currentHash = computed(() => window.location.hash)
const localStorageCount = computed(() => Object.keys(localStorage).length)
const menuCacheStatus = computed(() => {
  return internalHomepageCache.isCacheValid() ? '有效' : '无效/不存在'
})

function addLog(message: string) {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, message })
  console.log(`[ClearCache] ${message}`)
}

function showStatus(message: string, type: 'success' | 'warning' | 'error' | 'info' = 'info') {
  statusMessage.value = message
  statusType.value = type
  addLog(message)
}

async function clearMenuCache() {
  loading.value = true
  try {
    addLog('开始清除菜单缓存...')
    
    // 清除内部缓存
    internalHomepageCache.clearInternalHomepageCache()
    addLog('已清除 InternalHomepage 缓存')
    
    // 清除菜单相关的 localStorage
    const menuKeys = Object.keys(localStorage).filter(key => 
      key.includes('menu') || key.includes('pinia') && key.includes('menuStore')
    )
    
    menuKeys.forEach(key => {
      localStorage.removeItem(key)
      addLog(`已清除 localStorage: ${key}`)
    })
    
    // 清除 sessionStorage 中的路由缓存
    sessionStorage.removeItem('iframeRoutes')
    addLog('已清除 sessionStorage: iframeRoutes')
    
    showStatus('菜单缓存清除完成！请刷新页面或重新登录。', 'success')
    ElMessage.success('菜单缓存已清除')
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showStatus(`清除菜单缓存失败: ${errorMessage}`, 'error')
    ElMessage.error('清除菜单缓存失败')
  } finally {
    loading.value = false
  }
}

async function clearAllCache() {
  loading.value = true
  try {
    addLog('开始清除所有缓存...')
    
    // 强制清除所有缓存
    internalHomepageCache.forceRefreshAll()
    addLog('已清除所有内部缓存')
    
    // 清除所有 localStorage
    const allKeys = Object.keys(localStorage)
    allKeys.forEach(key => {
      localStorage.removeItem(key)
      addLog(`已清除 localStorage: ${key}`)
    })
    
    // 清除所有 sessionStorage
    const sessionKeys = Object.keys(sessionStorage)
    sessionKeys.forEach(key => {
      sessionStorage.removeItem(key)
      addLog(`已清除 sessionStorage: ${key}`)
    })
    
    showStatus('所有缓存清除完成！请刷新页面或重新登录。', 'success')
    ElMessage.success('所有缓存已清除')
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showStatus(`清除所有缓存失败: ${errorMessage}`, 'error')
    ElMessage.error('清除所有缓存失败')
  } finally {
    loading.value = false
  }
}

async function testMenuAPI() {
  loading.value = true
  try {
    addLog('开始测试菜单API...')
    
    // 强制刷新菜单数据
    const result = await menuApiV2.forceRefreshAll()
    addLog(`菜单API测试成功，获取到 ${result.menuList.length} 个菜单项`)
    
    // 检查关键菜单项
    const systemMenu = result.menuList.find(menu => menu.path === '/system')
    const logsMenu = result.menuList.find(menu => menu.path === '/logs')
    
    if (systemMenu) {
      addLog(`✓ System菜单存在: ${systemMenu.path}`)
    } else {
      addLog('✗ System菜单不存在')
    }
    
    if (logsMenu) {
      addLog(`✓ Logs菜单存在: ${logsMenu.path}`)
    } else {
      addLog('✗ Logs菜单不存在')
    }
    
    showStatus('菜单API测试完成！', 'success')
    ElMessage.success('菜单API测试成功')
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showStatus(`菜单API测试失败: ${errorMessage}`, 'error')
    ElMessage.error('菜单API测试失败')
  } finally {
    loading.value = false
  }
}

function goToLogin() {
  router.push('/auth/login')
}

onMounted(() => {
  addLog('缓存清理工具已加载')
  showStatus('工具已就绪，可以开始清理缓存', 'info')
})
</script>

<style scoped>
.clear-cache-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #303133;
  margin-bottom: 10px;
}

p {
  color: #606266;
  margin-bottom: 30px;
}

.button-group {
  margin-bottom: 30px;
}

.button-group .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.status-section {
  margin-bottom: 30px;
}

.info-section {
  margin-bottom: 30px;
}

.info-section h3 {
  color: #303133;
  margin-bottom: 15px;
}

.log-section h3 {
  color: #303133;
  margin-bottom: 15px;
}

.log-container {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  color: #303133;
}
</style>

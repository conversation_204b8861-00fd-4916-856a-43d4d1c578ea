import { asyncRoutes } from '@/router/routes/asyncRoutes'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'
import request from '@/utils/http'

interface MenuRoute {
  id: string
  path: string
  component: string
  name: string
  title: {
    zh: string
    en: string
    ar?: string
  }
  icon?: string
  meta?: {
    requiresAuth?: boolean
    roles?: string[]
    permissions?: string[]
    keepAlive?: boolean
    isHide?: boolean
    activePath?: string
  }
  children?: MenuRoute[]
}

interface MenuResponse {
  menuList: AppRouteRecord[]
}

// 转换 MenuRoute 为 AppRouteRecord
function convertMenuRoutesToAppRoutes(routes: MenuRoute[], parentPath = ''): AppRouteRecord[] {
  return routes.map(route => {
    // 构建完整路径
    const fullPath = parentPath ? `${parentPath}/${route.path}`.replace(/\/+/g, '/') : route.path

    const appRoute: AppRouteRecord = {
      path: fullPath,
      name: route.name,
      component: route.component,
      meta: {
        title: route.title, // 保持完整的 i18n 对象，让 formatMenuTitle 处理
        icon: route.icon,
        keepAlive: route.meta?.keepAlive,
        roles: route.meta?.roles,
        permissions: route.meta?.permissions,
        isHide: route.meta?.isHide,
        activePath: route.meta?.activePath
      }
    }

    if (route.children && route.children.length > 0) {
      appRoute.children = convertMenuRoutesToAppRoutes(route.children, fullPath)
    }



    return appRoute
  })
}

// 菜单接口
export const menuService = {
  async getMenuList(delay = 300): Promise<MenuResponse> {
    try {
      // 1. 请求后端 AdminHomepage 接口，严格对接协议
      let menuData: AppRouteRecord[] = []
      try {
        // Fallback navigation structure when backend API is unavailable
        // This is kept as a safety mechanism, not for development mocking
        const mockNavigationTabs = [
          {
            id: 'booking',
            title: { zh: '预订', en: 'Booking' },
            subTabs: [
              {
                id: 'hotel-search',
                title: { zh: '酒店搜索', en: 'Hotel Search' }
              },
              {
                id: 'order-management',
                title: { zh: '订单管理', en: 'Order Management' }
              },
              {
                id: 'analytics',
                title: { zh: '预订统计', en: 'Booking Analytics' }
              }
            ]
          },
          {
            id: 'system',
            title: { zh: '系统管理', en: 'System' },
            subTabs: [
              {
                id: 'user-center',
                title: { zh: '个人中心', en: 'User Center' }
              }
            ]
          },
          {
            id: 'dashboard',
            title: { zh: '仪表盘', en: 'Dashboard' },
            subTabs: [
              {
                id: 'console',
                title: { zh: '工作台', en: 'Console' }
              }
            ]
          }
        ]
        // 递归转换 navigationTabs 为 AppRouteRecord，符合动态路由注册规范
        const toCamel = (str: string) =>
          str.replace(/[-_](\w)/g, (_, c) => (c ? c.toUpperCase() : '')).replace(/^(\w)/, (s) => s.toUpperCase())

        const resolvePath = (parent: string, child: string) => [parent.replace(/^\//, '').replace(/\/$/, ''), child].filter(Boolean).join('/')

        function navigationTabsToRoutes(tabs: any[], parentPath = ''): AppRouteRecord[] {
          return tabs.map((tab) => {
            const isRoot = parentPath === ''
            const path = isRoot ? `/${tab.id}` : tab.id
            const fullPathForComponent = isRoot ? '/index/index' : `/${resolvePath(parentPath, tab.id)}/index`

            const route: AppRouteRecord = {
              path,
              name: toCamel(tab.id),
              component: isRoot ? '/index/index' : fullPathForComponent,
              meta: {
                title: tab.title?.zh || tab.title?.en || tab.id
              },
              children: Array.isArray(tab.subTabs) && tab.subTabs.length > 0 ? navigationTabsToRoutes(tab.subTabs, `${isRoot ? '' : parentPath}/${tab.id}`.replace(/\/+/g, '/').replace(/^\//, '')) : []
            }
            return route
          })
        }

        // 尝试调用新的 InternalHomepage 接口
        const res = await request.post<any>({ url: '/api/view/internalHomepage', data: {} })
        if (res?.data?.menuRoutes && Array.isArray(res.data.menuRoutes)) {
          // 使用新的 menuRoutes 结构
          menuData = convertMenuRoutesToAppRoutes(res.data.menuRoutes)
        } else {
          // 回退到旧的 navigationTabs 结构
          const navigationTabs = Array.isArray(res?.data?.navigationTabs) ? res.data.navigationTabs : mockNavigationTabs
          menuData = navigationTabsToRoutes(navigationTabs)
        }
      } catch (e) {
        // fallback to basic navigation structure when API fails
      }
      // 模拟接口延迟
      await new Promise((resolve) => setTimeout(resolve, delay))
      return { menuList: menuData }
    } catch (error) {
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  }
}

import request from '@/utils/http'
import type { BffTable } from '@/types/bff'
import type { Entity, CreateEntityRequest, UpdateEntityRequest } from '@/types/entity'

export interface ListEntityRequest {
  parentEntityIds?: number[]
  page?: {
    pageNum: number
    pageSize: number
  }
}

export interface CreateEntityResponse {
  data: Entity
}

export interface UpdateEntityResponse {
  data: Entity
}

export interface GetEntityResponse {
  data: Entity
}

// Entity API service
export class EntityService {
  /**
   * 获取实体列表
   * 调用后端 /api/user/listEntity 接口
   */
  static async listEntity(params: ListEntityRequest): Promise<BffTable<Entity>> {
    const response = await request.post<BffTable<Entity>>({
      url: '/api/user/listEntity',
      data: params
    })
    // axios拦截器已经返回了data字段，不需要再访问.data
    return response
  }

  /**
   * 创建实体
   * 调用后端 /api/user/createEntity 接口
   */
  static async createEntity(data: CreateEntityRequest): Promise<CreateEntityResponse> {
    const response = await request.post<CreateEntityResponse>({
      url: '/api/user/createEntity',
      data: { data }
    })
    return response
  }

  /**
   * 更新实体
   * 调用后端 /api/user/updateEntity 接口
   */
  static async updateEntity(data: UpdateEntityRequest): Promise<UpdateEntityResponse> {
    const response = await request.post<UpdateEntityResponse>({
      url: '/api/user/updateEntity',
      data: { data }
    })
    return response
  }

  /**
   * 获取单个实体详情
   * 暂时通过listEntity接口获取单个实体
   */
  static async getEntity(id: string): Promise<Entity> {
    // 暂时通过listEntity获取，后续可能需要后端添加getEntity接口
    const table = await this.listEntity({})
    const entity = table.rows.find(row => row.raw.id.toString() === id)?.raw
    if (!entity) {
      throw new Error(`Entity with id ${id} not found`)
    }
    return entity
  }

  /**
   * 删除实体
   * 调用后端 /api/user/deleteEntity 接口
   */
  static async deleteEntity(id: string): Promise<{ success: boolean }> {
    const response = await request.post<{ success: boolean }>({
      url: '/api/user/deleteEntity',
      data: { id }
    })
    return response
  }
}

// 导出默认实例
export const entityApi = {
  listEntity: EntityService.listEntity,
  createEntity: EntityService.createEntity,
  updateEntity: EntityService.updateEntity,
  getEntity: EntityService.getEntity,
  deleteEntity: EntityService.deleteEntity
}

import request from '@/utils/http'
import type { Guest } from './bookingApi'

// Backend data types
export interface TimeWindow {
  start?: string
  end?: string
}

export interface I18N {
  [key: string]: string // language code -> text
}

export interface Money {
  amount: number
  currency: string
}

// BFF Table Protocol types
export interface ElementItem {
  type?: string
  value?: any
  text?: string
  link?: string
  action?: string
  style?: string
}

export interface ElementRow {
  key: string // row id
  columns: ElementItem[] // BFF row data
  raw?: any // original object data
}

export interface BffTableResponse<T = any> {
  rows: ElementRow[]
  headerKeys?: string[] // corresponds to keys in each row element
  total?: number
  hasMore?: boolean
  isRowToColumnEnabled?: boolean // row to column transformation
  styleId?: string // agreed style ID between frontend and backend
}

// Order list request interface - matches backend ListOrderReq
export interface OrderListRequest {
  keyword?: string
  orderIds?: string[]
  tags?: string[]
  createTimeWindow?: TimeWindow
  checkInTimeWindow?: TimeWindow
  checkOutTimeWindow?: TimeWindow
  cancelTimeWindow?: TimeWindow
  page?: number
  pageSize?: number
}

// Order list response interface - matches backend ListOrderResp with BFF Table protocol
export interface OrderListResponse extends BffTableResponse {
  // Inherits BFF Table structure
}

// Order detail request interface - matches backend DetailOrderReq
export interface OrderDetailRequest {
  orderId?: string // Legacy support - will be deprecated
  platformOrderId?: number // New preferred parameter for order detail queries
}

// Order detail response interfaces - matches backend DetailOrderResp
export interface OrderSummary {
  id: string
  status: any // OrderStatus from backend
  createTime?: string
  orderRoom?: number
  rooms?: number
  salesMoney?: Money
  supplierNet?: Money
  refundableMode?: 'fullyRefundable' | 'partiallyRefundable' | ''
  supplier?: any
  tags?: string[]
}

export interface DetailOrderAccount {
  orderAccount: {
    salesCurrency: string
    salesAmount: number
    supplyCurrency: string
    supplyAmount: number
    platformRevenueAmountUsd: number
    tenantRevenueAmountUsd: number
    customerRevenueAmountUsd: number
  }
}

export interface DetailOrderSubOrder {
  hotel: {
    hotelId: number
    hotelName: string
    address?: string
  }
  booking: {
    rows: Array<{
      raw: {
        id: string
        status: number
        createTime: string
      }
      key: string
    }>
  }
  gain: {
    rows: Array<{
      raw: {
        platformRevenue: number
        tenantRevenue: number
        customerRevenue: number
      }
      key: string
    }>
  }
}

export interface OrderDetailResponse {
  summary?: OrderSummary
  account?: DetailOrderAccount
  subOrders?: DetailOrderSubOrder[]
}

// Order detail type alias for convenience
export type OrderDetail = OrderDetailResponse

// Legacy Order interface for backward compatibility
export interface Order {
  orderId: string
  confirmationNumber: string
  status: string
  totalPrice: number
  currency: string
  hotel: {
    id: string
    name: string
    address: string
    city: string
    country: string
    rating: number
    image?: string
  }
  room: {
    id: string
    name: string
    type: string
  }
  checkIn: string
  checkOut: string
  nights: number
  adults: number
  children: number
  rooms: number
  guests: Guest[]
  specialRequests?: string
  cancellationPolicy: string
  createdAt: string
  updatedAt: string
  bookedBy: {
    id: string
    name: string
    email: string
  }
}

// Order statistics interface
export interface OrderStatistics {
  totalOrders: number
  totalRevenue: number
  currency: string
  statusBreakdown: {
    status: string
    count: number
    percentage: number
  }[]
  monthlyStats: {
    month: string
    orders: number
    revenue: number
  }[]
  topHotels: {
    hotelId: string
    hotelName: string
    orders: number
    revenue: number
  }[]
}

// Order API service
export const orderApi = {
  // Get order list for tenants (admin view) - matches backend /api/trade/tenant/listOrder
  async getTenantOrderList(params: OrderListRequest = {}): Promise<OrderListResponse> {
    try {
      const requestData: any = {}

      // Add optional fields
      if (params.keyword) requestData.keyword = params.keyword
      if (params.orderIds) requestData.orderIds = params.orderIds
      if (params.tags) requestData.tags = params.tags
      if (params.createTimeWindow) requestData.createTimeWindow = params.createTimeWindow
      if (params.checkInTimeWindow) requestData.checkInTimeWindow = params.checkInTimeWindow
      if (params.checkOutTimeWindow) requestData.checkOutTimeWindow = params.checkOutTimeWindow
      if (params.cancelTimeWindow) requestData.cancelTimeWindow = params.cancelTimeWindow

      const response = await request.post<OrderListResponse>({
        url: '/api/trade/tenant/listOrder',
        data: requestData
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {
        rows: [],
        total: 0,
        hasMore: false
      }
    } catch (error) {
      console.error('Get tenant order list failed:', error)
      throw new Error('获取订单列表失败')
    }
  },

  // Get order list for customers - matches backend /api/trade/customer/listOrder
  async getCustomerOrderList(params: OrderListRequest = {}): Promise<OrderListResponse> {
    try {
      const requestData: any = {}

      // Add optional fields
      if (params.keyword) requestData.keyword = params.keyword
      if (params.orderIds) requestData.orderIds = params.orderIds
      if (params.tags) requestData.tags = params.tags
      if (params.createTimeWindow) requestData.createTimeWindow = params.createTimeWindow
      if (params.checkInTimeWindow) requestData.checkInTimeWindow = params.checkInTimeWindow
      if (params.checkOutTimeWindow) requestData.checkOutTimeWindow = params.checkOutTimeWindow
      if (params.cancelTimeWindow) requestData.cancelTimeWindow = params.cancelTimeWindow

      const response = await request.post<OrderListResponse>({
        url: '/api/trade/customer/listOrder',
        data: requestData
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {
        rows: [],
        total: 0,
        hasMore: false
      }
    } catch (error) {
      console.error('Get customer order list failed:', error)
      throw new Error('获取订单列表失败')
    }
  },

  // Get tenant order detail - matches backend /api/trade/tenant/detailOrder
  // Updated to support both orderId and platformOrderId parameters
  async getTenantOrderDetail(params: string | OrderDetailRequest): Promise<OrderDetailResponse> {
    try {
      let requestData: any = {}

      if (typeof params === 'string') {
        // Legacy support: if string is passed, treat as orderId
        requestData = { orderId: params }
      } else {
        // New approach: support both orderId and platformOrderId
        if (params.platformOrderId) {
          requestData = { platformOrderId: params.platformOrderId }
        } else if (params.orderId) {
          requestData = { orderId: params.orderId }
        } else {
          throw new Error('Either orderId or platformOrderId must be provided')
        }
      }

      const response = await request.post<OrderDetailResponse>({
        url: '/api/trade/tenant/detailOrder',
        data: requestData
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {}
    } catch (error) {
      console.error('Get tenant order detail failed:', error)
      throw new Error('获取订单详情失败')
    }
  },

  // Get customer order detail - matches backend /api/trade/customer/detailOrder
  // Updated to support both orderId and platformOrderId parameters
  async getCustomerOrderDetail(params: string | OrderDetailRequest): Promise<OrderDetailResponse> {
    try {
      let requestData: any = {}

      if (typeof params === 'string') {
        // Legacy support: if string is passed, treat as orderId
        requestData = { orderId: params }
      } else {
        // New approach: support both orderId and platformOrderId
        if (params.platformOrderId) {
          requestData = { platformOrderId: params.platformOrderId }
        } else if (params.orderId) {
          requestData = { orderId: params.orderId }
        } else {
          throw new Error('Either orderId or platformOrderId must be provided')
        }
      }

      const response = await request.post<OrderDetailResponse>({
        url: '/api/trade/customer/detailOrder',
        data: requestData
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {}
    } catch (error) {
      console.error('Get customer order detail failed:', error)
      throw new Error('获取订单详情失败')
    }
  },

  // Get order detail (generic) - matches backend /api/trade/detailOrder
  // Updated to support both orderId and platformOrderId parameters
  async getOrderDetail(params: string | OrderDetailRequest): Promise<OrderDetailResponse> {
    try {
      let requestData: any = {}

      if (typeof params === 'string') {
        // Legacy support: if string is passed, treat as orderId
        requestData = { orderId: params }
      } else {
        // New approach: support both orderId and platformOrderId
        if (params.platformOrderId) {
          requestData = { platformOrderId: params.platformOrderId }
        } else if (params.orderId) {
          requestData = { orderId: params.orderId }
        } else {
          throw new Error('Either orderId or platformOrderId must be provided')
        }
      }

      const response = await request.post<OrderDetailResponse>({
        url: '/api/trade/detailOrder',
        data: requestData
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {}
    } catch (error) {
      console.error('Get order detail failed:', error)
      throw new Error('获取订单详情失败')
    }
  },

  // Get order statistics
  async getOrderStatistics(startDate?: string, endDate?: string): Promise<OrderStatistics> {
    try {
      // Note: This endpoint might need to be implemented in the backend
      const response = await request.post<any>({
        url: '/api/trade/tenant/orderHomeFunction',
        data: {
          startDate,
          endDate,
          type: 'statistics'
        }
      })
      
      // axios拦截器已经返回了data字段，不需要再访问.data
      return {
        totalOrders: response?.totalOrders || 0,
        totalRevenue: response?.totalRevenue || 0,
        currency: response?.currency || 'USD',
        statusBreakdown: response?.statusBreakdown || [],
        monthlyStats: response?.monthlyStats || [],
        topHotels: response?.topHotels || []
      }
    } catch (error) {
      console.error('Get order statistics failed:', error)
      throw new Error('获取订单统计失败')
    }
  },

  // Export orders
  async exportOrders(params: OrderListRequest = {}): Promise<Blob> {
    try {
      const response = await request.post<Blob>({
        url: '/api/trade/exportOrders',
        data: {
          filters: {
            keyword: params.keyword,
            orderIds: params.orderIds,
            tags: params.tags,
            createTimeWindow: params.createTimeWindow,
            checkInTimeWindow: params.checkInTimeWindow,
            checkOutTimeWindow: params.checkOutTimeWindow,
            cancelTimeWindow: params.cancelTimeWindow
          }
        },
        responseType: 'blob'
      })

      return response
    } catch (error) {
      console.error('Export orders failed:', error)
      throw new Error('导出订单失败')
    }
  },

  // Get order analytics statistics
  async getOrderAnalytics(startDate?: string, endDate?: string): Promise<OrderStatistics> {
    try {
      const response = await request.post<any>({
        url: '/api/trade/statistics',
        data: {
          startDate,
          endDate
        }
      })

      return response
    } catch (error) {
      console.error('Get order statistics failed:', error)
      throw new Error('获取订单统计失败')
    }
  }
}

import request from '@/utils/http'

// Backend data types - matches hotel.supplier.domain
export interface Guest {
  firstName?: string
  lastName?: string
  age?: number // Only matters for children
  mobilePhone?: string
  nationalityCode?: string // ISO 3166-1 alpha-2
  roomIndex?: number // Assigned room index
}

export interface Booker {
  firstName?: string
  lastName?: string
  email?: string
  phone?: {
    countryCode?: string
    number?: string
  }
}

export interface Payment {
  // Payment details - structure to be defined based on requirements
}

export interface OrderStatus {
  // Order status enum values from backend
}

// Booking request interface - matches backend BookReq
export interface BookingRequest {
  ratePkgId: string // Required - from hotel search results
  guests: Guest[] // Required - guest details
  booker?: Booker // Optional - booker information
  payment?: Payment // Optional - payment details
  platformOrderId?: number // Optional - platform order ID
  remarkToHotel?: string // Optional - special requests
}

// Booking response interface - matches backend BookResp
export interface BookingResponse {
  supplierOrderId: string // Required - supplier order ID
  orderStatus: OrderStatus // Required - order status
  platformOrderId: number // Required - platform order ID for subsequent order detail queries
}

// Cancellation request interface - matches backend CancelReq
export interface CancellationRequest {
  orderId: string // Required - order ID to cancel
}

// Cancellation response interface - matches backend CancelResp
export interface CancellationResponse {
  // Response structure to be defined based on backend implementation
}

// Booking API service
export const bookingApi = {
  // Create booking - matches backend /api/trade/book
  async createBooking(params: BookingRequest): Promise<BookingResponse> {
    try {
      const requestData: any = {
        ratePkgId: params.ratePkgId,
        guests: params.guests
      }

      // Add optional fields
      if (params.booker) requestData.booker = params.booker
      if (params.payment) requestData.payment = params.payment
      if (params.platformOrderId) requestData.platformOrderId = params.platformOrderId
      if (params.remarkToHotel) requestData.remarkToHotel = params.remarkToHotel

      const response = await request.post<BookingResponse>({
        url: '/api/trade/book',
        data: requestData
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {
        supplierOrderId: '',
        orderStatus: {} as OrderStatus,
        platformOrderId: 0
      }
    } catch (error) {
      console.error('Create booking failed:', error)
      throw new Error('预订失败')
    }
  },

  // Cancel booking - matches backend /api/trade/cancel
  async cancelBooking(params: CancellationRequest): Promise<CancellationResponse> {
    try {
      const response = await request.post<CancellationResponse>({
        url: '/api/trade/cancel',
        data: {
          orderId: params.orderId
        }
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {}
    } catch (error) {
      console.error('Cancel booking failed:', error)
      throw new Error('取消预订失败')
    }
  },

  // Validate booking data
  validateBookingData(params: BookingRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!params.ratePkgId) {
      errors.push('房价包ID不能为空')
    }

    if (!params.guests || params.guests.length === 0) {
      errors.push('客人信息不能为空')
    } else {
      params.guests.forEach((guest, index) => {
        if (!guest.firstName && !guest.lastName) {
          errors.push(`第${index + 1}位客人姓名不能为空`)
        }
        if (guest.age !== undefined && guest.age < 0) {
          errors.push(`第${index + 1}位客人年龄不能为负数`)
        }
      })
    }

    return {
      valid: errors.length === 0,
      errors
    }
  },

  // Helper function to create order detail request from booking response
  // Use this to query order details after successful booking
  createOrderDetailRequest(bookingResponse: BookingResponse): { platformOrderId: number } {
    return {
      platformOrderId: bookingResponse.platformOrderId
    }
  }
}

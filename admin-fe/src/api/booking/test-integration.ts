/**
 * 预订模块API对接测试文件
 * 用于验证前端API与后端接口的对接是否正确
 * 注意：此文件仅用于真实API测试，不包含任何mock数据
 */

import { hotelSearchApi } from './searchApi'
import { bookingApi } from './bookingApi'
import { orderApi } from './orderApi'

// 测试酒店搜索API
export const testHotelSearch = async () => {
  console.log('Testing hotel search API...')
  
  try {
    const searchParams = {
      regionName: '北京',
      checkIn: '2025-07-10',
      checkOut: '2025-07-12',
      adultCount: 2,
      childrenCount: 0,
      roomCount: 1,
      pageNum: 1,
      pageSize: 10,
      currency: 'USD',
      language: 'zh'
    }
    
    const response = await hotelSearchApi.searchHotels(searchParams)
    console.log('Hotel search response:', response)
    
    if (response.list && response.list.length > 0) {
      console.log('✅ Hotel search API working correctly')
      return response.list[0] // Return first hotel for further testing
    } else {
      console.log('⚠️ Hotel search returned no results')
      return null
    }
  } catch (error) {
    console.error('❌ Hotel search API failed:', error)
    return null
  }
}

// 测试可用性检查API
export const testAvailabilityCheck = async (ratePkgId: string) => {
  console.log('Testing availability check API...')
  
  try {
    const response = await hotelSearchApi.checkAvailability({
      ratePkgId
    })
    
    console.log('Availability check response:', response)

    if (response.status === 1 && response.roomRatePkg) {
      console.log('✅ Availability check API working correctly')
      return response.roomRatePkg
    } else {
      console.log('⚠️ Availability check returned unavailable or no rate data')
      return null
    }
  } catch (error) {
    console.error('❌ Availability check API failed:', error)
    return null
  }
}

// 测试预订API
export const testBooking = async (ratePkgId: string) => {
  console.log('Testing booking API...')
  
  try {
    const bookingParams = {
      ratePkgId,
      guests: [
        {
          firstName: 'Test',
          lastName: 'User',
          nationalityCode: 'CN',
          roomIndex: 1
        }
      ],
      booker: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: {
          countryCode: '+86',
          number: '13800138000'
        }
      },
      remarkToHotel: 'API integration test booking'
    }
    
    const response = await bookingApi.createBooking(bookingParams)
    console.log('Booking response:', response)
    
    if (response.supplierOrderId) {
      console.log('✅ Booking API working correctly')
      return response.supplierOrderId
    } else {
      console.log('⚠️ Booking API returned no order ID')
      return null
    }
  } catch (error) {
    console.error('❌ Booking API failed:', error)
    return null
  }
}

// 测试订单列表API
export const testOrderList = async () => {
  console.log('Testing order list API...')
  
  try {
    const response = await orderApi.getTenantOrderList({
      keyword: 'test'
    })
    
    console.log('Order list response:', response)
    
    if (response.rows) {
      console.log('✅ Order list API working correctly (BFF Table format)')
      return response.rows.length > 0 ? response.rows[0].key : null
    } else {
      console.log('⚠️ Order list returned no data')
      return null
    }
  } catch (error) {
    console.error('❌ Order list API failed:', error)
    return null
  }
}

// 测试订单详情API
export const testOrderDetail = async (orderId: string) => {
  console.log('Testing order detail API...')
  
  try {
    const response = await orderApi.getTenantOrderDetail(orderId)
    console.log('Order detail response:', response)
    
    if (response.summary) {
      console.log('✅ Order detail API working correctly')
      return response
    } else {
      console.log('⚠️ Order detail returned no summary data')
      return null
    }
  } catch (error) {
    console.error('❌ Order detail API failed:', error)
    return null
  }
}

// 测试取消预订API
export const testCancelBooking = async (orderId: string) => {
  console.log('Testing cancel booking API...')
  
  try {
    const response = await bookingApi.cancelBooking({ orderId })
    console.log('Cancel booking response:', response)
    
    console.log('✅ Cancel booking API working correctly')
    return response
  } catch (error) {
    console.error('❌ Cancel booking API failed:', error)
    return null
  }
}

// 运行所有测试
export const runAllTests = async () => {
  console.log('🚀 Starting API integration tests...')
  
  // 1. 测试酒店搜索
  const hotel = await testHotelSearch()
  
  if (hotel && hotel.rooms && hotel.rooms.length > 0) {
    const firstRoom = hotel.rooms[0]
    if (firstRoom.rates && firstRoom.rates.length > 0) {
      const ratePkgId = firstRoom.rates[0].ratePkgId
      
      // 2. 测试可用性检查
      await testAvailabilityCheck(ratePkgId)
      
      // 3. 测试预订（注意：这会创建真实订单，请谨慎使用）
      console.log('⚠️ Booking test skipped to avoid creating real orders')
      // const orderId = await testBooking(ratePkgId)

      // if (orderId) {
      //   await testOrderDetail(orderId)
      //   await testCancelBooking(orderId)
      // }
    }
  }
  
  // 6. 测试订单列表
  const orderKey = await testOrderList()
  
  if (orderKey) {
    // 7. 测试订单详情
    await testOrderDetail(orderKey)
  }
  
  console.log('🏁 API integration tests completed')
}

// 验证API数据结构
export const validateApiStructures = () => {
  console.log('🔍 Validating API data structures...')
  
  // 验证搜索请求结构
  const searchRequest = {
    regionName: 'Beijing',
    checkIn: '2025-07-10',
    checkOut: '2025-07-12',
    adultCount: 2,
    roomCount: 1
  }
  
  console.log('Search request structure:', searchRequest)
  
  // 验证预订请求结构
  const bookingRequest = {
    ratePkgId: 'test-rate-pkg-id',
    guests: [
      {
        firstName: 'John',
        lastName: 'Doe',
        nationalityCode: 'US'
      }
    ]
  }
  
  console.log('Booking request structure:', bookingRequest)
  
  console.log('✅ API structures validated')
}

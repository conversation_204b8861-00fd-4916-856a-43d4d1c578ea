import request from '@/utils/http'

// Backend data types
export interface DateInt {
  // Format: 20060102 (YYYYMMDD)
  value: number
}

export interface I18N {
  [key: string]: string // language code -> text
}

export interface Money {
  amount: number
  currency: string
}

export interface Latlng {
  lat: number
  lng: number
}

export interface LatlngCoordinator {
  google?: Latlng
  gaode?: Latlng
}

// Geography search types
export interface Region {
  id: number
  name: string
  nameFull: string
  type: string
  countryCode: string
  countrySubdivisionCode?: string
  expediaId?: string
  tripId?: string
  coordinates?: any
  ancestors?: Region[]
  descendants?: Region[]
  extra?: any
}

export interface SearchContext {
  regionId?: string
}

export interface SearchItem {
  type: 'city' | 'place' | 'hotel'
  region?: Region
  place?: {
    latlngCoordinator?: LatlngCoordinator
  }
  hotel?: any
}

// City search request/response
export interface CitySearchRequest {
  keyword: string
  context?: SearchContext
}

export interface CitySearchResponse {
  candidates: SearchItem[]
}

// City search history item for frontend memory
export interface CitySearchHistoryItem {
  regionId: string
  name: string
  nameFull: string
  countryCode: string
  type: string
  searchedAt: number // timestamp
}

export interface Guest {
  firstName?: string
  lastName?: string
  age?: number
  mobilePhone?: string
  nationalityCode?: string
  roomIndex?: number
}

// Hotel search request interface - matches backend HotelListReq
export interface HotelSearchRequest {
  regionName?: string // destination city/region name
  regionId?: string // optional region ID (string to avoid JS big number issues)
  checkIn: string // Will be converted to DateInt format
  checkOut: string // Will be converted to DateInt format
  adultCount?: number // default 1
  childrenCount?: number
  roomCount?: number // default 1
  guests?: Guest[]
  countryCode?: string // ISO 3166-1 alpha-2
  nationalityCode?: string // ISO 3166-1 alpha-2
  residencyCode?: string // ISO 3166-1 alpha-2
  hotelIds?: number[]
  requireRooms?: boolean
  pageNum?: number
  pageSize?: number
  price?: {
    lowPrice?: number
    highPrice?: number
  }
  distance?: {
    latlng?: LatlngCoordinator
    radius?: number
  }
  internalSuppliers?: number[] // Supplier filter for internal platform
  currency?: string
  language?: string

  // Frontend-only fields
  destination?: string
  adults?: number
  children?: number
  rooms?: number
  onlyAvailableSuppliers?: boolean
}

// Hotel search response interface - matches backend HotelListResp
export interface Hotel {
  id: number
  name: I18N
  address: I18N
  citySummary?: {
    city: {
      id: number
      name: I18N
    }
    country: {
      id: number
      name: I18N
    }
  }
  rating?: number
  star?: number
  latlngCoordinator?: LatlngCoordinator
  minPrice?: Money
  hotelPictures?: any[]
  hotelFacilities?: any[]
  rooms?: Room[]
  isAvailable?: boolean
  numOfReviews?: number
  openYear?: number
  fitmentYear?: number
  phone?: string
  email?: string
  fax?: string
}

// 前端显示用的酒店接口（经过多语言处理后的数据）
export interface HotelDisplay {
  id: string
  name: string
  address: string
  city: string
  country: string
  rating: number
  images: any[]
  amenities: string[]
  description: string
  location: {
    latitude: number
    longitude: number
  }
  minPrice: number
  currency: string
}

export interface HotelSearchResponse {
  list: Hotel[]
  total: number
  hasMore: boolean
  basic?: any
}

// Room and rate interfaces - matches backend Room structure
export interface Room {
  hotelId: number
  roomTypeId: string
  roomName: I18N
  roomDesc?: I18N
  bedDesc?: I18N
  bedType?: number // 0: no preference, 1: single, 2: double, 3: twin
  bedCount?: number
  occupancy?: number
  roomArea?: string
  smoking?: boolean
  images?: string[]
  isAvailable?: boolean
  rates?: RoomRatePkg[]
  profile?: Record<string, any>
  masterRoomTypeId?: number
  supplierHotelId?: string
}

export interface RoomRatePkg {
  ratePkgId: string
  ratePlanId?: string
  available: boolean
  rate?: {
    amount: number
    currency: string
  }
  tax?: any
  meal?: any
  cancelPolicy?: any
  payment?: any[]
  amenities?: any[]
  bedTypes?: any[]
  occupancy?: {
    adultsCount: number
    childAges?: number[]
  }
  inventory?: number
  dailyRates?: any[]
  timestamp?: number
}

export interface HotelRatesResponse {
  hotelId: number
  rooms: Room[]
}

// Availability check interfaces - matches backend CheckAvailReq/Resp
export interface AvailabilityRequest {
  ratePkgId: string // Required - from hotel search results
  hotelId?: number // Optional - for observation
  roomId?: number // Optional - for observation
}

// Backend returns supplier/domain.CheckAvailResp structure
export interface AvailabilityResponse {
  status: number // CheckAvailStatus: 1=Available, 2=Unavailable
  roomRatePkg?: RoomRatePkg
}

// Utility functions
const formatDateToInt = (dateStr: string): number => {
  // Convert YYYY-MM-DD to YYYYMMDD format
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return parseInt(`${year}${month}${day}`)
}

// Hotel search API service
export const hotelSearchApi = {
  // Search cities by keyword - matches backend /api/search
  async searchCities(params: CitySearchRequest): Promise<CitySearchResponse> {
    try {
      const requestData: any = {
        keyword: params.keyword
      }

      // Add optional context
      if (params.context) {
        requestData.context = params.context
      }

      const response = await request.post<CitySearchResponse>({
        url: '/api/search/anything',
        data: requestData
      })

      console.log('Processed response from axios interceptor:', response)

      // 由于axios响应拦截器已经处理了 { code, data, msg } 格式
      // 直接返回response，它已经是 { candidates: [...] } 格式
      return response || {
        candidates: []
      }
    } catch (error) {
      console.error('City search failed:', error)
      throw new Error('城市搜索失败')
    }
  },

  // Search hotels
  async searchHotels(params: HotelSearchRequest): Promise<HotelSearchResponse> {
    try {
      const requestData: any = {
        checkIn: formatDateToInt(params.checkIn),
        checkOut: formatDateToInt(params.checkOut),
        adultCount: params.adultCount || 1,
        childrenCount: params.childrenCount || 0,
        roomCount: params.roomCount || 1,
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 20
      }

      // Add optional fields
      if (params.regionName) requestData.regionName = params.regionName
      if (params.regionId) requestData.regionId = params.regionId // Keep as string for big number safety
      if (params.guests) requestData.guests = params.guests
      if (params.countryCode) requestData.countryCode = params.countryCode
      if (params.nationalityCode) requestData.nationalityCode = params.nationalityCode
      if (params.residencyCode) requestData.residencyCode = params.residencyCode
      if (params.hotelIds) requestData.hotelIds = params.hotelIds
      if (params.requireRooms !== undefined) requestData.requireRooms = params.requireRooms
      if (params.price) requestData.price = params.price
      if (params.distance) requestData.distance = params.distance
      if (params.internalSuppliers) requestData.internalSuppliers = params.internalSuppliers

      const response = await request.post<HotelSearchResponse>({
        url: '/api/search/hotelList',
        data: requestData,
        headers: {
          'Currency': params.currency || 'USD',
          'Language': params.language || 'en'
        }
      })

      console.log('Hotel search API response:', response)

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || {
        list: [],
        total: 0,
        hasMore: false
      }
    } catch (error) {
      console.error('Hotel search failed:', error)
      throw new Error('酒店搜索失败')
    }
  },

  // Get hotel rates
  async getHotelRates(hotelId: number, params: HotelSearchRequest): Promise<HotelRatesResponse> {
    try {
      const requestData = {
        hotelId,
        checkIn: formatDateToInt(params.checkIn),
        checkOut: formatDateToInt(params.checkOut),
        adultCount: params.adultCount || 1,
        childrenCount: params.childrenCount || 0,
        roomCount: params.roomCount || 1
      }

      if (params.guests) requestData.guests = params.guests
      if (params.countryCode) requestData.countryCode = params.countryCode
      if (params.nationalityCode) requestData.nationalityCode = params.nationalityCode
      if (params.residencyCode) requestData.residencyCode = params.residencyCode

      const response = await request.post<any>({
        url: '/api/search/hotelRates',
        data: requestData,
        headers: {
          'Currency': params.currency || 'USD',
          'Language': params.language || 'en'
        }
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return {
        hotelId,
        rooms: response?.rooms || []
      }
    } catch (error) {
      console.error('Get hotel rates failed:', error)
      throw error
    }
  },

  // Get hotel detail
  async getHotelDetail(hotelId: string): Promise<Hotel> {
    try {
      const response = await request.post<any>({
        url: '/api/search/hotelStaticDetail',
        data: {
          hotelId
        }
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      if (!response?.hotel) {
        throw new Error('酒店详情数据为空')
      }

      return response.hotel
    } catch (error) {
      console.error('Get hotel detail failed:', error)
      throw error
    }
  },

  // Check availability
  async checkAvailability(params: AvailabilityRequest, searchParams?: HotelSearchRequest): Promise<AvailabilityResponse> {
    try {
      const requestData: any = {
        ratePkgId: params.ratePkgId
      }

      // Add optional fields
      if (params.hotelId) requestData.hotelId = params.hotelId
      if (params.roomId) requestData.roomId = params.roomId

      // Prepare headers similar to other search APIs
      const headers: Record<string, string> = {}
      if (searchParams?.currency) headers['Currency'] = searchParams.currency
      if (searchParams?.language) headers['Language'] = searchParams.language

      const response = await request.post<AvailabilityResponse>({
        url: '/api/search/checkAvail',
        data: requestData,
        headers
      })

      // axios拦截器已经返回了data字段，不需要再访问.data
      return response || { rate: undefined }
    } catch (error) {
      console.error('Check availability failed:', error)
      throw new Error('检查可用性失败')
    }
  }
}

/**
 * Test file for the new platformOrderId functionality
 * This file demonstrates how to use the updated booking and order APIs
 */

import { bookingApi, orderApi } from '@/api/booking'
import type { BookingRequest, BookingResponse } from './bookingApi'
import type { OrderDetailRequest } from './orderApi'

// Test the complete booking flow with platformOrderId
export const testBookingFlowWithPlatformOrderId = async () => {
  console.log('🧪 Testing booking flow with platformOrderId...')

  try {
    // 1. Create a test booking request
    const bookingRequest: BookingRequest = {
      ratePkgId: 'test-rate-pkg-123',
      guests: [
        {
          firstName: 'John',
          lastName: 'Doe',
          nationalityCode: 'US',
          roomIndex: 1
        }
      ],
      booker: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: {
          countryCode: '+1',
          number: '1234567890'
        }
      },
      remarkToHotel: 'Test booking for platformOrderId functionality'
    }

    console.log('📝 Booking request:', bookingRequest)

    // 2. Create the booking
    const bookingResponse: BookingResponse = await bookingApi.createBooking(bookingRequest)
    
    console.log('✅ Booking created successfully:', {
      supplierOrderId: bookingResponse.supplierOrderId,
      orderStatus: bookingResponse.orderStatus,
      platformOrderId: bookingResponse.platformOrderId // New field
    })

    // 3. Test the helper function
    const detailRequest = bookingApi.createOrderDetailRequest(bookingResponse)
    console.log('🔧 Helper function result:', detailRequest)

    // 4. Query order detail using platformOrderId (recommended approach)
    console.log('🔍 Querying order detail using platformOrderId...')
    const orderDetailByPlatformId = await orderApi.getTenantOrderDetail({
      platformOrderId: bookingResponse.platformOrderId
    })
    console.log('📋 Order detail (by platformOrderId):', orderDetailByPlatformId)

    // 5. Query order detail using orderId (legacy approach)
    console.log('🔍 Querying order detail using orderId (legacy)...')
    const orderDetailByOrderId = await orderApi.getTenantOrderDetail(bookingResponse.supplierOrderId)
    console.log('📋 Order detail (by orderId):', orderDetailByOrderId)

    // 6. Test different API methods with platformOrderId
    console.log('🔍 Testing customer order detail API...')
    const customerOrderDetail = await orderApi.getCustomerOrderDetail({
      platformOrderId: bookingResponse.platformOrderId
    })
    console.log('📋 Customer order detail:', customerOrderDetail)

    console.log('🔍 Testing generic order detail API...')
    const genericOrderDetail = await orderApi.getOrderDetail({
      platformOrderId: bookingResponse.platformOrderId
    })
    console.log('📋 Generic order detail:', genericOrderDetail)

    return {
      bookingResponse,
      orderDetailByPlatformId,
      orderDetailByOrderId,
      customerOrderDetail,
      genericOrderDetail
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    throw error
  }
}

// Test backward compatibility
export const testBackwardCompatibility = async (existingOrderId: string) => {
  console.log('🧪 Testing backward compatibility with existing orderId...')

  try {
    // Test that old API calls still work
    const orderDetail1 = await orderApi.getTenantOrderDetail(existingOrderId)
    console.log('✅ Legacy string parameter works:', orderDetail1)

    const orderDetail2 = await orderApi.getTenantOrderDetail({ orderId: existingOrderId })
    console.log('✅ Legacy object parameter works:', orderDetail2)

    const orderDetail3 = await orderApi.getOrderDetail(existingOrderId)
    console.log('✅ Generic API with string parameter works:', orderDetail3)

    const orderDetail4 = await orderApi.getOrderDetail({ orderId: existingOrderId })
    console.log('✅ Generic API with object parameter works:', orderDetail4)

    return {
      orderDetail1,
      orderDetail2,
      orderDetail3,
      orderDetail4
    }

  } catch (error) {
    console.error('❌ Backward compatibility test failed:', error)
    throw error
  }
}

// Test error handling
export const testErrorHandling = async () => {
  console.log('🧪 Testing error handling...')

  try {
    // Test with empty request object
    await orderApi.getTenantOrderDetail({})
    console.log('❌ Should have thrown an error for empty request')
  } catch (error) {
    console.log('✅ Correctly threw error for empty request:', error.message)
  }

  try {
    // Test with invalid platformOrderId
    await orderApi.getTenantOrderDetail({ platformOrderId: -1 })
    console.log('❌ Should have thrown an error for invalid platformOrderId')
  } catch (error) {
    console.log('✅ Correctly handled invalid platformOrderId:', error.message)
  }

  try {
    // Test with non-existent orderId
    await orderApi.getTenantOrderDetail({ orderId: 'non-existent-order' })
    console.log('❌ Should have thrown an error for non-existent orderId')
  } catch (error) {
    console.log('✅ Correctly handled non-existent orderId:', error.message)
  }
}

// Utility function to demonstrate the migration path
export const demonstrateMigrationPath = () => {
  console.log('📚 Migration Path Demonstration:')
  console.log('')
  console.log('1. OLD WAY (still supported):')
  console.log('   const orderDetail = await orderApi.getTenantOrderDetail("order123")')
  console.log('')
  console.log('2. NEW WAY (recommended):')
  console.log('   // After booking:')
  console.log('   const bookingResponse = await bookingApi.createBooking(request)')
  console.log('   const orderDetail = await orderApi.getTenantOrderDetail({')
  console.log('     platformOrderId: bookingResponse.platformOrderId')
  console.log('   })')
  console.log('')
  console.log('3. HELPER FUNCTION:')
  console.log('   const detailRequest = bookingApi.createOrderDetailRequest(bookingResponse)')
  console.log('   const orderDetail = await orderApi.getTenantOrderDetail(detailRequest)')
  console.log('')
}

// Export all test functions
export default {
  testBookingFlowWithPlatformOrderId,
  testBackwardCompatibility,
  testErrorHandling,
  demonstrateMigrationPath
}

import request from '@/utils/http'
import type {
  QueryLogsParams,
  QueryLogsResponse,
  GetLogDetailParams,
  GetLogDetailResponse,
  ExportLogsParams,
  ExportLogsResponse
} from './types'

/**
 * 日志查看器 API
 */
export const logViewerApi = {
  /**
   * 查询日志列表
   */
  async queryLogs(params: QueryLogsParams): Promise<QueryLogsResponse> {
    return request.post({
      url: '/api/logViewer/queryLogs',
      data: params
    })
  },

  /**
   * 获取日志详情
   */
  async getLogDetail(params: GetLogDetailParams): Promise<GetLogDetailResponse> {
    return request.post({
      url: '/api/logViewer/getLogDetail',
      data: params
    })
  },

  /**
   * 导出日志
   */
  async exportLogs(params: ExportLogsParams): Promise<ExportLogsResponse> {
    return request.post({
      url: '/api/logViewer/exportLogs',
      data: params
    })
  },

  /**
   * 获取日志统计
   */
  async getLogStatistics(params: GetLogStatisticsParams): Promise<GetLogStatisticsResponse> {
    return request.post({
      url: '/api/logViewer/getLogStatistics',
      data: params
    })
  }
}

export * from './types'

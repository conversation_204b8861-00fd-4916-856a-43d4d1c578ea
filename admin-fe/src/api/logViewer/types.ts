/**
 * 日志查看器相关类型定义
 */

// 查询日志参数
export interface QueryLogsParams {
  // 分页
  page: number
  pageSize: number
  
  // 时间范围
  startTime?: string
  endTime?: string
  
  // 基础筛选
  logId?: string
  sessionId?: string
  userId?: number
  sellerEntityId?: number
  buyerEntityId?: number
  
  // API 相关
  apiInPath?: string
  apiOutSupplier?: string
  apiOutPath?: string
  
  // 业务相关
  supplierBizId?: string
  supplierBizType?: string
  bizErrorCode?: string
  
  // 请求/响应相关
  inputHeaderKey?: string
  inputHeaderValue?: string
  inputCredentialKey?: string
  inputCredentialValue?: string
  inputBodyKeyword?: string
  outputBodyKeyword?: string
  outputHeaderKey?: string
  outputHeaderValue?: string
  outputHttpStatusCode?: number
  
  // 性能相关
  costTimeMin?: string
  costTimeMax?: string
  
  // 排序
  sortBy?: string
  sortOrder?: string
}

// 日志条目（列表展示）
export interface LogItem {
  timestamp: string
  logId: string
  sessionId: string
  userId: number
  sellerEntityId: number
  buyerEntityId: number
  
  // API 信息
  apiInPath: string
  apiOutSupplier: string
  apiOutPath: string
  
  // 业务信息
  supplierBizId: string
  supplierBizType: string
  
  // 状态信息
  httpStatusCode: number
  bizErrorCode: string
  costTime: number // 毫秒
  
  // 请求/响应大小
  inputBodySize: number
  outputBodySize: number
  
  // 快速预览
  inputPreview: string
  outputPreview: string
  
  // 状态标识
  status: 'success' | 'warning' | 'error'
}

// 查询日志响应
export interface QueryLogsResponse {
  logs: LogItem[]
  total: number
  page: number
  size: number
}

// 获取日志详情参数
export interface GetLogDetailParams {
  logId: string
}

// 日志输入详情
export interface LogInput {
  header?: Record<string, string>
  body?: string
  bodySize: number
  credential?: Record<string, string>
}

// 日志输出详情
export interface LogOutput {
  header?: Record<string, string>
  body?: string
  bodySize: number
  costTime: number // 毫秒
  internalCostTime: number // 毫秒
  httpStatusCode: number
}

// 业务错误详情
export interface LogBizError {
  code: number
  message: string
}

// 日志详情
export interface LogDetail extends LogItem {
  // 完整的请求/响应数据
  input?: LogInput
  output?: LogOutput
  
  // 自定义键值对
  kvs?: Record<string, any>
  
  // 错误详情
  bizError?: LogBizError
}

// 获取日志详情响应
export interface GetLogDetailResponse {
  log: LogDetail
}

// 导出日志参数
export interface ExportLogsParams extends QueryLogsParams {
  // 导出格式
  format: 'csv' | 'json' | 'excel'
  
  // 导出字段（CSV格式时使用）
  fields?: string[]
}

// 导出日志响应
export interface ExportLogsResponse {
  downloadUrl: string
  fileName: string
  fileSize: number
}

// 获取日志统计参数
export interface GetLogStatisticsParams {
  // 时间范围
  startTime?: string
  endTime?: string

  // 统计维度
  groupBy?: 'hour' | 'day' | 'supplier' | 'api'
}

// 分组统计
export interface GroupStat {
  name: string
  count: number
  rate: number // 百分比
}

// 趋势点
export interface TrendPoint {
  time: string
  totalCount: number
  errorCount: number
  errorRate: number
  avgCostTime: number
}

// 获取日志统计响应
export interface GetLogStatisticsResponse {
  totalCount: number
  successCount: number
  warningCount: number
  errorCount: number
  avgCostTime: number // 毫秒

  // 分组统计
  groupStats?: GroupStat[]

  // 趋势数据
  trendData?: TrendPoint[]
}

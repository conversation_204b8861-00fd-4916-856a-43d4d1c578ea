import request from '@/utils/http'
import type {BffTable} from '@/types/bff'

export class UserService {
    // 登录
    static async login(params: { email: string; password: string; ttl?: number }) {
        // 只返回 data 字段，保证 loginRes.token 可用
        return await request.post<{ token: string; user: any }>({
            url: '/api/auth/login',
            data: params
        })
    }

    // 获取用户信息
    static getUserInfo() {
        return request.get<Api.User.UserInfo>({
            url: '/api/user/info'
        })
    }

    // 获取用户详情（POST /api/user/getUser）
    static getUser(key: string) {
        return request.post<any>({
            url: '/api/user/getUser',
            data: {key}
        })
    }

    // 更新用户信息（POST /api/user/updateUser）
    static updateUser(data: any) {
        return request.post<any>({
            url: '/api/user/updateUser',
            data
        })
    }

    // 获取业务(tenant)用户列表
    static listTenantUsers(data: any) {
        return request.post<BffTable<any>>({
            url: '/api/user/tenant/listUser',
            data
        })
    }

    // 获取客户(customer)用户列表
    static listCustomerUsers(data: any) {
        return request.post<BffTable<any>>({
            url: '/api/user/customer/listUser',
            data
        })
    }

    static listPlatformUsers(data: any) {
        return request.post<BffTable<any>>({
            url: '/api/user/platform/listUser',
            data
        })
    }

    // 邀请业务(tenant)用户
    static inviteTenantUser(data: any) {
        return request.post<any>({
            url: '/api/user/tenant/inviteUser',
            data
        })
    }

    // 邀请客户(customer)用户
    static inviteCustomerUser(data: any) {
        return request.post<any>({
            url: '/api/user/customer/inviteUser',
            data
        })
    }

}

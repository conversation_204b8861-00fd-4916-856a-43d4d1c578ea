describe('Booking Flow E2E Test', () => {
  beforeEach(() => {
    // Login first
    cy.visit('/login')
    cy.get('[data-cy="username"]').type('<EMAIL>')
    cy.get('[data-cy="password"]').type('password123')
    cy.get('[data-cy="login-button"]').click()
    
    // Wait for login to complete
    cy.url().should('not.include', '/login')
  })

  it('should complete the full booking flow', () => {
    // Step 1: Navigate to hotel search
    cy.visit('/booking/hotel-search')
    cy.get('h1').should('contain', '酒店搜索')

    // Step 2: Fill search form
    cy.get('[data-cy="destination-input"]').type('上海')
    cy.get('[data-cy="checkin-date"]').click()
    // Select tomorrow as check-in date
    cy.get('.el-date-picker__header-label').first().click()
    cy.get('.el-date-table td.available').first().click()
    
    cy.get('[data-cy="checkout-date"]').click()
    // Select day after tomorrow as check-out date
    cy.get('.el-date-table td.available').eq(1).click()
    
    cy.get('[data-cy="adults-select"]').select('2')
    cy.get('[data-cy="children-select"]').select('0')
    cy.get('[data-cy="rooms-select"]').select('1')

    // Step 3: Search hotels
    cy.get('[data-cy="search-button"]').click()
    cy.get('[data-cy="loading"]').should('not.exist')
    
    // Wait for search results
    cy.get('[data-cy="hotel-list"]').should('be.visible')
    cy.get('[data-cy="hotel-item"]').should('have.length.greaterThan', 0)

    // Step 4: Select a hotel
    cy.get('[data-cy="hotel-item"]').first().within(() => {
      cy.get('[data-cy="view-details"]').click()
    })

    // Step 5: Hotel detail page
    cy.url().should('include', '/booking/hotel-search/detail')
    cy.get('[data-cy="hotel-name"]').should('be.visible')
    cy.get('[data-cy="room-list"]').should('be.visible')

    // Step 6: Select a room and book
    cy.get('[data-cy="room-item"]').first().within(() => {
      cy.get('[data-cy="book-room"]').click()
    })

    // Step 7: Booking form page
    cy.url().should('include', '/booking/hotel-search/book')
    cy.get('h1').should('contain', '完成预订')

    // Step 8: Check availability first
    cy.get('[data-cy="check-availability"]').click()
    cy.get('[data-cy="availability-loading"]').should('not.exist')
    cy.get('[data-cy="availability-status"]').should('contain', '房间可预订')

    // Step 9: Fill booking form
    cy.get('[data-cy="first-name"]').type('John')
    cy.get('[data-cy="last-name"]').type('Doe')
    cy.get('[data-cy="email"]').type('<EMAIL>')
    cy.get('[data-cy="phone"]').type('1234567890')
    cy.get('[data-cy="nationality"]').select('US')

    // Step 10: Fill payment information
    cy.get('[data-cy="payment-method"]').select('credit_card')
    cy.get('[data-cy="card-number"]').type('****************')
    cy.get('[data-cy="card-holder"]').type('John Doe')
    cy.get('[data-cy="card-expiry"]').type('12/25')
    cy.get('[data-cy="card-cvv"]').type('123')

    // Step 11: Agree to terms and submit
    cy.get('[data-cy="agree-terms"]').check()
    cy.get('[data-cy="submit-booking"]').click()

    // Step 12: Verify booking success
    cy.get('[data-cy="booking-success"]').should('be.visible')
    cy.url().should('include', '/booking/order-management')

    // Step 13: Verify order appears in order list
    cy.get('[data-cy="order-list"]').should('be.visible')
    cy.get('[data-cy="order-item"]').should('have.length.greaterThan', 0)

    // Step 14: View order details
    cy.get('[data-cy="order-item"]').first().within(() => {
      cy.get('[data-cy="view-details"]').click()
    })

    // Step 15: Verify order detail page
    cy.url().should('include', '/booking/order-management/detail/')
    cy.get('h1').should('contain', '订单详情')
    cy.get('[data-cy="order-summary"]').should('be.visible')
    cy.get('[data-cy="account-info"]').should('be.visible')

    // Step 16: Test cancel order (optional)
    cy.get('[data-cy="cancel-order"]').should('be.visible')
    cy.get('[data-cy="cancel-order"]').click()
    cy.get('[data-cy="confirm-cancel"]').click()
    cy.get('[data-cy="cancel-success"]').should('be.visible')
  })

  it('should handle booking validation errors', () => {
    // Navigate to booking page with invalid parameters
    cy.visit('/booking/hotel-search/book')
    
    // Should redirect to search page due to missing parameters
    cy.url().should('include', '/booking/hotel-search')
    cy.get('[data-cy="error-message"]').should('contain', '缺少房价包ID')
  })

  it('should handle unavailable rooms', () => {
    // Test with real backend API - no mock data

    // Navigate to booking page with valid parameters
    cy.visit('/booking/hotel-search/book?ratePkgId=test-rate-pkg-123&hotelId=123&roomId=456')

    // Test real backend response - no mock needed
    // Should show appropriate message based on actual availability
    cy.get('[data-cy="availability-status"]').should('exist')
  })

  it('should handle booking API errors', () => {
    // Mock booking API error
    cy.intercept('POST', '/api/trade/book', {
      statusCode: 500,
      body: {
        code: 500,
        msg: 'Internal server error'
      }
    }).as('bookingError')

    // Navigate to booking page and fill form
    cy.visit('/booking/hotel-search/book?ratePkgId=test-rate-pkg-123&hotelId=123&roomId=456')
    
    // Fill required fields
    cy.get('[data-cy="first-name"]').type('John')
    cy.get('[data-cy="last-name"]').type('Doe')
    cy.get('[data-cy="email"]').type('<EMAIL>')
    cy.get('[data-cy="phone"]').type('1234567890')
    cy.get('[data-cy="nationality"]').select('US')
    cy.get('[data-cy="agree-terms"]').check()

    // Submit booking
    cy.get('[data-cy="submit-booking"]').click()
    
    // Wait for API call
    cy.wait('@bookingError')
    
    // Should show error message
    cy.get('[data-cy="error-message"]').should('contain', '预订失败')
  })

  it('should display order list with proper data', () => {
    // Mock order list API
    cy.intercept('POST', '/api/trade/tenant/listOrder', {
      statusCode: 200,
      body: {
        code: 0,
        data: {
          total: 2,
          rows: [
            {
              key: '12345',
              raw: {
                id: '12345',
                status: 2,
                createTime: '2024-01-15T10:30:00Z',
                salesMoney: {
                  amount: 10000,
                  currency: 'USD'
                },
                confirmationNumber: 'CONF-12345'
              }
            },
            {
              key: '67890',
              raw: {
                id: '67890',
                status: 1,
                createTime: '2024-01-14T15:45:00Z',
                salesMoney: {
                  amount: 15000,
                  currency: 'USD'
                },
                confirmationNumber: 'CONF-67890'
              }
            }
          ]
        }
      }
    }).as('orderList')

    // Navigate to order management
    cy.visit('/booking/order-management')
    
    // Wait for API call
    cy.wait('@orderList')
    
    // Verify order list display
    cy.get('[data-cy="order-list"]').should('be.visible')
    cy.get('[data-cy="order-item"]').should('have.length', 2)
    
    // Verify first order
    cy.get('[data-cy="order-item"]').first().within(() => {
      cy.get('[data-cy="order-id"]').should('contain', '12345')
      cy.get('[data-cy="order-status"]').should('contain', '已确认')
      cy.get('[data-cy="order-amount"]').should('contain', '$100.00')
    })
    
    // Verify second order
    cy.get('[data-cy="order-item"]').eq(1).within(() => {
      cy.get('[data-cy="order-id"]').should('contain', '67890')
      cy.get('[data-cy="order-status"]').should('contain', '待确认')
      cy.get('[data-cy="order-amount"]').should('contain', '$150.00')
    })
  })

  it('should handle order search and filtering', () => {
    // Navigate to order management
    cy.visit('/booking/order-management')
    
    // Test search by order ID
    cy.get('[data-cy="search-order-id"]').type('12345')
    cy.get('[data-cy="search-button"]').click()
    
    // Verify search parameters are sent
    cy.get('[data-cy="order-list"]').should('be.visible')
    
    // Test status filter
    cy.get('[data-cy="status-filter"]').select('confirmed')
    cy.get('[data-cy="search-button"]').click()
    
    // Test date range filter
    cy.get('[data-cy="date-range"]').click()
    cy.get('.el-date-picker__header-label').first().click()
    cy.get('.el-date-table td.available').first().click()
    cy.get('.el-date-table td.available').eq(7).click()
    cy.get('[data-cy="search-button"]').click()
    
    // Test reset
    cy.get('[data-cy="reset-button"]').click()
    cy.get('[data-cy="search-order-id"]').should('have.value', '')
    cy.get('[data-cy="status-filter"]').should('have.value', '')
  })
})

# 酒店预订系统 E2E 测试

本目录包含酒店预订系统的端到端测试，使用 Playwright 测试框架。

## 测试结构

```
tests/e2e/
├── pages/                    # 页面对象模型 (POM)
│   ├── BasePage.ts          # 基础页面类
│   ├── LoginPage.ts         # 登录页面
│   ├── HotelSearchPage.ts   # 酒店搜索页面
│   ├── HotelDetailPage.ts   # 酒店详情页面
│   ├── BookingPage.ts       # 预订页面
│   └── OrderManagementPage.ts # 订单管理页面
├── fixtures/                 # 测试数据
│   └── testData.ts          # 测试用例数据
├── utils/                    # 测试工具
│   └── testHelpers.ts       # 测试辅助函数
├── *.spec.ts                # 测试用例文件
├── global-setup.ts          # 全局设置
├── global-teardown.ts       # 全局清理
└── README.md               # 本文件
```

## 测试覆盖范围

### 1. 登录功能 (`login.spec.ts`)
- ✅ 成功登录管理员账户
- ✅ 显示无效凭据错误
- ✅ 验证必填字段
- ✅ 支持记住我功能
- ✅ 处理网络错误

### 2. 酒店搜索功能 (`hotel-search.spec.ts`)
- ✅ 成功搜索酒店
- ✅ 显示无搜索结果
- ✅ 支持价格筛选
- ✅ 支持评分筛选
- ✅ 支持设施筛选
- ✅ 支持排序功能
- ✅ 支持分页功能
- ✅ 验证搜索表单
- ✅ 处理API错误

### 3. 酒店详情页功能 (`hotel-detail.spec.ts`)
- ✅ 显示酒店基本信息
- ✅ 显示酒店图片轮播
- ✅ 显示酒店设施
- ✅ 显示房间列表
- ✅ 支持选择房间
- ✅ 显示预订摘要
- ✅ 支持立即预订
- ✅ 显示地图和评价
- ✅ 处理房间可用性

### 4. 预订流程功能 (`booking-flow.spec.ts`)
- ✅ 显示预订摘要
- ✅ 成功完成预订流程
- ✅ 验证必填字段
- ✅ 验证邮箱和信用卡格式
- ✅ 支持多种支付方式
- ✅ 支持国际客人信息
- ✅ 处理预订API错误

### 5. 订单管理功能 (`order-management.spec.ts`)
- ✅ 显示订单列表
- ✅ 支持多种搜索条件
- ✅ 支持查看订单详情
- ✅ 支持取消订单
- ✅ 支持导出功能
- ✅ 支持批量操作
- ✅ 支持排序和分页

### 6. 完整流程测试 (`complete-booking-flow.spec.ts`)
- ✅ 从登录到预订成功的完整流程
- ✅ 处理预订流程中的错误情况
- ✅ 支持多种支付方式的完整流程

## 运行测试

### 前置条件

1. 确保已安装依赖：
```bash
npm install
```

2. 安装 Playwright 浏览器：
```bash
npm run test:e2e:install
```

3. 确保开发服务器运行在 `http://localhost:3008`：
```bash
npm run dev
```

### 运行命令

```bash
# 运行所有 E2E 测试
npm run test:e2e

# 以 UI 模式运行测试
npm run test:e2e:ui

# 以有头模式运行测试（显示浏览器）
npm run test:e2e:headed

# 调试模式运行测试
npm run test:e2e:debug

# 查看测试报告
npm run test:e2e:report

# 运行特定测试文件
npx playwright test login.spec.ts

# 运行特定测试用例
npx playwright test --grep "应该成功登录"
```

### 浏览器支持

测试配置支持以下浏览器：
- ✅ Chromium (桌面版)
- ✅ Firefox (桌面版)
- ✅ WebKit/Safari (桌面版)
- ✅ Chrome (移动版)
- ✅ Safari (移动版)

## 测试配置

### 环境变量

可以通过环境变量配置测试：

```bash
# 设置基础URL
BASE_URL=http://localhost:3008

# 设置测试超时时间
TEST_TIMEOUT=30000

# 设置并行测试数量
WORKERS=4
```

### 测试数据

测试使用模拟数据，定义在 `fixtures/testData.ts` 中：
- 测试用户账户
- 酒店和房间数据
- 搜索参数
- 客人信息
- 支付信息

### API 模拟

测试使用 Playwright 的路由拦截功能模拟 API 响应，确保测试的独立性和可靠性。

## 最佳实践

### 1. 页面对象模型 (POM)
- 每个页面都有对应的页面对象类
- 封装页面元素和操作方法
- 提高测试代码的可维护性

### 2. 测试数据管理
- 使用固定的测试数据
- 避免依赖外部数据源
- 确保测试的可重复性

### 3. 错误处理
- 测试正常流程和异常情况
- 验证错误消息和状态
- 确保用户体验的完整性

### 4. 并行执行
- 测试设计为可并行执行
- 避免测试间的相互依赖
- 提高测试执行效率

## 故障排除

### 常见问题

1. **测试超时**
   - 检查开发服务器是否运行
   - 增加超时时间配置
   - 检查网络连接

2. **元素定位失败**
   - 检查页面是否正确加载
   - 验证元素选择器
   - 等待页面加载完成

3. **API 模拟失败**
   - 检查路由匹配规则
   - 验证模拟数据格式
   - 确保 API 路径正确

### 调试技巧

1. 使用 `--headed` 模式查看浏览器操作
2. 使用 `--debug` 模式逐步执行
3. 添加 `page.pause()` 暂停执行
4. 使用 `page.screenshot()` 截图调试
5. 查看浏览器控制台日志

## 持续集成

测试可以集成到 CI/CD 流程中：

```yaml
# GitHub Actions 示例
- name: Run E2E tests
  run: |
    npm ci
    npm run test:e2e:install
    npm run dev &
    npm run test:e2e
```

## 贡献指南

添加新测试时请遵循：

1. 使用描述性的测试名称
2. 遵循现有的代码结构
3. 添加适当的断言
4. 处理异常情况
5. 更新文档

## 联系方式

如有问题或建议，请联系开发团队。

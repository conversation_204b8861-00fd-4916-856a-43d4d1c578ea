import { test, expect } from '@playwright/test'
import { LoginPage } from './pages/LoginPage'
import { testUsers, testErrorMessages } from './fixtures/testData'
import { TestHelpers } from './utils/testHelpers'

test.describe('登录流程 E2E', () => {
  let loginPage: LoginPage

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page)
    await loginPage.goto()
  })

  test('应该成功登录管理员账户', async ({ page }) => {
    // 验证登录表单可见
    await loginPage.expectLoginFormVisible()

    // 执行登录
    await loginPage.login(testUsers.admin.email, testUsers.admin.password)

    // 验证跳转到仪表板
    await TestHelpers.expectUrlContains(page, '/dashboard')

    // 验证用户信息存储
    const token = await TestHelpers.getLocalStorageItem(page, 'e2e_token')
    expect(token).toBeTruthy()
  })

  test('应该显示无效凭据错误', async ({ page }) => {
    await loginPage.loginWithInvalidCredentials('<EMAIL>', 'wrongpassword')
    await loginPage.expectErrorMessage(testErrorMessages.login.invalidCredentials)
  })

  test('应该验证必填字段', async ({ page }) => {
    // 测试空邮箱
    await loginPage.loginWithInvalidCredentials('', 'password')
    await loginPage.expectValidationError('email', testErrorMessages.login.emptyEmail)

    // 测试空密码
    await loginPage.loginWithInvalidCredentials('<EMAIL>', '')
    await loginPage.expectValidationError('password', testErrorMessages.login.emptyPassword)
  })

  test('应该支持记住我功能', async ({ page }) => {
    await loginPage.login(testUsers.admin.email, testUsers.admin.password, true)
    await TestHelpers.expectUrlContains(page, '/dashboard')

    // 验证记住我状态
    const rememberMe = await TestHelpers.getLocalStorageItem(page, 'remember_me')
    expect(rememberMe).toBe('true')
  })

  test('应该处理网络错误', async ({ page }) => {
    // 模拟网络错误
    await TestHelpers.mockApiError(page, '/api/auth/login', 500, '服务器错误')

    await loginPage.loginWithInvalidCredentials(testUsers.admin.email, testUsers.admin.password)
    await loginPage.expectErrorMessage('服务器错误')
  })
})
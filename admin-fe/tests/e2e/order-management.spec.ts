import { test, expect } from '@playwright/test'
import { OrderManagementPage } from './pages/OrderManagementPage'
import { testOrders } from './fixtures/testData'
import { TestHelpers } from './utils/testHelpers'

test.describe('订单管理功能', () => {
  let orderManagementPage: OrderManagementPage

  test.beforeEach(async ({ page }) => {
    // 使用保存的认证状态
    await page.context().addInitScript(() => {
      localStorage.setItem('e2e_token', 'mock-token')
    })

    // 模拟订单列表API响应
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: testOrders,
        total: testOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    orderManagementPage = new OrderManagementPage(page)
    await orderManagementPage.goto()
  })

  test('应该显示订单列表', async ({ page }) => {
    await orderManagementPage.expectOrdersTableVisible()
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBe(testOrders.length)
    
    // 验证第一个订单的信息
    const orderNumber = await orderManagementPage.getOrderNumber(0)
    expect(orderNumber).toContain(testOrders[0].orderId)
    
    const hotelName = await orderManagementPage.getHotelName(0)
    expect(hotelName).toContain(testOrders[0].hotelName)
  })

  test('应该支持按订单号搜索', async ({ page }) => {
    const searchOrderId = testOrders[0].orderId
    const filteredOrders = testOrders.filter(order => order.orderId === searchOrderId)
    
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: filteredOrders,
        total: filteredOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.searchOrders({
      orderNumber: searchOrderId
    })
    
    await orderManagementPage.expectOrderInTable(searchOrderId)
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBe(1)
  })

  test('应该支持按酒店名称搜索', async ({ page }) => {
    const searchHotelName = testOrders[0].hotelName
    const filteredOrders = testOrders.filter(order => order.hotelName.includes(searchHotelName))
    
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: filteredOrders,
        total: filteredOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.searchOrders({
      hotelName: searchHotelName
    })
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBeGreaterThan(0)
  })

  test('应该支持按客人姓名搜索', async ({ page }) => {
    const searchGuestName = testOrders[0].guestName
    const filteredOrders = testOrders.filter(order => order.guestName.includes(searchGuestName))
    
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: filteredOrders,
        total: filteredOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.searchOrders({
      guestName: searchGuestName
    })
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBeGreaterThan(0)
  })

  test('应该支持按状态筛选', async ({ page }) => {
    const status = 'confirmed'
    const filteredOrders = testOrders.filter(order => order.status === status)
    
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: filteredOrders,
        total: filteredOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.filterByStatus('已确认')
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBeGreaterThan(0)
  })

  test('应该支持按日期范围搜索', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: testOrders,
        total: testOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.searchOrders({
      dateRange: ['2024-12-01', '2024-12-31']
    })
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBeGreaterThan(0)
  })

  test('应该支持重置搜索', async ({ page }) => {
    // 先执行搜索
    await orderManagementPage.searchOrders({
      orderNumber: testOrders[0].orderId
    })
    
    // 重置搜索
    await orderManagementPage.resetSearch()
    
    // 验证搜索表单已重置
    const orderNumberValue = await page.locator('input[placeholder*="订单号"]').inputValue()
    expect(orderNumberValue).toBe('')
  })

  test('应该支持查看订单详情', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/orders/*/detail', {
      data: {
        order: testOrders[0],
        guestInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+86 138 0013 8000'
        },
        paymentInfo: {
          method: '信用卡',
          cardLast4: '1111',
          amount: 1600,
          currency: 'CNY'
        }
      }
    })

    await orderManagementPage.viewOrderDetails(0)
    await orderManagementPage.expectOrderDetailDialogVisible()
    await orderManagementPage.closeOrderDetailDialog()
  })

  test('应该支持取消订单', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/orders/*/cancel', {
      data: {
        success: true,
        message: '订单已取消'
      }
    })

    // 模拟取消后的订单列表
    const updatedOrders = testOrders.map(order => 
      order.orderId === testOrders[0].orderId 
        ? { ...order, status: 'cancelled' }
        : order
    )
    
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: updatedOrders,
        total: updatedOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.cancelOrder(0)
    
    await TestHelpers.expectNotificationVisible(page, 'success', '订单已取消')
  })

  test('应该支持导出订单', async ({ page }) => {
    // 模拟文件下载
    const downloadPromise = page.waitForEvent('download')
    await orderManagementPage.exportOrders()
    
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('orders')
  })

  test('应该支持选择订单', async ({ page }) => {
    await orderManagementPage.selectOrder(0)
    await orderManagementPage.expectBatchActionsVisible()
  })

  test('应该支持全选订单', async ({ page }) => {
    await orderManagementPage.selectAllOrders()
    await orderManagementPage.expectBatchActionsVisible()
  })

  test('应该支持批量导出', async ({ page }) => {
    await orderManagementPage.selectAllOrders()
    
    const downloadPromise = page.waitForEvent('download')
    await orderManagementPage.batchExport()
    
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('orders')
  })

  test('应该支持批量取消', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/orders/batch-cancel', {
      data: {
        success: true,
        cancelledCount: 2,
        message: '已取消2个订单'
      }
    })

    await orderManagementPage.selectAllOrders()
    await orderManagementPage.batchCancel()
    
    await TestHelpers.expectNotificationVisible(page, 'success', '已取消2个订单')
  })

  test('应该支持排序功能', async ({ page }) => {
    await orderManagementPage.sortByColumn('创建时间')
    
    // 验证排序后的结果
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBeGreaterThan(0)
  })

  test('应该处理空搜索结果', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: [],
        total: 0,
        page: 1,
        pageSize: 10
      }
    })

    await orderManagementPage.searchOrders({
      orderNumber: 'NONEXISTENT'
    })
    
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBe(0)
    
    await expect(page.locator('.empty-state')).toBeVisible()
  })

  test('应该处理API错误', async ({ page }) => {
    await TestHelpers.mockApiError(page, '/api/orders', 500, '服务器错误')
    
    await page.reload()
    await TestHelpers.expectNotificationVisible(page, 'error', '服务器错误')
  })

  test('应该支持分页功能', async ({ page }) => {
    // 模拟多页数据
    const moreOrders = [...testOrders, ...testOrders, ...testOrders]
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: moreOrders.slice(0, 10),
        total: moreOrders.length,
        page: 1,
        pageSize: 10
      }
    })

    await page.reload()
    await orderManagementPage.waitForPageLoad()
    
    // 验证分页组件可见
    await expect(orderManagementPage.pagination).toBeVisible()
    
    // 测试下一页
    await TestHelpers.mockApiResponse(page, '/api/orders', {
      data: {
        orders: moreOrders.slice(10, 20),
        total: moreOrders.length,
        page: 2,
        pageSize: 10
      }
    })

    await orderManagementPage.goToPage(2)
    const ordersCount = await orderManagementPage.getOrdersCount()
    expect(ordersCount).toBeGreaterThan(0)
  })
})

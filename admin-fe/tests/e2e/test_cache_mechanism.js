/**
 * InternalHomepage接口缓存机制测试
 * 验证10分钟缓存是否正常工作
 */

const assert = require('assert')
const axios = require('axios')

// 测试配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8888'
const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123'
}

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 全局变量
let authToken = ''
let sessionId = ''

/**
 * 登录获取token
 */
async function login() {
  try {
    console.log('🔐 正在登录...')
    const response = await api.post('/api/user/login', {
      email: TEST_USER.email,
      password: TEST_USER.password
    })

    const { data } = response.data
    authToken = data.token
    sessionId = data.sessionId

    // 设置默认请求头
    api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
    api.defaults.headers.common['X-Session-Id'] = sessionId

    console.log('✅ 登录成功')
    return true
  } catch (error) {
    console.error('❌ 登录失败:', error.response?.data || error.message)
    return false
  }
}

/**
 * 测试单次InternalHomepage请求
 */
async function testSingleRequest() {
  console.log('\n📋 测试单次InternalHomepage请求...')
  
  try {
    const startTime = Date.now()
    const response = await api.post('/api/view/internalHomepage', {})
    const duration = Date.now() - startTime
    
    const { data } = response.data
    
    // 验证响应结构
    assert(data.starlingMap, '应该包含starlingMap字段')
    assert(data.menuRoutes, '应该包含menuRoutes字段')
    assert(data.downloadButtonURLs, '应该包含downloadButtonURLs字段')
    
    console.log(`✅ 单次请求成功，耗时: ${duration}ms`)
    console.log(`   - StarlingMap条目数: ${Object.keys(data.starlingMap).length}`)
    console.log(`   - MenuRoutes数量: ${data.menuRoutes.length}`)
    
    return { data, duration }
  } catch (error) {
    console.error('❌ 单次请求失败:', error.response?.data || error.message)
    throw error
  }
}

/**
 * 测试多次连续请求（验证后端性能）
 */
async function testMultipleRequests(count = 5) {
  console.log(`\n🔄 测试${count}次连续请求...`)
  
  const results = []
  
  for (let i = 1; i <= count; i++) {
    try {
      const startTime = Date.now()
      const response = await api.post('/api/view/internalHomepage', {})
      const duration = Date.now() - startTime
      
      results.push({
        requestNumber: i,
        duration,
        success: true
      })
      
      console.log(`   第${i}次请求: ${duration}ms`)
      
      // 短暂延迟避免过于频繁
      await new Promise(resolve => setTimeout(resolve, 100))
    } catch (error) {
      results.push({
        requestNumber: i,
        duration: 0,
        success: false,
        error: error.message
      })
      console.log(`   第${i}次请求: 失败 - ${error.message}`)
    }
  }
  
  // 统计结果
  const successCount = results.filter(r => r.success).length
  const avgDuration = results
    .filter(r => r.success)
    .reduce((sum, r) => sum + r.duration, 0) / successCount
  
  console.log(`📊 连续请求统计:`)
  console.log(`   - 成功率: ${successCount}/${count} (${(successCount/count*100).toFixed(1)}%)`)
  console.log(`   - 平均耗时: ${avgDuration.toFixed(1)}ms`)
  
  return results
}

/**
 * 测试并发请求（验证后端并发处理能力）
 */
async function testConcurrentRequests(count = 3) {
  console.log(`\n⚡ 测试${count}个并发请求...`)
  
  const startTime = Date.now()
  
  try {
    const promises = Array.from({ length: count }, (_, i) => 
      api.post('/api/view/internalHomepage', {})
        .then(response => ({
          requestNumber: i + 1,
          success: true,
          data: response.data.data
        }))
        .catch(error => ({
          requestNumber: i + 1,
          success: false,
          error: error.message
        }))
    )
    
    const results = await Promise.all(promises)
    const totalDuration = Date.now() - startTime
    
    const successCount = results.filter(r => r.success).length
    
    console.log(`📊 并发请求统计:`)
    console.log(`   - 成功率: ${successCount}/${count} (${(successCount/count*100).toFixed(1)}%)`)
    console.log(`   - 总耗时: ${totalDuration}ms`)
    console.log(`   - 平均每个请求: ${(totalDuration/count).toFixed(1)}ms`)
    
    // 验证所有成功的响应数据一致性
    const successResults = results.filter(r => r.success)
    if (successResults.length > 1) {
      const firstStarlingMapKeys = Object.keys(successResults[0].data.starlingMap).sort()
      const allConsistent = successResults.every(result => {
        const keys = Object.keys(result.data.starlingMap).sort()
        return JSON.stringify(keys) === JSON.stringify(firstStarlingMapKeys)
      })
      
      console.log(`   - 数据一致性: ${allConsistent ? '✅ 一致' : '❌ 不一致'}`)
    }
    
    return results
  } catch (error) {
    console.error('❌ 并发请求测试失败:', error.message)
    throw error
  }
}

/**
 * 测试缓存建议的有效性
 */
async function testCacheRecommendation() {
  console.log('\n💡 测试缓存建议的有效性...')
  
  try {
    // 1. 测试基准性能（无缓存情况下的多次请求）
    console.log('   📏 测试基准性能（模拟无缓存）...')
    const baselineResults = await testMultipleRequests(10)
    const baselineAvg = baselineResults
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.duration, 0) / baselineResults.filter(r => r.success).length
    
    // 2. 分析请求频率对性能的影响
    console.log('   🔍 分析请求模式...')
    
    // 模拟用户在10分钟内的典型操作
    const typicalOperations = [
      '登录后首次加载',
      '切换到用户管理页面',
      '切换到实体管理页面', 
      '切换到系统管理页面',
      '返回首页',
      '刷新页面'
    ]
    
    console.log('   📋 模拟典型用户操作:')
    for (const operation of typicalOperations) {
      const startTime = Date.now()
      await api.post('/api/view/internalHomepage', {})
      const duration = Date.now() - startTime
      console.log(`      ${operation}: ${duration}ms`)
      
      // 模拟用户操作间隔
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    // 3. 计算缓存收益
    const totalRequestsIn10Min = typicalOperations.length
    const totalTimeWithoutCache = baselineAvg * totalRequestsIn10Min
    const totalTimeWithCache = baselineAvg + (totalRequestsIn10Min - 1) * 5 // 假设缓存命中只需5ms
    const timeSaved = totalTimeWithoutCache - totalTimeWithCache
    const improvementPercent = (timeSaved / totalTimeWithoutCache * 100).toFixed(1)
    
    console.log(`\n📈 缓存收益分析:`)
    console.log(`   - 10分钟内典型请求次数: ${totalRequestsIn10Min}`)
    console.log(`   - 无缓存总耗时: ${totalTimeWithoutCache.toFixed(1)}ms`)
    console.log(`   - 有缓存总耗时: ${totalTimeWithCache.toFixed(1)}ms`)
    console.log(`   - 节省时间: ${timeSaved.toFixed(1)}ms`)
    console.log(`   - 性能提升: ${improvementPercent}%`)
    
    if (parseFloat(improvementPercent) > 50) {
      console.log('   ✅ 缓存机制能显著提升性能，建议实施')
    } else {
      console.log('   ⚠️ 缓存收益有限，需要进一步评估')
    }
    
    return {
      baselineAvg,
      totalRequestsIn10Min,
      timeSaved,
      improvementPercent: parseFloat(improvementPercent)
    }
  } catch (error) {
    console.error('❌ 缓存建议测试失败:', error.message)
    throw error
  }
}

/**
 * 主测试函数
 */
async function runCacheTests() {
  console.log('🚀 开始InternalHomepage缓存机制测试\n')
  
  try {
    // 1. 登录
    const loginSuccess = await login()
    if (!loginSuccess) {
      throw new Error('登录失败，无法继续测试')
    }
    
    // 2. 测试单次请求
    await testSingleRequest()
    
    // 3. 测试多次连续请求
    await testMultipleRequests(5)
    
    // 4. 测试并发请求
    await testConcurrentRequests(3)
    
    // 5. 测试缓存建议的有效性
    await testCacheRecommendation()
    
    console.log('\n🎉 所有缓存测试完成！')
    console.log('\n💡 建议:')
    console.log('   1. 实施10分钟缓存机制可以显著减少后端压力')
    console.log('   2. 在首页刷新时清除缓存确保数据最新')
    console.log('   3. 提供手动刷新缓存的功能给管理员')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
    process.exit(1)
  }
}

// 运行测试
if (require.main === module) {
  runCacheTests()
}

module.exports = {
  runCacheTests,
  testSingleRequest,
  testMultipleRequests,
  testConcurrentRequests,
  testCacheRecommendation
}

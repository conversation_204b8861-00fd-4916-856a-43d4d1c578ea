export const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'hotelbyte.com',
    role: 'admin'
  },
  tenant: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'tenant'
  },
  customer: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'customer'
  }
}

export const testHotels = [
  {
    id: '1',
    name: '豪华海景酒店',
    rating: 5,
    address: '海滨大道123号',
    city: '三亚',
    country: '中国',
    description: '享受无敌海景的豪华度假酒店',
    amenities: ['免费WiFi', '游泳池', '健身房', '餐厅', '停车场'],
    images: ['/hotel1-1.jpg', '/hotel1-2.jpg', '/hotel1-3.jpg'],
    minPrice: 800,
    currency: 'CNY'
  },
  {
    id: '2',
    name: '城市商务酒店',
    rating: 4,
    address: '商务区中心路88号',
    city: '上海',
    country: '中国',
    description: '位于商务区核心的现代化酒店',
    amenities: ['免费WiFi', '会议室', '健身房', '餐厅'],
    images: ['/hotel2-1.jpg', '/hotel2-2.jpg'],
    minPrice: 500,
    currency: 'CNY'
  }
]

export const testSearchParams = {
  valid: {
    destination: '三亚',
    checkIn: '2024-12-25',
    checkOut: '2024-12-27',
    adults: 2,
    children: 0,
    rooms: 1
  },
  withChildren: {
    destination: '上海',
    checkIn: '2024-12-20',
    checkOut: '2024-12-22',
    adults: 2,
    children: 1,
    rooms: 1
  },
  multiRoom: {
    destination: '北京',
    checkIn: '2024-12-15',
    checkOut: '2024-12-18',
    adults: 4,
    children: 2,
    rooms: 2
  }
}

export const testGuestInfo = {
  valid: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+86 138 0013 8000',
    country: '中国',
    idType: '身份证',
    idNumber: '110101199001011234'
  },
  international: {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '****** 123 4567',
    country: '美国',
    idType: '护照',
    idNumber: 'P123456789'
  }
}

export const testPaymentInfo = {
  creditCard: {
    method: '信用卡',
    cardNumber: '****************',
    expiryDate: '12/25',
    cvv: '123',
    cardHolderName: 'John Doe'
  },
  alipay: {
    method: '支付宝'
  },
  wechatPay: {
    method: '微信支付'
  }
}

export const testRooms = [
  {
    id: '1',
    name: '豪华海景房',
    description: '50平米，面朝大海，配备阳台',
    maxOccupancy: 2,
    amenities: ['海景', '阳台', '免费WiFi', '迷你吧'],
    rates: [
      {
        id: '1-1',
        name: '标准价格',
        price: 800,
        currency: 'CNY',
        cancellationPolicy: '免费取消',
        breakfast: false
      },
      {
        id: '1-2',
        name: '含早餐',
        price: 900,
        currency: 'CNY',
        cancellationPolicy: '免费取消',
        breakfast: true
      }
    ]
  },
  {
    id: '2',
    name: '标准双床房',
    description: '35平米，两张单人床',
    maxOccupancy: 2,
    amenities: ['免费WiFi', '空调', '电视'],
    rates: [
      {
        id: '2-1',
        name: '标准价格',
        price: 500,
        currency: 'CNY',
        cancellationPolicy: '免费取消',
        breakfast: false
      }
    ]
  }
]

export const testOrders = [
  {
    orderId: 'HB2024001',
    hotelName: '豪华海景酒店',
    roomName: '豪华海景房',
    guestName: 'John Doe',
    checkIn: '2024-12-25',
    checkOut: '2024-12-27',
    nights: 2,
    totalAmount: 1600,
    currency: 'CNY',
    status: 'confirmed',
    createdAt: '2024-12-01T10:00:00Z'
  },
  {
    orderId: 'HB2024002',
    hotelName: '城市商务酒店',
    roomName: '标准双床房',
    guestName: 'Jane Smith',
    checkIn: '2024-12-20',
    checkOut: '2024-12-22',
    nights: 2,
    totalAmount: 1000,
    currency: 'CNY',
    status: 'pending',
    createdAt: '2024-12-01T11:00:00Z'
  }
]

export const testFilters = {
  priceRange: {
    min: 300,
    max: 1000
  },
  rating: {
    min: 4
  },
  amenities: ['免费WiFi', '游泳池', '健身房']
}

export const testSpecialRequests = [
  '请安排高层房间',
  '需要婴儿床',
  '对花粉过敏，请安排无花房间',
  '蜜月旅行，希望有特别布置'
]

export const testErrorMessages = {
  login: {
    invalidCredentials: '用户名或密码错误',
    emptyEmail: '请输入邮箱',
    emptyPassword: '请输入密码',
    invalidEmail: '请输入有效的邮箱地址'
  },
  search: {
    emptyDestination: '请输入目的地',
    invalidDateRange: '离店日期必须晚于入住日期',
    pastDate: '入住日期不能早于今天'
  },
  booking: {
    emptyFirstName: '请输入名字',
    emptyLastName: '请输入姓氏',
    invalidEmail: '请输入有效的邮箱地址',
    emptyPhone: '请输入电话号码',
    invalidCardNumber: '请输入有效的卡号',
    termsNotAccepted: '请同意条款和条件'
  }
}

export const testUrls = {
  login: '/auth/login',
  dashboard: '/dashboard',
  hotelSearch: '/booking/hotel-search',
  hotelDetail: '/booking/hotel-search/hotel-detail',
  booking: '/booking/hotel-search/hotel-book',
  orderManagement: '/booking/order-management',
  analytics: '/booking/analytics'
}

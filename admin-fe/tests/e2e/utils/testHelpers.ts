import { Page, expect } from '@playwright/test'
import { testUsers } from '../fixtures/testData'

export class TestHelpers {
  static async login(page: Page, userType: 'admin' | 'tenant' | 'customer' = 'admin') {
    const user = testUsers[userType]
    await page.goto('/auth/login')
    
    await page.locator('input[type="text"]').first().fill(user.email)
    await page.locator('input[type="password"]').fill(user.password)
    
    // Handle captcha if present
    await page.evaluate(() => {
      if ((window as any).__VUE_APP_E2E__) {
        (window as any).isPassing = true
      }
    })
    
    await page.locator('button.login-btn').click()
    await page.waitForURL('**/dashboard', { timeout: 30000 })
  }

  static async logout(page: Page) {
    await page.locator('.user-avatar').click()
    await page.locator('[data-testid="logout-button"]').click()
    await page.waitForURL('**/auth/login')
  }

  static async navigateToBookingModule(page: Page) {
    await page.locator('.sidebar-menu').locator('text=预订').click()
    await page.waitForLoadState('networkidle')
  }

  static async waitForApiResponse(page: Page, url: string, timeout: number = 10000) {
    return await page.waitForResponse(response => 
      response.url().includes(url) && response.status() === 200,
      { timeout }
    )
  }

  static async mockApiResponse(page: Page, url: string, responseData: any) {
    await page.route(`**${url}`, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(responseData)
      })
    })
  }

  static async mockApiError(page: Page, url: string, statusCode: number = 500, errorMessage: string = 'Server Error') {
    await page.route(`**${url}`, route => {
      route.fulfill({
        status: statusCode,
        contentType: 'application/json',
        body: JSON.stringify({ error: errorMessage })
      })
    })
  }

  static async takeScreenshot(page: Page, name: string) {
    await page.screenshot({ 
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true 
    })
  }

  static async waitForElementToBeVisible(page: Page, selector: string, timeout: number = 10000) {
    await page.locator(selector).waitFor({ state: 'visible', timeout })
  }

  static async waitForElementToBeHidden(page: Page, selector: string, timeout: number = 10000) {
    await page.locator(selector).waitFor({ state: 'hidden', timeout })
  }

  static async scrollToElement(page: Page, selector: string) {
    await page.locator(selector).scrollIntoViewIfNeeded()
  }

  static async fillFormField(page: Page, selector: string, value: string) {
    const field = page.locator(selector)
    await field.clear()
    await field.fill(value)
    await expect(field).toHaveValue(value)
  }

  static async selectDropdownOption(page: Page, selectSelector: string, optionText: string) {
    await page.locator(selectSelector).click()
    await page.locator(`.el-select-dropdown__item:has-text("${optionText}")`).click()
  }

  static async uploadFile(page: Page, inputSelector: string, filePath: string) {
    await page.locator(inputSelector).setInputFiles(filePath)
  }

  static async downloadFile(page: Page, downloadTriggerSelector: string) {
    const downloadPromise = page.waitForEvent('download')
    await page.locator(downloadTriggerSelector).click()
    const download = await downloadPromise
    return download
  }

  static async expectTableRowCount(page: Page, tableSelector: string, expectedCount: number) {
    const rows = page.locator(`${tableSelector} tbody tr`)
    await expect(rows).toHaveCount(expectedCount)
  }

  static async expectTableContainsText(page: Page, tableSelector: string, text: string) {
    await expect(page.locator(`${tableSelector}:has-text("${text}")`)).toBeVisible()
  }

  static async clickTableRowByIndex(page: Page, tableSelector: string, rowIndex: number) {
    await page.locator(`${tableSelector} tbody tr`).nth(rowIndex).click()
  }

  static async getTableCellText(page: Page, tableSelector: string, rowIndex: number, columnIndex: number): Promise<string> {
    return await page.locator(`${tableSelector} tbody tr`).nth(rowIndex).locator('td').nth(columnIndex).textContent() || ''
  }

  static async expectNotificationVisible(page: Page, type: 'success' | 'error' | 'warning' | 'info', message?: string) {
    const notification = page.locator(`.el-message--${type}`)
    await expect(notification).toBeVisible()
    if (message) {
      await expect(notification).toContainText(message)
    }
  }

  static async dismissNotifications(page: Page) {
    const notifications = page.locator('.el-message')
    const count = await notifications.count()
    for (let i = 0; i < count; i++) {
      const notification = notifications.nth(i)
      if (await notification.isVisible()) {
        await notification.click()
      }
    }
  }

  static async expectDialogVisible(page: Page, title?: string) {
    const dialog = page.locator('.el-dialog')
    await expect(dialog).toBeVisible()
    if (title) {
      await expect(dialog.locator('.el-dialog__title')).toContainText(title)
    }
  }

  static async closeDialog(page: Page) {
    await page.locator('.el-dialog__close').click()
    await expect(page.locator('.el-dialog')).toBeHidden()
  }

  static async expectLoadingSpinnerHidden(page: Page) {
    await expect(page.locator('.el-loading-mask')).toBeHidden()
  }

  static async waitForPageLoad(page: Page) {
    await page.waitForLoadState('networkidle')
    await TestHelpers.expectLoadingSpinnerHidden(page).catch(() => {
      // Loading spinner might not exist, that's ok
    })
  }

  static generateRandomEmail(): string {
    const timestamp = Date.now()
    return `test${timestamp}@example.com`
  }

  static generateRandomPhone(): string {
    const randomNum = Math.floor(Math.random() * 100000000).toString().padStart(8, '0')
    return `138${randomNum}`
  }

  static formatDate(date: Date): string {
    return date.toISOString().split('T')[0]
  }

  static addDays(date: Date, days: number): Date {
    const result = new Date(date)
    result.setDate(result.getDate() + days)
    return result
  }

  static async retryAction(action: () => Promise<void>, maxRetries: number = 3, delay: number = 1000) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        await action()
        return
      } catch (error) {
        if (i === maxRetries - 1) {
          throw error
        }
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  static async expectUrlContains(page: Page, urlPart: string) {
    await expect(page).toHaveURL(new RegExp(urlPart))
  }

  static async expectPageTitle(page: Page, title: string) {
    await expect(page).toHaveTitle(title)
  }

  static async clearLocalStorage(page: Page) {
    await page.evaluate(() => {
      localStorage.clear()
    })
  }

  static async setLocalStorageItem(page: Page, key: string, value: string) {
    await page.evaluate(({ key, value }) => {
      localStorage.setItem(key, value)
    }, { key, value })
  }

  static async getLocalStorageItem(page: Page, key: string): Promise<string | null> {
    return await page.evaluate((key) => {
      return localStorage.getItem(key)
    }, key)
  }
}

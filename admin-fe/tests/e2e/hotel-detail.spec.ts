import { test, expect } from '@playwright/test'
import { HotelDetailPage } from './pages/HotelDetailPage'
import { testHotels, testRooms } from './fixtures/testData'
import { TestHelpers } from './utils/testHelpers'

test.describe('酒店详情页功能', () => {
  let hotelDetailPage: HotelDetailPage

  test.beforeEach(async ({ page }) => {
    // 使用保存的认证状态
    await page.context().addInitScript(() => {
      localStorage.setItem('e2e_token', 'mock-token')
    })

    // 模拟酒店详情API响应
    await TestHelpers.mockApiResponse(page, '/api/hotel/detail/*', {
      data: {
        hotel: testHotels[0],
        rooms: testRooms,
        reviews: [
          {
            id: '1',
            guestName: 'John D.',
            rating: 5,
            comment: '非常棒的酒店，服务一流！',
            date: '2024-11-15'
          }
        ]
      }
    })

    hotelDetailPage = new HotelDetailPage(page)
    await page.goto('/booking/hotel-search/hotel-detail/1')
    await hotelDetailPage.waitForPageLoad()
  })

  test('应该显示酒店基本信息', async ({ page }) => {
    await hotelDetailPage.expectHotelInfoVisible()
    
    // 验证酒店名称
    const hotelName = await hotelDetailPage.hotelName.textContent()
    expect(hotelName).toContain(testHotels[0].name)
    
    // 验证酒店评分
    await expect(hotelDetailPage.hotelRating).toBeVisible()
    
    // 验证酒店地址
    const address = await hotelDetailPage.hotelAddress.textContent()
    expect(address).toContain(testHotels[0].address)
  })

  test('应该显示酒店图片轮播', async ({ page }) => {
    await hotelDetailPage.expectImageCarouselVisible()
    
    // 测试图片导航
    await hotelDetailPage.navigateCarousel('next')
    await hotelDetailPage.navigateCarousel('prev')
  })

  test('应该显示酒店设施', async ({ page }) => {
    await hotelDetailPage.expectAmenitiesVisible()
  })

  test('应该显示房间列表', async ({ page }) => {
    await hotelDetailPage.expectRoomsListVisible()
    
    // 验证房间信息
    const roomName = await hotelDetailPage.getRoomName(0)
    expect(roomName).toContain(testRooms[0].name)
    
    const roomPrice = await hotelDetailPage.getRoomPrice(0)
    expect(roomPrice).toContain(testRooms[0].rates[0].price.toString())
  })

  test('应该支持选择房间', async ({ page }) => {
    await hotelDetailPage.selectRoom(0, 0)
    await hotelDetailPage.expectBookingSummaryVisible()
  })

  test('应该显示预订摘要', async ({ page }) => {
    await hotelDetailPage.selectRoom(0, 0)
    await hotelDetailPage.expectBookingSummaryVisible()
    
    // 验证预订摘要内容
    await expect(hotelDetailPage.bookingSummary.locator('.selected-room')).toBeVisible()
    await expect(hotelDetailPage.bookingSummary.locator('.total-price')).toBeVisible()
  })

  test('应该支持立即预订', async ({ page }) => {
    await hotelDetailPage.selectRoom(0, 0)
    await hotelDetailPage.proceedToBooking()
    
    // 验证跳转到预订页面
    await TestHelpers.expectUrlContains(page, '/hotel-book')
  })

  test('应该显示地图', async ({ page }) => {
    await hotelDetailPage.expectMapVisible()
  })

  test('应该显示用户评价', async ({ page }) => {
    await hotelDetailPage.expectReviewsVisible()
  })

  test('应该支持查看房间详情', async ({ page }) => {
    await hotelDetailPage.viewRoomDetails(0)
    await hotelDetailPage.closeRoomDetails()
  })

  test('应该显示房间可用性状态', async ({ page }) => {
    // 测试可预订房间
    await hotelDetailPage.expectRoomAvailable(0)
  })

  test('应该处理房间不可用情况', async ({ page }) => {
    // 模拟房间不可用的响应
    await TestHelpers.mockApiResponse(page, '/api/hotel/detail/*', {
      data: {
        hotel: testHotels[0],
        rooms: testRooms.map(room => ({
          ...room,
          available: false
        })),
        reviews: []
      }
    })

    await page.reload()
    await hotelDetailPage.waitForPageLoad()
    
    await hotelDetailPage.expectRoomUnavailable(0)
  })

  test('应该支持返回搜索结果', async ({ page }) => {
    await hotelDetailPage.goBack()
    await TestHelpers.expectUrlContains(page, '/hotel-search')
  })

  test('应该处理API错误', async ({ page }) => {
    await TestHelpers.mockApiError(page, '/api/hotel/detail/*', 404, '酒店不存在')
    
    await page.reload()
    await TestHelpers.expectNotificationVisible(page, 'error', '酒店不存在')
  })

  test('应该支持多种房型选择', async ({ page }) => {
    // 选择第一个房间的第一个价格
    await hotelDetailPage.selectRoom(0, 0)
    await hotelDetailPage.expectBookingSummaryVisible()
    
    // 选择第一个房间的第二个价格（含早餐）
    await hotelDetailPage.selectRoom(0, 1)
    await hotelDetailPage.expectBookingSummaryVisible()
    
    // 验证价格更新
    const price = await hotelDetailPage.getRoomPrice(0, 1)
    expect(price).toContain(testRooms[0].rates[1].price.toString())
  })

  test('应该响应式显示在移动设备上', async ({ page }) => {
    // 设置移动设备视口
    await page.setViewportSize({ width: 375, height: 667 })
    
    await hotelDetailPage.expectHotelInfoVisible()
    await hotelDetailPage.expectRoomsListVisible()
  })

  test('应该支持图片放大查看', async ({ page }) => {
    await hotelDetailPage.expectImageCarouselVisible()
    
    // 点击图片放大
    await hotelDetailPage.imageCarousel.locator('img').first().click()
    
    // 验证图片查看器打开
    await TestHelpers.expectDialogVisible(page)
  })

  test('应该显示房间设施信息', async ({ page }) => {
    await hotelDetailPage.viewRoomDetails(0)
    
    // 验证房间设施列表
    const dialog = page.locator('.el-dialog')
    await expect(dialog.locator('.room-amenities')).toBeVisible()
    
    await hotelDetailPage.closeRoomDetails()
  })
})

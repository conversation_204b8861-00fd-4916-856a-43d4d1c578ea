import { test, expect } from '@playwright/test'
import { HotelSearchPage } from './pages/HotelSearchPage'
import { testSearchParams, testHotels, testFilters, testErrorMessages } from './fixtures/testData'
import { TestHelpers } from './utils/testHelpers'

test.describe('酒店搜索功能', () => {
  let hotelSearchPage: HotelSearchPage

  test.beforeEach(async ({ page }) => {
    // 使用保存的认证状态
    await page.context().addInitScript(() => {
      localStorage.setItem('e2e_token', 'mock-token')
    })

    hotelSearchPage = new HotelSearchPage(page)
    await hotelSearchPage.goto()
  })

  test('应该成功搜索酒店', async ({ page }) => {
    // 模拟搜索API响应
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: testHotels,
        total: testHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    // 执行搜索
    await hotelSearchPage.searchHotels(testSearchParams.valid)

    // 验证搜索结果
    await hotelSearchPage.expectSearchResults()
    const hotelCount = await hotelSearchPage.getHotelCount()
    expect(hotelCount).toBeGreaterThan(0)

    // 验证酒店卡片内容
    await hotelSearchPage.expectHotelCardContent(0)
  })

  test('应该显示无搜索结果', async ({ page }) => {
    // 模拟空搜索结果
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: [],
        total: 0,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.expectNoResults()
  })

  test('应该支持价格筛选', async ({ page }) => {
    // 模拟筛选后的结果
    const filteredHotels = testHotels.filter(hotel => 
      hotel.minPrice >= testFilters.priceRange.min && 
      hotel.minPrice <= testFilters.priceRange.max
    )

    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: filteredHotels,
        total: filteredHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    // 先执行基础搜索
    await hotelSearchPage.searchHotels(testSearchParams.valid)

    // 应用价格筛选
    await hotelSearchPage.applyPriceFilter(testFilters.priceRange.min, testFilters.priceRange.max)

    // 验证筛选结果
    await hotelSearchPage.expectSearchResults()
  })

  test('应该支持评分筛选', async ({ page }) => {
    const filteredHotels = testHotels.filter(hotel => hotel.rating >= testFilters.rating.min)

    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: filteredHotels,
        total: filteredHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.applyRatingFilter(testFilters.rating.min)
    await hotelSearchPage.expectSearchResults()
  })

  test('应该支持设施筛选', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: testHotels,
        total: testHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.applyAmenitiesFilter(testFilters.amenities)
    await hotelSearchPage.expectSearchResults()
  })

  test('应该支持排序功能', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: testHotels,
        total: testHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.sortBy('价格从低到高')
    await hotelSearchPage.expectSearchResults()
  })

  test('应该支持分页功能', async ({ page }) => {
    // 模拟多页数据
    const moreHotels = [...testHotels, ...testHotels, ...testHotels]
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: moreHotels.slice(0, 10),
        total: moreHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.expectPaginationVisible()

    // 测试下一页
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: moreHotels.slice(10, 20),
        total: moreHotels.length,
        page: 2,
        pageSize: 10
      }
    })

    await hotelSearchPage.goToNextPage()
    await hotelSearchPage.expectSearchResults()
  })

  test('应该验证搜索表单', async ({ page }) => {
    // 测试空目的地
    await hotelSearchPage.searchHotels({
      destination: '',
      checkIn: testSearchParams.valid.checkIn,
      checkOut: testSearchParams.valid.checkOut
    })

    await TestHelpers.expectNotificationVisible(page, 'error', testErrorMessages.search.emptyDestination)
  })

  test('应该验证日期范围', async ({ page }) => {
    // 测试无效日期范围（离店早于入住）
    await hotelSearchPage.searchHotels({
      destination: testSearchParams.valid.destination,
      checkIn: '2024-12-27',
      checkOut: '2024-12-25'
    })

    await TestHelpers.expectNotificationVisible(page, 'error', testErrorMessages.search.invalidDateRange)
  })

  test('应该支持重置搜索', async ({ page }) => {
    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.resetSearch()

    // 验证表单已重置
    const destinationValue = await page.locator('input[placeholder*="目的地"]').inputValue()
    expect(destinationValue).toBe('')
  })

  test('应该处理搜索API错误', async ({ page }) => {
    await TestHelpers.mockApiError(page, '/api/hotel/search', 500, '搜索服务暂时不可用')

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await TestHelpers.expectNotificationVisible(page, 'error', '搜索服务暂时不可用')
  })

  test('应该支持查看酒店详情', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: testHotels,
        total: testHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.clickViewDetails(0)

    // 验证跳转到酒店详情页
    await TestHelpers.expectUrlContains(page, '/hotel-detail')
  })

  test('应该支持立即预订', async ({ page }) => {
    await TestHelpers.mockApiResponse(page, '/api/hotel/search', {
      data: {
        hotels: testHotels,
        total: testHotels.length,
        page: 1,
        pageSize: 10
      }
    })

    await hotelSearchPage.searchHotels(testSearchParams.valid)
    await hotelSearchPage.clickBookNow(0)

    // 验证跳转到预订页面
    await TestHelpers.expectUrlContains(page, '/hotel-book')
  })
})

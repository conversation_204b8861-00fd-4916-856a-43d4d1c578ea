import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './BasePage'

export class OrderManagementPage extends BasePage {
  readonly searchForm: Locator
  readonly orderNumberInput: Locator
  readonly hotelNameInput: Locator
  readonly guestNameInput: Locator
  readonly statusSelect: Locator
  readonly dateRangePicker: Locator
  readonly searchButton: Locator
  readonly resetButton: Locator
  readonly exportButton: Locator
  readonly ordersTable: Locator
  readonly batchActionsPanel: Locator
  readonly orderDetailDialog: Locator

  constructor(page: Page) {
    super(page)
    this.searchForm = page.locator('.search-form')
    this.orderNumberInput = page.locator('input[placeholder*="订单号"]')
    this.hotelNameInput = page.locator('input[placeholder*="酒店名称"]')
    this.guestNameInput = page.locator('input[placeholder*="客人姓名"]')
    this.statusSelect = page.locator('.status-select')
    this.dateRangePicker = page.locator('.date-range-picker')
    this.searchButton = page.locator('button:has-text("搜索")')
    this.resetButton = page.locator('button:has-text("重置")')
    this.exportButton = page.locator('button:has-text("导出")')
    this.ordersTable = page.locator('.orders-table')
    this.batchActionsPanel = page.locator('.batch-actions')
    this.orderDetailDialog = page.locator('.order-detail-dialog')
  }

  async goto(): Promise<void> {
    await this.page.goto('/booking/order-management')
    await this.waitForPageLoad()
  }

  async searchOrders(criteria: {
    orderNumber?: string
    hotelName?: string
    guestName?: string
    status?: string
    dateRange?: [string, string]
  }): Promise<void> {
    if (criteria.orderNumber) {
      await this.orderNumberInput.fill(criteria.orderNumber)
    }

    if (criteria.hotelName) {
      await this.hotelNameInput.fill(criteria.hotelName)
    }

    if (criteria.guestName) {
      await this.guestNameInput.fill(criteria.guestName)
    }

    if (criteria.status) {
      await this.statusSelect.click()
      await this.page.locator(`.el-select-dropdown__item:has-text("${criteria.status}")`).click()
    }

    if (criteria.dateRange) {
      await this.dateRangePicker.click()
      await this.page.locator(`[data-date="${criteria.dateRange[0]}"]`).click()
      await this.page.locator(`[data-date="${criteria.dateRange[1]}"]`).click()
    }

    await this.searchButton.click()
    await this.waitForPageLoad()
  }

  async resetSearch(): Promise<void> {
    await this.resetButton.click()
    await this.waitForPageLoad()
  }

  async exportOrders(): Promise<void> {
    await this.exportButton.click()
    // Wait for download to start
    await this.page.waitForTimeout(2000)
  }

  async expectOrdersTableVisible(): Promise<void> {
    await expect(this.ordersTable).toBeVisible()
    await expect(this.ordersTable.locator('thead')).toBeVisible()
  }

  async getOrdersCount(): Promise<number> {
    return await this.ordersTable.locator('tbody tr').count()
  }

  async selectOrder(rowIndex: number): Promise<void> {
    const checkbox = this.ordersTable.locator('tbody tr').nth(rowIndex).locator('.el-checkbox')
    await checkbox.click()
  }

  async selectAllOrders(): Promise<void> {
    const selectAllCheckbox = this.ordersTable.locator('thead .el-checkbox')
    await selectAllCheckbox.click()
  }

  async viewOrderDetails(rowIndex: number): Promise<void> {
    const viewButton = this.ordersTable.locator('tbody tr').nth(rowIndex).locator('button:has-text("查看")')
    await viewButton.click()
    await this.expectDialogVisible('订单详情')
  }

  async cancelOrder(rowIndex: number): Promise<void> {
    const cancelButton = this.ordersTable.locator('tbody tr').nth(rowIndex).locator('button:has-text("取消")')
    await cancelButton.click()
    
    // Confirm cancellation in dialog
    await this.page.locator('.el-message-box button:has-text("确定")').click()
    await this.waitForPageLoad()
  }

  async expectBatchActionsVisible(): Promise<void> {
    await expect(this.batchActionsPanel).toBeVisible()
    await expect(this.batchActionsPanel.locator('button:has-text("批量导出")')).toBeVisible()
    await expect(this.batchActionsPanel.locator('button:has-text("批量取消")')).toBeVisible()
  }

  async batchExport(): Promise<void> {
    await this.batchActionsPanel.locator('button:has-text("批量导出")').click()
    await this.page.waitForTimeout(2000)
  }

  async batchCancel(): Promise<void> {
    await this.batchActionsPanel.locator('button:has-text("批量取消")').click()
    await this.page.locator('.el-message-box button:has-text("确定")').click()
    await this.waitForPageLoad()
  }

  async expectOrderDetailDialogVisible(): Promise<void> {
    await expect(this.orderDetailDialog).toBeVisible()
    await expect(this.orderDetailDialog.locator('.order-info')).toBeVisible()
    await expect(this.orderDetailDialog.locator('.guest-info')).toBeVisible()
    await expect(this.orderDetailDialog.locator('.payment-info')).toBeVisible()
  }

  async closeOrderDetailDialog(): Promise<void> {
    await this.orderDetailDialog.locator('.el-dialog__close').click()
    await expect(this.orderDetailDialog).toBeHidden()
  }

  async getOrderStatus(rowIndex: number): Promise<string> {
    return await this.ordersTable.locator('tbody tr').nth(rowIndex).locator('.status-tag').textContent() || ''
  }

  async getOrderNumber(rowIndex: number): Promise<string> {
    return await this.ordersTable.locator('tbody tr').nth(rowIndex).locator('.order-number').textContent() || ''
  }

  async getHotelName(rowIndex: number): Promise<string> {
    return await this.ordersTable.locator('tbody tr').nth(rowIndex).locator('.hotel-name').textContent() || ''
  }

  async getGuestName(rowIndex: number): Promise<string> {
    return await this.ordersTable.locator('tbody tr').nth(rowIndex).locator('.guest-name').textContent() || ''
  }

  async getTotalAmount(rowIndex: number): Promise<string> {
    return await this.ordersTable.locator('tbody tr').nth(rowIndex).locator('.total-amount').textContent() || ''
  }

  async expectOrderInTable(orderNumber: string): Promise<void> {
    await expect(this.ordersTable.locator(`text=${orderNumber}`)).toBeVisible()
  }

  async expectOrderNotInTable(orderNumber: string): Promise<void> {
    await expect(this.ordersTable.locator(`text=${orderNumber}`)).not.toBeVisible()
  }

  async sortByColumn(columnName: string): Promise<void> {
    await this.ordersTable.locator(`th:has-text("${columnName}")`).click()
    await this.waitForPageLoad()
  }

  async filterByStatus(status: string): Promise<void> {
    await this.statusSelect.click()
    await this.page.locator(`.el-select-dropdown__item:has-text("${status}")`).click()
    await this.searchButton.click()
    await this.waitForPageLoad()
  }
}

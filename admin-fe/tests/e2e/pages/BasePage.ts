import { Page, Locator, expect } from '@playwright/test'

export class BasePage {
  readonly page: Page

  constructor(page: Page) {
    this.page = page
  }

  // Common elements
  get loadingSpinner(): Locator {
    return this.page.locator('.el-loading-mask')
  }

  get errorMessage(): Locator {
    return this.page.locator('.el-message--error')
  }

  get successMessage(): Locator {
    return this.page.locator('.el-message--success')
  }

  get warningMessage(): Locator {
    return this.page.locator('.el-message--warning')
  }

  get infoMessage(): Locator {
    return this.page.locator('.el-message--info')
  }

  // Navigation elements
  get sidebarMenu(): Locator {
    return this.page.locator('.sidebar-menu')
  }

  get userAvatar(): Locator {
    return this.page.locator('.user-avatar')
  }

  get logoutButton(): Locator {
    return this.page.locator('[data-testid="logout-button"]')
  }

  // Common actions
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle')
    await this.loadingSpinner.waitFor({ state: 'hidden', timeout: 10000 }).catch(() => {
      // Loading spinner might not exist, that's ok
    })
  }

  async clickMenuItem(menuText: string): Promise<void> {
    await this.sidebarMenu.locator(`text=${menuText}`).click()
    await this.waitForPageLoad()
  }

  async expectSuccessMessage(message?: string): Promise<void> {
    await expect(this.successMessage).toBeVisible()
    if (message) {
      await expect(this.successMessage).toContainText(message)
    }
  }

  async expectErrorMessage(message?: string): Promise<void> {
    await expect(this.errorMessage).toBeVisible()
    if (message) {
      await expect(this.errorMessage).toContainText(message)
    }
  }

  async dismissMessages(): Promise<void> {
    // Click on any visible message to dismiss it
    const messages = this.page.locator('.el-message')
    const count = await messages.count()
    for (let i = 0; i < count; i++) {
      const message = messages.nth(i)
      if (await message.isVisible()) {
        await message.click()
      }
    }
  }

  async logout(): Promise<void> {
    await this.userAvatar.click()
    await this.logoutButton.click()
    await this.page.waitForURL('**/auth/login')
  }

  // Form helpers
  async fillInput(selector: string, value: string): Promise<void> {
    const input = this.page.locator(selector)
    await input.clear()
    await input.fill(value)
    await expect(input).toHaveValue(value)
  }

  async selectOption(selector: string, value: string): Promise<void> {
    await this.page.locator(selector).selectOption(value)
  }

  async clickButton(text: string): Promise<void> {
    await this.page.locator(`button:has-text("${text}")`).click()
  }

  async clickButtonByTestId(testId: string): Promise<void> {
    await this.page.locator(`[data-testid="${testId}"]`).click()
  }

  // Table helpers
  async getTableRowCount(tableSelector: string = '.el-table'): Promise<number> {
    return await this.page.locator(`${tableSelector} tbody tr`).count()
  }

  async clickTableRowAction(rowIndex: number, actionText: string, tableSelector: string = '.el-table'): Promise<void> {
    const row = this.page.locator(`${tableSelector} tbody tr`).nth(rowIndex)
    await row.locator(`button:has-text("${actionText}")`).click()
  }

  // Dialog helpers
  async expectDialogVisible(title?: string): Promise<void> {
    const dialog = this.page.locator('.el-dialog')
    await expect(dialog).toBeVisible()
    if (title) {
      await expect(dialog.locator('.el-dialog__title')).toContainText(title)
    }
  }

  async closeDialog(): Promise<void> {
    await this.page.locator('.el-dialog__close').click()
    await expect(this.page.locator('.el-dialog')).toBeHidden()
  }

  // Pagination helpers
  async goToPage(pageNumber: number): Promise<void> {
    await this.page.locator(`.el-pagination .el-pager li:has-text("${pageNumber}")`).click()
    await this.waitForPageLoad()
  }

  async changePageSize(size: number): Promise<void> {
    await this.page.locator('.el-pagination .el-select').click()
    await this.page.locator(`.el-select-dropdown__item:has-text("${size}")`).click()
    await this.waitForPageLoad()
  }
}

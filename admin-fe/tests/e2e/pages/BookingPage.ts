import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './BasePage'

export interface GuestInfo {
  firstName: string
  lastName: string
  email: string
  phone: string
  country?: string
  idType?: string
  idNumber?: string
}

export class BookingPage extends BasePage {
  readonly bookingSummary: Locator
  readonly guestInfoForm: Locator
  readonly paymentForm: Locator
  readonly specialRequestsTextarea: Locator
  readonly termsCheckbox: Locator
  readonly confirmBookingButton: Locator
  readonly backButton: Locator

  // Guest info fields
  readonly firstNameInput: Locator
  readonly lastNameInput: Locator
  readonly emailInput: Locator
  readonly phoneInput: Locator
  readonly countrySelect: Locator
  readonly idTypeSelect: Locator
  readonly idNumberInput: Locator

  // Payment fields
  readonly paymentMethodSelect: Locator
  readonly cardNumberInput: Locator
  readonly expiryDateInput: Locator
  readonly cvvInput: Locator
  readonly cardHolderNameInput: Locator

  constructor(page: Page) {
    super(page)
    this.bookingSummary = page.locator('.booking-summary')
    this.guestInfoForm = page.locator('.guest-info-form')
    this.paymentForm = page.locator('.payment-form')
    this.specialRequestsTextarea = page.locator('textarea[placeholder*="特殊要求"]')
    this.termsCheckbox = page.locator('.terms-checkbox')
    this.confirmBookingButton = page.locator('button:has-text("确认预订")')
    this.backButton = page.locator('button:has-text("返回")')

    // Guest info fields
    this.firstNameInput = page.locator('input[placeholder*="名字"]')
    this.lastNameInput = page.locator('input[placeholder*="姓氏"]')
    this.emailInput = page.locator('input[type="email"]')
    this.phoneInput = page.locator('input[placeholder*="电话"]')
    this.countrySelect = page.locator('.country-select')
    this.idTypeSelect = page.locator('.id-type-select')
    this.idNumberInput = page.locator('input[placeholder*="证件号码"]')

    // Payment fields
    this.paymentMethodSelect = page.locator('.payment-method-select')
    this.cardNumberInput = page.locator('input[placeholder*="卡号"]')
    this.expiryDateInput = page.locator('input[placeholder*="有效期"]')
    this.cvvInput = page.locator('input[placeholder*="CVV"]')
    this.cardHolderNameInput = page.locator('input[placeholder*="持卡人姓名"]')
  }

  async expectBookingSummaryVisible(): Promise<void> {
    await expect(this.bookingSummary).toBeVisible()
    await expect(this.bookingSummary.locator('.hotel-info')).toBeVisible()
    await expect(this.bookingSummary.locator('.room-info')).toBeVisible()
    await expect(this.bookingSummary.locator('.stay-dates')).toBeVisible()
    await expect(this.bookingSummary.locator('.total-price')).toBeVisible()
  }

  async fillGuestInfo(guestInfo: GuestInfo): Promise<void> {
    await this.firstNameInput.fill(guestInfo.firstName)
    await this.lastNameInput.fill(guestInfo.lastName)
    await this.emailInput.fill(guestInfo.email)
    await this.phoneInput.fill(guestInfo.phone)

    if (guestInfo.country) {
      await this.countrySelect.click()
      await this.page.locator(`.el-select-dropdown__item:has-text("${guestInfo.country}")`).click()
    }

    if (guestInfo.idType) {
      await this.idTypeSelect.click()
      await this.page.locator(`.el-select-dropdown__item:has-text("${guestInfo.idType}")`).click()
    }

    if (guestInfo.idNumber) {
      await this.idNumberInput.fill(guestInfo.idNumber)
    }
  }

  async fillPaymentInfo(paymentInfo: {
    method: string
    cardNumber?: string
    expiryDate?: string
    cvv?: string
    cardHolderName?: string
  }): Promise<void> {
    await this.paymentMethodSelect.click()
    await this.page.locator(`.el-select-dropdown__item:has-text("${paymentInfo.method}")`).click()

    if (paymentInfo.method === '信用卡' && paymentInfo.cardNumber) {
      await this.cardNumberInput.fill(paymentInfo.cardNumber)
      await this.expiryDateInput.fill(paymentInfo.expiryDate || '')
      await this.cvvInput.fill(paymentInfo.cvv || '')
      await this.cardHolderNameInput.fill(paymentInfo.cardHolderName || '')
    }
  }

  async addSpecialRequests(requests: string): Promise<void> {
    await this.specialRequestsTextarea.fill(requests)
  }

  async acceptTerms(): Promise<void> {
    await this.termsCheckbox.check()
  }

  async confirmBooking(): Promise<void> {
    await this.confirmBookingButton.click()
    await this.waitForPageLoad()
  }

  async expectFormValidation(field: string, message: string): Promise<void> {
    const errorLocator = this.page.locator(`.el-form-item__error:has-text("${message}")`)
    await expect(errorLocator).toBeVisible()
  }

  async expectConfirmButtonDisabled(): Promise<void> {
    await expect(this.confirmBookingButton).toBeDisabled()
  }

  async expectConfirmButtonEnabled(): Promise<void> {
    await expect(this.confirmBookingButton).toBeEnabled()
  }

  async goBack(): Promise<void> {
    await this.backButton.click()
    await this.waitForPageLoad()
  }

  async getTotalPrice(): Promise<string> {
    return await this.bookingSummary.locator('.total-price .amount').textContent() || ''
  }

  async getHotelName(): Promise<string> {
    return await this.bookingSummary.locator('.hotel-name').textContent() || ''
  }

  async getRoomName(): Promise<string> {
    return await this.bookingSummary.locator('.room-name').textContent() || ''
  }

  async getStayDates(): Promise<string> {
    return await this.bookingSummary.locator('.stay-dates').textContent() || ''
  }

  async expectBookingSuccess(): Promise<void> {
    await expect(this.page.locator('.booking-success')).toBeVisible()
    await expect(this.page.locator('text=预订成功')).toBeVisible()
    await expect(this.page.locator('.order-number')).toBeVisible()
  }

  async getOrderNumber(): Promise<string> {
    return await this.page.locator('.order-number').textContent() || ''
  }

  async viewOrderDetails(): Promise<void> {
    await this.page.locator('button:has-text("查看订单详情")').click()
    await this.waitForPageLoad()
  }

  async continueBooking(): Promise<void> {
    await this.page.locator('button:has-text("继续预订")').click()
    await this.waitForPageLoad()
  }
}

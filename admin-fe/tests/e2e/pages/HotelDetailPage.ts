import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './BasePage'

export class HotelDetailPage extends BasePage {
  readonly hotelName: Locator
  readonly hotelRating: Locator
  readonly hotelAddress: Locator
  readonly hotelDescription: Locator
  readonly imageCarousel: Locator
  readonly amenitiesList: Locator
  readonly roomsList: Locator
  readonly bookingSummary: Locator
  readonly mapContainer: Locator
  readonly reviewsSection: Locator
  readonly backButton: Locator
  readonly editDatesButton: Locator
  readonly checkInDatePicker: Locator
  readonly checkOutDatePicker: Locator
  readonly confirmDatesButton: Locator
  readonly cancelDatesButton: Locator

  constructor(page: Page) {
    super(page)
    this.hotelName = page.locator('.hotel-name')
    this.hotelRating = page.locator('.hotel-rating')
    this.hotelAddress = page.locator('.hotel-address')
    this.hotelDescription = page.locator('.hotel-description')
    this.imageCarousel = page.locator('.image-carousel')
    this.amenitiesList = page.locator('.amenities-list')
    this.roomsList = page.locator('.rooms-list')
    this.bookingSummary = page.locator('.booking-summary')
    this.mapContainer = page.locator('.map-container')
    this.reviewsSection = page.locator('.reviews-section')
    this.backButton = page.locator('button:has-text("返回")')
    this.editDatesButton = page.getByRole('button', { name: 'Edit Dates' })
    this.checkInDatePicker = page.locator('.dates-edit .param-item:first-child .el-date-editor')
    this.checkOutDatePicker = page.locator('.dates-edit .param-item:nth-child(2) .el-date-editor')
    this.confirmDatesButton = page.getByRole('button', { name: 'Confirm' })
    this.cancelDatesButton = page.getByRole('button', { name: 'Cancel' })
  }

  async expectHotelInfoVisible(): Promise<void> {
    await expect(this.hotelName).toBeVisible()
    await expect(this.hotelRating).toBeVisible()
    await expect(this.hotelAddress).toBeVisible()
    await expect(this.hotelDescription).toBeVisible()
  }

  async expectImageCarouselVisible(): Promise<void> {
    await expect(this.imageCarousel).toBeVisible()
    const images = this.imageCarousel.locator('img')
    await expect(images.first()).toBeVisible()
  }

  async navigateCarousel(direction: 'next' | 'prev'): Promise<void> {
    const button = direction === 'next' 
      ? this.imageCarousel.locator('.carousel-next')
      : this.imageCarousel.locator('.carousel-prev')
    await button.click()
  }

  async expectAmenitiesVisible(): Promise<void> {
    await expect(this.amenitiesList).toBeVisible()
    const amenities = this.amenitiesList.locator('.amenity-item')
    const count = await amenities.count()
    expect(count).toBeGreaterThan(0)
  }

  async expectRoomsListVisible(): Promise<void> {
    await expect(this.roomsList).toBeVisible()
    const rooms = this.roomsList.locator('.room-item')
    const count = await rooms.count()
    expect(count).toBeGreaterThan(0)
  }

  async selectRoom(roomIndex: number, rateIndex: number = 0): Promise<void> {
    const room = this.roomsList.locator('.room-item').nth(roomIndex)
    const rate = room.locator('.rate-item').nth(rateIndex)
    await rate.locator('button:has-text("选择")').click()
    await this.waitForPageLoad()
  }

  async expectBookingSummaryVisible(): Promise<void> {
    await expect(this.bookingSummary).toBeVisible()
    await expect(this.bookingSummary.locator('.selected-room')).toBeVisible()
    await expect(this.bookingSummary.locator('.total-price')).toBeVisible()
    await expect(this.bookingSummary.locator('button:has-text("立即预订")')).toBeVisible()
  }

  async proceedToBooking(): Promise<void> {
    await this.bookingSummary.locator('button:has-text("立即预订")').click()
    await this.waitForPageLoad()
  }

  async expectMapVisible(): Promise<void> {
    await expect(this.mapContainer).toBeVisible()
  }

  async expectReviewsVisible(): Promise<void> {
    await expect(this.reviewsSection).toBeVisible()
    const reviews = this.reviewsSection.locator('.review-item')
    const count = await reviews.count()
    expect(count).toBeGreaterThanOrEqual(0) // Reviews might be empty
  }

  async goBack(): Promise<void> {
    await this.backButton.click()
    await this.waitForPageLoad()
  }

  async getRoomPrice(roomIndex: number, rateIndex: number = 0): Promise<string> {
    const room = this.roomsList.locator('.room-item').nth(roomIndex)
    const rate = room.locator('.rate-item').nth(rateIndex)
    return await rate.locator('.price').textContent() || ''
  }

  async getRoomName(roomIndex: number): Promise<string> {
    const room = this.roomsList.locator('.room-item').nth(roomIndex)
    return await room.locator('.room-name').textContent() || ''
  }

  async expectRoomAvailable(roomIndex: number): Promise<void> {
    const room = this.roomsList.locator('.room-item').nth(roomIndex)
    await expect(room.locator('.availability-status')).toContainText('可预订')
  }

  async expectRoomUnavailable(roomIndex: number): Promise<void> {
    const room = this.roomsList.locator('.room-item').nth(roomIndex)
    await expect(room.locator('.availability-status')).toContainText('已满房')
  }

  async viewRoomDetails(roomIndex: number): Promise<void> {
    const room = this.roomsList.locator('.room-item').nth(roomIndex)
    await room.locator('button:has-text("详情")').click()
    await this.expectDialogVisible('房间详情')
  }

  async closeRoomDetails(): Promise<void> {
    await this.closeDialog()
  }
}

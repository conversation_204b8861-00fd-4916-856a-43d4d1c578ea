import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './BasePage'

export class LoginPage extends BasePage {
  readonly emailInput: Locator
  readonly passwordInput: Locator
  readonly loginButton: Locator
  readonly rememberMeCheckbox: Locator
  readonly forgotPasswordLink: Locator
  readonly captchaSlider: Locator

  constructor(page: Page) {
    super(page)
    this.emailInput = page.locator('input[type="text"]').first()
    this.passwordInput = page.locator('input[type="password"]')
    this.loginButton = page.locator('button.login-btn')
    this.rememberMeCheckbox = page.locator('.el-checkbox__input')
    this.forgotPasswordLink = page.locator('text=忘记密码')
    this.captchaSlider = page.locator('.slider-component')
  }

  async goto(): Promise<void> {
    await this.page.goto('/auth/login')
    await this.waitForPageLoad()
  }

  async login(email: string, password: string, rememberMe: boolean = false): Promise<void> {
    // Fill email
    await this.emailInput.clear()
    await this.emailInput.fill(email)
    await expect(this.emailInput).toHaveValue(email)

    // Fill password
    await this.passwordInput.clear()
    await this.passwordInput.fill(password)
    await expect(this.passwordInput).toHaveValue(password)

    // Handle remember me checkbox
    if (rememberMe) {
      await this.rememberMeCheckbox.check()
    }

    // Handle captcha slider if present
    await this.handleCaptcha()

    // Click login button
    await this.loginButton.click()

    // Wait for navigation to dashboard
    await this.page.waitForURL('**/dashboard', { timeout: 30000 })
  }

  async loginWithInvalidCredentials(email: string, password: string): Promise<void> {
    await this.emailInput.clear()
    await this.emailInput.fill(email)
    await this.passwordInput.clear()
    await this.passwordInput.fill(password)
    
    await this.handleCaptcha()
    await this.loginButton.click()
    
    // Should stay on login page and show error
    await this.expectErrorMessage()
  }

  private async handleCaptcha(): Promise<void> {
    // Check if captcha slider exists
    if (await this.captchaSlider.isVisible()) {
      // For E2E testing, we can bypass captcha by setting a global flag
      await this.page.evaluate(() => {
        if ((window as any).__VUE_APP_E2E__) {
          (window as any).isPassing = true
        }
      })
    }
  }

  async expectLoginFormVisible(): Promise<void> {
    await expect(this.emailInput).toBeVisible()
    await expect(this.passwordInput).toBeVisible()
    await expect(this.loginButton).toBeVisible()
  }

  async expectLoginButtonDisabled(): Promise<void> {
    await expect(this.loginButton).toBeDisabled()
  }

  async expectLoginButtonEnabled(): Promise<void> {
    await expect(this.loginButton).toBeEnabled()
  }

  async clickForgotPassword(): Promise<void> {
    await this.forgotPasswordLink.click()
  }

  async expectValidationError(field: 'email' | 'password', message: string): Promise<void> {
    const errorLocator = field === 'email' 
      ? this.emailInput.locator('..').locator('.el-form-item__error')
      : this.passwordInput.locator('..').locator('.el-form-item__error')
    
    await expect(errorLocator).toBeVisible()
    await expect(errorLocator).toContainText(message)
  }
}

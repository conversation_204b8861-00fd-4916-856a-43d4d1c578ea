import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './BasePage'

export class HotelSearchPage extends BasePage {
  readonly destinationInput: Locator
  readonly checkInInput: Locator
  readonly checkOutInput: Locator
  readonly adultsSelect: Locator
  readonly childrenSelect: Locator
  readonly roomsSelect: Locator
  readonly searchButton: Locator
  readonly resetButton: Locator
  readonly filterPanel: Locator
  readonly priceRangeSlider: Locator
  readonly ratingFilter: Locator
  readonly amenitiesFilter: Locator
  readonly hotelCards: Locator
  readonly sortSelect: Locator
  readonly pagination: Locator

  constructor(page: Page) {
    super(page)
    this.destinationInput = page.locator('input[placeholder*="目的地"]')
    this.checkInInput = page.locator('input[placeholder*="入住"]')
    this.checkOutInput = page.locator('input[placeholder*="离店"]')
    this.adultsSelect = page.locator('.adults-select')
    this.childrenSelect = page.locator('.children-select')
    this.roomsSelect = page.locator('.rooms-select')
    this.searchButton = page.locator('button:has-text("搜索")')
    this.resetButton = page.locator('button:has-text("重置")')
    this.filterPanel = page.locator('.filter-panel')
    this.priceRangeSlider = page.locator('.price-range-slider')
    this.ratingFilter = page.locator('.rating-filter')
    this.amenitiesFilter = page.locator('.amenities-filter')
    this.hotelCards = page.locator('.hotel-card')
    this.sortSelect = page.locator('.sort-select')
    this.pagination = page.locator('.el-pagination')
  }

  async goto(): Promise<void> {
    await this.page.goto('/booking/hotel-search')
    await this.waitForPageLoad()
  }

  async searchHotels(searchParams: {
    destination: string
    checkIn: string
    checkOut: string
    adults?: number
    children?: number
    rooms?: number
  }): Promise<void> {
    // Fill destination
    await this.destinationInput.clear()
    await this.destinationInput.fill(searchParams.destination)

    // Fill check-in date
    await this.checkInInput.click()
    await this.page.locator(`[data-date="${searchParams.checkIn}"]`).click()

    // Fill check-out date
    await this.checkOutInput.click()
    await this.page.locator(`[data-date="${searchParams.checkOut}"]`).click()

    // Select adults
    if (searchParams.adults) {
      await this.adultsSelect.click()
      await this.page.locator(`.el-select-dropdown__item:has-text("${searchParams.adults}")`).click()
    }

    // Select children
    if (searchParams.children) {
      await this.childrenSelect.click()
      await this.page.locator(`.el-select-dropdown__item:has-text("${searchParams.children}")`).click()
    }

    // Select rooms
    if (searchParams.rooms) {
      await this.roomsSelect.click()
      await this.page.locator(`.el-select-dropdown__item:has-text("${searchParams.rooms}")`).click()
    }

    // Click search
    await this.searchButton.click()
    await this.waitForPageLoad()
  }

  async resetSearch(): Promise<void> {
    await this.resetButton.click()
    await this.waitForPageLoad()
  }

  async applyPriceFilter(minPrice: number, maxPrice: number): Promise<void> {
    // Interact with price range slider
    const slider = this.priceRangeSlider
    await slider.isVisible()
    
    // This is a simplified implementation - actual slider interaction would be more complex
    await this.page.locator(`input[placeholder="最低价格"]`).fill(minPrice.toString())
    await this.page.locator(`input[placeholder="最高价格"]`).fill(maxPrice.toString())
    await this.page.locator('button:has-text("应用")').click()
    await this.waitForPageLoad()
  }

  async applyRatingFilter(minRating: number): Promise<void> {
    await this.ratingFilter.locator(`.el-rate__item:nth-child(${minRating})`).click()
    await this.waitForPageLoad()
  }

  async applyAmenitiesFilter(amenities: string[]): Promise<void> {
    for (const amenity of amenities) {
      await this.amenitiesFilter.locator(`label:has-text("${amenity}")`).click()
    }
    await this.page.locator('button:has-text("应用筛选")').click()
    await this.waitForPageLoad()
  }

  async sortBy(sortOption: string): Promise<void> {
    await this.sortSelect.click()
    await this.page.locator(`.el-select-dropdown__item:has-text("${sortOption}")`).click()
    await this.waitForPageLoad()
  }

  async getHotelCount(): Promise<number> {
    return await this.hotelCards.count()
  }

  async clickHotelCard(index: number): Promise<void> {
    await this.hotelCards.nth(index).click()
    await this.waitForPageLoad()
  }

  async clickViewDetails(index: number): Promise<void> {
    await this.hotelCards.nth(index).locator('button:has-text("查看详情")').click()
    await this.waitForPageLoad()
  }

  async clickBookNow(index: number): Promise<void> {
    await this.hotelCards.nth(index).locator('button:has-text("立即预订")').click()
    await this.waitForPageLoad()
  }

  async expectSearchResults(): Promise<void> {
    await expect(this.hotelCards.first()).toBeVisible()
    const count = await this.getHotelCount()
    expect(count).toBeGreaterThan(0)
  }

  async expectNoResults(): Promise<void> {
    await expect(this.page.locator('.empty-state')).toBeVisible()
    await expect(this.page.locator('text=未找到符合条件的酒店')).toBeVisible()
  }

  async expectHotelCardContent(index: number): Promise<void> {
    const card = this.hotelCards.nth(index)
    await expect(card.locator('.hotel-name')).toBeVisible()
    await expect(card.locator('.hotel-rating')).toBeVisible()
    await expect(card.locator('.hotel-address')).toBeVisible()
    await expect(card.locator('.hotel-price')).toBeVisible()
    await expect(card.locator('button:has-text("查看详情")')).toBeVisible()
    await expect(card.locator('button:has-text("立即预订")')).toBeVisible()
  }

  async expectPaginationVisible(): Promise<void> {
    await expect(this.pagination).toBeVisible()
  }

  async goToNextPage(): Promise<void> {
    await this.pagination.locator('.btn-next').click()
    await this.waitForPageLoad()
  }

  async goToPreviousPage(): Promise<void> {
    await this.pagination.locator('.btn-prev').click()
    await this.waitForPageLoad()
  }
}

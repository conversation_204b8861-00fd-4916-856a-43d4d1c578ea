#!/usr/bin/env node

/**
 * 端到端测试：InternalHomepage API 和前端集成
 * 
 * 测试目标：
 * 1. 验证后端 API 返回正确的数据结构
 * 2. 验证 starlingMap 包含多语言数据
 * 3. 验证前端能够正确接收和处理数据
 */

const https = require('http');
const assert = require('assert');

// 测试配置
const API_BASE_URL = 'http://localhost:8081';
const FRONTEND_BASE_URL = 'http://localhost:3008';

// 工具函数：发送 HTTP 请求
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });
        
        req.on('error', reject);
        req.end();
    });
}

// 测试 1: 验证 InternalHomepage API
async function testInternalHomepageAPI() {
    console.log('🧪 测试 1: InternalHomepage API...');
    
    try {
        const response = await makeRequest(`${API_BASE_URL}/api/view/internalHomepage`);
        
        // 验证响应状态
        assert.strictEqual(response.status, 200, 'API 应该返回 200 状态码');
        
        // 验证响应结构
        const { data } = response.data;
        assert(data, 'API 应该返回 data 字段');
        assert(data.downloadButtonURLs, 'API 应该返回 downloadButtonURLs');
        assert(data.navigationTabs, 'API 应该返回 navigationTabs');
        assert(data.starlingMap, 'API 应该返回 starlingMap');
        
        // 验证 navigationTabs 结构
        assert(Array.isArray(data.navigationTabs), 'navigationTabs 应该是数组');
        assert(data.navigationTabs.length > 0, 'navigationTabs 不应该为空');
        
        // 验证第一个导航标签
        const firstTab = data.navigationTabs[0];
        assert.strictEqual(firstTab.id, 'user-management', '第一个标签应该是 user-management');
        assert(firstTab.title, '导航标签应该有 title');
        assert(firstTab.title.en, '导航标签应该有英文标题');
        assert(firstTab.title.zh, '导航标签应该有中文标题');
        
        // 验证子标签
        assert(Array.isArray(firstTab.subTabs), 'subTabs 应该是数组');
        assert(firstTab.subTabs.length > 0, 'subTabs 不应该为空');
        
        const firstSubTab = firstTab.subTabs[0];
        assert.strictEqual(firstSubTab.id, 'tenant-users', '第一个子标签应该是 tenant-users');
        assert.strictEqual(firstSubTab.title.en, 'Tenant Users', '子标签英文标题应该是 Tenant Users');
        assert.strictEqual(firstSubTab.title.zh, '租户用户', '子标签中文标题应该是 租户用户');
        
        // 验证 starlingMap
        assert(typeof data.starlingMap === 'object', 'starlingMap 应该是对象');
        const starlingKeys = Object.keys(data.starlingMap);
        assert(starlingKeys.length > 0, 'starlingMap 不应该为空');
        
        // 验证多语言数据结构
        const firstKey = starlingKeys[0];
        const firstValue = data.starlingMap[firstKey];
        assert(typeof firstValue === 'object', 'starlingMap 值应该是对象');
        assert(firstValue.en, 'starlingMap 应该包含英文翻译');
        assert(firstValue.zh, 'starlingMap 应该包含中文翻译');
        
        console.log('✅ InternalHomepage API 测试通过');
        console.log(`   - 返回了 ${data.navigationTabs.length} 个导航标签`);
        console.log(`   - 返回了 ${starlingKeys.length} 个多语言条目`);
        console.log(`   - 第一个多语言条目: ${firstKey}`);
        console.log(`     EN: ${firstValue.en}`);
        console.log(`     ZH: ${firstValue.zh}`);
        
        return data;
        
    } catch (error) {
        console.error('❌ InternalHomepage API 测试失败:', error.message);
        throw error;
    }
}

// 测试 2: 验证前端可访问性
async function testFrontendAccessibility() {
    console.log('🧪 测试 2: 前端可访问性...');
    
    try {
        const response = await makeRequest(`${FRONTEND_BASE_URL}/`);
        
        // 验证前端服务运行
        assert.strictEqual(response.status, 200, '前端服务应该可访问');
        assert(typeof response.data === 'string', '前端应该返回 HTML');
        assert(response.data.includes('<div id="app">'), '前端应该包含 Vue 应用容器');
        
        console.log('✅ 前端可访问性测试通过');
        
    } catch (error) {
        console.error('❌ 前端可访问性测试失败:', error.message);
        throw error;
    }
}

// 测试 3: 验证多语言数据完整性
async function testMultilingualData() {
    console.log('🧪 测试 3: 多语言数据完整性...');
    
    try {
        const response = await makeRequest(`${API_BASE_URL}/api/view/internalHomepage`);
        const { data } = response.data;
        
        // 验证关键的多语言条目
        const requiredKeys = [
            'nav.nav.user.management',
            'user.user.list.title',
            'common.common.button.add',
            'common.common.button.edit',
            'common.common.button.delete'
        ];
        
        for (const key of requiredKeys) {
            assert(data.starlingMap[key], `应该包含多语言条目: ${key}`);
            assert(data.starlingMap[key].en, `${key} 应该有英文翻译`);
            assert(data.starlingMap[key].zh, `${key} 应该有中文翻译`);
        }
        
        // 验证用户管理相关的翻译
        const userMgmtKey = 'nav.nav.user.management';
        assert.strictEqual(data.starlingMap[userMgmtKey].en, 'User Management', '用户管理英文翻译正确');
        assert.strictEqual(data.starlingMap[userMgmtKey].zh, '用户管理', '用户管理中文翻译正确');
        
        // 验证租户用户相关的翻译
        const tenantUserKey = 'user.user.list.title';
        assert.strictEqual(data.starlingMap[tenantUserKey].en, 'Tenant User List', '租户用户列表英文翻译正确');
        assert.strictEqual(data.starlingMap[tenantUserKey].zh, '租户用户列表', '租户用户列表中文翻译正确');
        
        console.log('✅ 多语言数据完整性测试通过');
        console.log(`   - 验证了 ${requiredKeys.length} 个关键多语言条目`);
        
    } catch (error) {
        console.error('❌ 多语言数据完整性测试失败:', error.message);
        throw error;
    }
}

// 主测试函数
async function runE2ETests() {
    console.log('🚀 开始端到端测试...\n');
    
    try {
        await testInternalHomepageAPI();
        console.log('');
        
        await testFrontendAccessibility();
        console.log('');
        
        await testMultilingualData();
        console.log('');
        
        console.log('🎉 所有端到端测试通过！');
        console.log('');
        console.log('✅ InternalHomepage 功能已成功实现：');
        console.log('   - 后端 API 正确返回数据结构');
        console.log('   - starlingMap 包含完整的多语言数据');
        console.log('   - 前端服务正常运行');
        console.log('   - 多语言支持符合项目要求');
        
    } catch (error) {
        console.error('💥 端到端测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runE2ETests();
}

module.exports = {
    testInternalHomepageAPI,
    testFrontendAccessibility,
    testMultilingualData,
    runE2ETests
};

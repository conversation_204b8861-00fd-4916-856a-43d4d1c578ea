import { chromium, FullConfig } from '@playwright/test'
import { LoginPage } from './pages/LoginPage'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...')
  
  const { baseURL } = config.projects[0].use
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Pre-authenticate and save auth state
    console.log('🔐 Pre-authenticating user...')
    const loginPage = new LoginPage(page)
    
    await page.goto(`${baseURL}/auth/login`)
    await loginPage.login('<EMAIL>', 'hotelbyte.com')
    
    // Wait for successful login and save auth state
    await page.waitForURL('**/dashboard', { timeout: 30000 })
    await page.context().storageState({ path: 'tests/e2e/auth.json' })
    
    console.log('✅ Authentication state saved')
    
    // Verify backend API is accessible
    console.log('🔍 Verifying backend API...')
    const response = await page.request.get(`${baseURL}/api/ping`)
    if (!response.ok()) {
      console.warn('⚠️ Backend API not accessible, tests may use mock data')
    } else {
      console.log('✅ Backend API is accessible')
    }
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
  
  console.log('✅ Global setup completed')
}

export default globalSetup

import { test, expect } from '@playwright/test'

test.describe('订单显示功能', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到订单管理页面
    await page.goto('/booking/order-management')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle')
  })

  test('应该正确显示订单列表', async ({ page }) => {
    // 等待订单列表加载
    await page.waitForSelector('[data-cy="order-list"]', { timeout: 10000 })
    
    // 检查是否有订单数据
    const orderRows = page.locator('[data-cy="order-list"] .el-table__row')
    const orderCount = await orderRows.count()
    
    if (orderCount > 0) {
      // 检查第一个订单的基本信息
      const firstOrder = orderRows.first()
      
      // 检查订单ID是否存在且不为空
      const orderIdCell = firstOrder.locator('[data-cy="order-id"]')
      await expect(orderIdCell).toBeVisible()
      const orderIdText = await orderIdCell.textContent()
      expect(orderIdText).toBeTruthy()
      expect(orderIdText?.trim()).not.toBe('')
      
      // 检查酒店名称是否显示
      const hotelNameCell = firstOrder.locator('td').nth(1) // 假设酒店名称在第二列
      await expect(hotelNameCell).toBeVisible()
      const hotelNameText = await hotelNameCell.textContent()
      expect(hotelNameText).toBeTruthy()
      
      // 检查入住日期是否显示
      const checkInCell = firstOrder.locator('td').nth(2) // 假设入住日期在第三列
      await expect(checkInCell).toBeVisible()
      const checkInText = await checkInCell.textContent()
      expect(checkInText).toBeTruthy()
      
      // 检查房间信息是否显示
      const roomInfoCell = firstOrder.locator('td').nth(3) // 假设房间信息在第四列
      await expect(roomInfoCell).toBeVisible()
      const roomInfoText = await roomInfoCell.textContent()
      expect(roomInfoText).toBeTruthy()
      
      // 检查总价是否显示
      const priceCell = firstOrder.locator('td').nth(4) // 假设总价在第五列
      await expect(priceCell).toBeVisible()
      const priceText = await priceCell.textContent()
      expect(priceText).toBeTruthy()
      expect(priceText).toMatch(/\d+/) // 应该包含数字
      
      // 检查状态是否显示
      const statusCell = firstOrder.locator('td').nth(5) // 假设状态在第六列
      await expect(statusCell).toBeVisible()
      const statusTag = statusCell.locator('.el-tag')
      await expect(statusTag).toBeVisible()
      
      console.log('订单列表显示正常，包含以下信息：')
      console.log('- 订单ID:', orderIdText)
      console.log('- 酒店名称:', hotelNameText)
      console.log('- 入住日期:', checkInText)
      console.log('- 房间信息:', roomInfoText)
      console.log('- 总价:', priceText)
    } else {
      console.log('当前没有订单数据，这可能是正常的')
    }
  })

  test('应该能够查看订单详情', async ({ page }) => {
    // 等待订单列表加载
    await page.waitForSelector('[data-cy="order-list"]', { timeout: 10000 })
    
    // 检查是否有订单
    const orderRows = page.locator('[data-cy="order-list"] .el-table__row')
    const orderCount = await orderRows.count()
    
    if (orderCount > 0) {
      // 点击第一个订单的订单ID链接
      const firstOrderIdLink = page.locator('[data-cy="order-id"]').first()
      await firstOrderIdLink.click()
      
      // 等待订单详情页面加载
      await page.waitForLoadState('networkidle')
      
      // 检查是否跳转到订单详情页面
      await expect(page).toHaveURL(/\/booking\/order-management\/\d+/)
      
      // 检查订单详情页面的基本元素
      await expect(page.locator('.order-summary')).toBeVisible()
      
      // 检查订单基本信息
      const orderIdElement = page.locator('.order-summary .summary-item').first()
      await expect(orderIdElement).toBeVisible()
      const orderIdText = await orderIdElement.textContent()
      expect(orderIdText).toContain('订单号')
      
      // 检查订单状态
      const statusElement = page.locator('.order-summary .el-tag')
      await expect(statusElement).toBeVisible()
      
      // 检查创建时间
      const createTimeElement = page.locator('.order-summary .summary-item').nth(2)
      await expect(createTimeElement).toBeVisible()
      const createTimeText = await createTimeElement.textContent()
      expect(createTimeText).toContain('创建时间')
      
      console.log('订单详情页面显示正常')
    } else {
      console.log('没有订单可以查看详情')
    }
  })

  test('应该正确处理空数据状态', async ({ page }) => {
    // 模拟空数据响应（如果可能的话）
    // 这里可以通过拦截网络请求来模拟空数据
    
    await page.route('**/api/trade/listOrder', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          rows: [],
          total: 0,
          hasMore: false
        })
      })
    })
    
    // 重新加载页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // 检查空状态显示
    const orderTable = page.locator('[data-cy="order-list"]')
    await expect(orderTable).toBeVisible()
    
    // 检查是否显示空数据提示
    const emptyText = page.locator('.el-table__empty-text')
    await expect(emptyText).toBeVisible()
    
    console.log('空数据状态处理正常')
  })

  test('应该正确处理错误状态', async ({ page }) => {
    // 模拟API错误响应
    await page.route('**/api/trade/listOrder', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal Server Error'
        })
      })
    })
    
    // 重新加载页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // 检查错误处理
    // 这里可能会显示错误消息或者保持加载状态
    // 具体的错误处理方式取决于前端的实现
    
    console.log('错误状态处理测试完成')
  })
})

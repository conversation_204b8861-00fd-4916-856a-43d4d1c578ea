import { test, expect } from '@playwright/test'

test.describe('订单创建流程', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[data-cy="username"]', 'admin')
    await page.fill('[data-cy="password"]', 'password')
    await page.click('[data-cy="login-button"]')
    await page.waitForURL('/dashboard')
  })

  test('应该能够成功创建订单', async ({ page }) => {
    // 导航到创建订单页面
    await page.goto('/booking/orders/create')
    
    // 搜索酒店
    await page.fill('input[placeholder*="输入酒店名称"]', '希尔顿酒店')
    await page.click('button:has-text("搜索")')
    
    // 等待搜索结果
    await page.waitForSelector('.hotel-results')
    
    // 选择第一个酒店
    await page.click('.hotel-results .el-table__row:first-child button:has-text("选择")')
    
    // 填写入住日期
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const checkIn = tomorrow.toISOString().split('T')[0]
    
    const dayAfterTomorrow = new Date()
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2)
    const checkOut = dayAfterTomorrow.toISOString().split('T')[0]
    
    await page.fill('input[placeholder="选择入住日期"]', checkIn)
    await page.fill('input[placeholder="选择离店日期"]', checkOut)
    
    // 等待房型加载
    await page.waitForSelector('.room-types', { timeout: 10000 })
    
    // 选择第一个房型
    await page.click('.room-types .el-table__row:first-child button:has-text("选择")')
    
    // 填写客人信息
    await page.fill('input[placeholder="请输入主客人姓名"]', '张三')
    await page.fill('input[placeholder="请输入联系电话"]', '13800138000')
    await page.fill('input[placeholder="请输入邮箱"]', '<EMAIL>')
    
    // 提交订单
    await page.click('button:has-text("创建订单")')
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toContainText('订单创建成功')
    
    // 验证跳转到订单管理页面
    await page.waitForURL(/\/booking\/order-management/)
  })

  test('应该验证必填字段', async ({ page }) => {
    await page.goto('/booking/orders/create')
    
    // 直接点击创建订单按钮
    await page.click('button:has-text("创建订单")')
    
    // 验证错误消息
    await expect(page.locator('.el-form-item__error')).toHaveCount(3) // 至少3个必填字段错误
  })

  test('应该能够添加和删除客人', async ({ page }) => {
    await page.goto('/booking/orders/create')
    
    // 先完成基本流程到客人信息部分
    await page.fill('input[placeholder*="输入酒店名称"]', '希尔顿酒店')
    await page.click('button:has-text("搜索")')
    await page.waitForSelector('.hotel-results')
    await page.click('.hotel-results .el-table__row:first-child button:has-text("选择")')
    
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const checkIn = tomorrow.toISOString().split('T')[0]
    
    const dayAfterTomorrow = new Date()
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2)
    const checkOut = dayAfterTomorrow.toISOString().split('T')[0]
    
    await page.fill('input[placeholder="选择入住日期"]', checkIn)
    await page.fill('input[placeholder="选择离店日期"]', checkOut)
    await page.waitForSelector('.room-types')
    await page.click('.room-types .el-table__row:first-child button:has-text("选择")')
    
    // 添加客人
    await page.click('button:has-text("添加客人")')
    
    // 验证客人表格出现
    await expect(page.locator('.guests-section')).toBeVisible()
    await expect(page.locator('.guests-section .el-table__row')).toHaveCount(1)
    
    // 再添加一个客人
    await page.click('button:has-text("添加客人")')
    await expect(page.locator('.guests-section .el-table__row')).toHaveCount(2)
    
    // 删除一个客人
    await page.click('.guests-section .el-table__row:first-child button:has-text("删除")')
    await expect(page.locator('.guests-section .el-table__row')).toHaveCount(1)
  })

  test('应该正确计算费用', async ({ page }) => {
    await page.goto('/booking/orders/create')
    
    // 完成基本流程
    await page.fill('input[placeholder*="输入酒店名称"]', '希尔顿酒店')
    await page.click('button:has-text("搜索")')
    await page.waitForSelector('.hotel-results')
    await page.click('.hotel-results .el-table__row:first-child button:has-text("选择")')
    
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const checkIn = tomorrow.toISOString().split('T')[0]
    
    const dayAfterTomorrow = new Date()
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2)
    const checkOut = dayAfterTomorrow.toISOString().split('T')[0]
    
    await page.fill('input[placeholder="选择入住日期"]', checkIn)
    await page.fill('input[placeholder="选择离店日期"]', checkOut)
    await page.waitForSelector('.room-types')
    await page.click('.room-types .el-table__row:first-child button:has-text("选择")')
    
    // 验证费用信息显示
    await expect(page.locator('.price-summary')).toBeVisible()
    await expect(page.locator('.price-summary .price-item')).toHaveCount(4) // 房费、税费、服务费、总额
    
    // 修改房间数，验证费用更新
    await page.fill('.el-input-number input', '2')
    
    // 验证总额更新（这里需要根据实际价格计算逻辑调整）
    await expect(page.locator('.price-item.total .amount')).not.toHaveText('¥0.00')
  })
})
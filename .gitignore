# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.class

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

goweb.app
result.xml
node_modules
.DS_Store
.venv
*.zap
supplier/java-demo/target
bleve_index/
geography/bleve_index/*
hotel-be-dev
.idea/
tmp/
.tmp/
#geography/config/country*.json
# 忽略个人IDE配置
.idea/workspace.xml
.idea/modules.xml
build/ddl/hoteldb/migrations/*

# Debug and log files
*.log
*_debug.log
*_debug_output.json
npminstall-debug.log
cypress_debug_output.json
cypress_e2e_debug.log

# Temporary files
*.tmp
*.swp
*.swo
*~
Thumbs.db

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Test results and coverage
test-results/
coverage/
*.coverage

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/


__debug**
.config/
hotel-api
admin-fe/playwright-report/*
admin-fe/test-results/*
# Database backup files
*.sql
hotel_tables_backup.sql

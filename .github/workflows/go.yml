# This workflow will build a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: Go

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:

  build:
    runs-on: ubuntu-latest
    env:
      ENV: CI
      PROJECT_PATH: ${{ github.workspace }}
    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.24.2'

    - name: Build
      run: go build -o goweb.app api/api.go

    - name: Test
      run: go test -v ./...

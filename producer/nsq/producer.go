package nsq

import (
	"context"
	"fmt"

	"hotel/common/log"

	"github.com/nsqio/go-nsq"
)

// NSQConfig NSQ配置
type NSQConfig struct {
	NSQDAddress      string   `yaml:"nsqd_address" json:"nsqd_address"`
	LookupdAddresses []string `yaml:"lookupd_addresses" json:"lookupd_addresses"`
	MaxInFlight      int      `yaml:"max_in_flight" json:"max_in_flight"`
	MaxAttempts      uint16   `yaml:"max_attempts" json:"max_attempts"`
}

// ProducerConfig 生产者配置
type ProducerConfig struct {
	NSQDAddress      string   `yaml:"nsqd_address" json:"nsqd_address"`
	LookupdAddresses []string `yaml:"lookupd_addresses" json:"lookupd_addresses"`
}

// PublishOptions 发布选项
type PublishOptions struct {
	Headers map[string]string `json:"headers"`
}

// Producer NSQ生产者实现
type Producer struct {
	producer *nsq.Producer
}

// NewProducer 创建NSQ生产者
func NewProducer(nsqConfig *NSQConfig, producerConfig *ProducerConfig) (*Producer, error) {
	config := nsq.NewConfig()

	// 创建NSQ生产者
	producer, err := nsq.NewProducer("127.0.0.1:4150", config) // 这里应该从配置中获取
	if err != nil {
		return nil, fmt.Errorf("failed to create NSQ producer: %w", err)
	}

	return &Producer{
		producer: producer,
	}, nil
}

// Publish 发布消息到指定主题
func (p *Producer) Publish(ctx context.Context, topic string, message []byte) error {
	return p.PublishWithOptions(ctx, topic, message, nil)
}

// PublishWithOptions 带选项发布消息
func (p *Producer) PublishWithOptions(ctx context.Context, topic string, message []byte, opts *PublishOptions) error {
	if topic == "" {
		return fmt.Errorf("topic name cannot be empty")
	}

	err := p.producer.Publish(topic, message)
	if err != nil {
		log.Error("Failed to publish message to NSQ topic %s: %v", topic, err)
		return fmt.Errorf("failed to publish message: %w", err)
	}

	log.Info("Message published to NSQ topic %s", topic)
	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	if p.producer != nil {
		p.producer.Stop()
	}
	return nil
}

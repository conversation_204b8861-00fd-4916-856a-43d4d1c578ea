package config

import (
	configutils "hotel/common/config"
	httpUtil "hotel/common/httpdispatcher"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

type Config struct {
	rest.RestConf
	JwtAuth *httpUtil.JWTConfig
	MySQL   struct {
		API string `json:"api"`
	}
}

var configFile = configutils.SafeFlagString("api", "api/config/config.yaml", "the config file")

func Init() Config {
	var c Config
	conf.MustLoad(*configFile, &c)
	return c
}

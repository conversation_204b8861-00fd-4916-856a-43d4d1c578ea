Name: hotel-api
Host: 0.0.0.0
Port: 80
MaxConns: 100
DevServer:
  Enabled: true
  Port: 9091
  HealthPath: "/ping"
JwtAuth:
  secret: "your-secret-key-至少32位" # 生产环境建议使用环境变量
  expiresIn: "604800s"                    # 7天（单位：秒）
  signingMethod: "HS256"# 签名算法
  issuer: "<EMAIL>" # 签发者
  audience: "demo" # 接收方
Timeout: 30000
MySQL:
  API: admin:Amit2025@tcp(hoteltest.chayosky20ji.ap-southeast-1.rds.amazonaws.com:3306)/hoteltest?charset=utf8mb4&parseTime=True&loc=UTC&timeout=60s&writeTimeout=60s&readTimeout=60s

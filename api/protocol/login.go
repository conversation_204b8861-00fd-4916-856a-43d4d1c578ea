package protocol

import (
	"hotel/user/domain"
)

type LoginReq struct {
	Email    string `json:"email" required:"true"`
	Password string `json:"password" required:"true"`
	TTL      int64  `json:"ttl"` // now allowed
}
type LoginResp struct {
	Token string       `json:"token"`
	User  *domain.User `json:"user"`
}

type TicketReq struct {
	AppKey    string `json:"appKey" required:"true"`
	AppSecret string `json:"appSecret" required:"true"`
	TTL       int64  `json:"ttl"` // default 1 hour
}
type TicketResp struct {
	Ticket string `json:"ticket"`
}

type ForgetPasswordReq struct {
	Email string `json:"email"`
}
type ForgetPasswordResp struct {
}

type ResetPasswordReq struct {
	Operator    *domain.User `json:"operator" apidoc:"-"`
	NewPassword string       `json:"newPassword"`
	OldPassword string       `json:"oldPassword"`
}
type ResetPasswordResp struct {
}

package protocol

import (
	"hotel/common/bff"
	"hotel/common/i18n"
	"hotel/common/pagehelper"
)

// ListStarlingReq 查询多语言配置列表请求
type ListStarlingReq struct {
	EntityId    *uint64 `json:"entityId,omitempty"`    // 实体ID，可选
	Application string  `json:"application,omitempty"` // 应用名称
	Space       string  `json:"space,omitempty"`       // 空间名称
	Key         string  `json:"key,omitempty"`         // 键名称
	Keyword     string  `json:"keyword,omitempty"`     // 关键词搜索（搜索key、zh、en字段）
	Page        *pagehelper.PageReq `json:"page,omitempty"` // 分页参数
}

// ListStarlingResp 查询多语言配置列表响应
type ListStarlingResp struct {
	Table bff.Table[StarlingItem] `json:"table"`
}

// StarlingItem 多语言配置项
type StarlingItem struct {
	Id          uint64    `json:"id"`
	EntityId    uint64    `json:"entityId"`
	Application string    `json:"application"`
	Space       string    `json:"space"`
	Key         string    `json:"key"`
	Content     i18n.I18N `json:"content"`    // 使用统一的i18n结构
	CreateTime  string    `json:"createTime"`
	UpdateTime  string    `json:"updateTime"`
}

// GetStarlingReq 获取单个多语言配置请求
type GetStarlingReq struct {
	Id uint64 `json:"id"`
}

// GetStarlingResp 获取单个多语言配置响应
type GetStarlingResp struct {
	Item *StarlingItem `json:"item"`
}

// CreateStarlingReq 创建多语言配置请求
type CreateStarlingReq struct {
	EntityId    uint64    `json:"entityId"`
	Application string    `json:"application"`
	Space       string    `json:"space"`
	Key         string    `json:"key"`
	Content     i18n.I18N `json:"content"` // 使用统一的i18n结构
}

// CreateStarlingResp 创建多语言配置响应
type CreateStarlingResp struct {
	Id uint64 `json:"id"`
}

// UpdateStarlingReq 更新多语言配置请求
type UpdateStarlingReq struct {
	Id          uint64    `json:"id"`
	EntityId    uint64    `json:"entityId"`
	Application string    `json:"application"`
	Space       string    `json:"space"`
	Key         string    `json:"key"`
	Content     i18n.I18N `json:"content"` // 使用统一的i18n结构
}

// UpdateStarlingResp 更新多语言配置响应
type UpdateStarlingResp struct {
	// 成功时返回空响应体，失败时通过HTTP状态码和错误信息表达
}

// DeleteStarlingReq 删除多语言配置请求
type DeleteStarlingReq struct {
	Id uint64 `json:"id"`
}

// DeleteStarlingResp 删除多语言配置响应
type DeleteStarlingResp struct {
	// 成功时返回空响应体，失败时通过HTTP状态码和错误信息表达
}

// BatchDeleteStarlingReq 批量删除多语言配置请求
type BatchDeleteStarlingReq struct {
	Ids []uint64 `json:"ids"`
}

// BatchDeleteStarlingResp 批量删除多语言配置响应
type BatchDeleteStarlingResp struct {
	// 成功时返回空响应体，失败时通过HTTP状态码和错误信息表达
}

// ExportStarlingReq 导出多语言配置请求
type ExportStarlingReq struct {
	EntityId    *uint64 `json:"entityId,omitempty"`
	Application string  `json:"application,omitempty"`
	Space       string  `json:"space,omitempty"`
}

// ExportStarlingResp 导出多语言配置响应
type ExportStarlingResp struct {
	FileURL string `json:"fileUrl"`
}

// ImportStarlingReq 导入多语言配置请求
type ImportStarlingReq struct {
	FileURL string `json:"fileUrl"`
	Replace bool   `json:"replace"` // 是否替换已存在的配置
}

// ImportStarlingResp 导入多语言配置响应
type ImportStarlingResp struct {
	Success     bool  `json:"success"`
	ImportCount int64 `json:"importCount"`
	UpdateCount int64 `json:"updateCount"`
}

// GetApplicationListReq 获取应用列表请求
type GetApplicationListReq struct {
	EntityId *uint64 `json:"entityId,omitempty"`
}

// GetApplicationListResp 获取应用列表响应
type GetApplicationListResp struct {
	Applications []string `json:"applications"`
}

// GetSpaceListReq 获取空间列表请求
type GetSpaceListReq struct {
	EntityId    *uint64 `json:"entityId,omitempty"`
	Application string  `json:"application"`
}

// GetSpaceListResp 获取空间列表响应
type GetSpaceListResp struct {
	Spaces []string `json:"spaces"`
}

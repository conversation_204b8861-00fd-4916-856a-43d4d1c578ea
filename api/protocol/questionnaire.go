package protocol

import (
	"hotel/common/pagehelper"
	"time"
)

// SubmitQuestionnaireRequest 提交问卷请求
type SubmitQuestionnaireRequest struct {
	QuestionnaireName string                 `json:"questionnaireName"` // 问卷名称
	SubmitterEmail    string                 `json:"submitterEmail"`    // 填写人ID
	Answers           map[string]interface{} `json:"answers"`           // 问卷回答内容JSON
}

// SubmitQuestionnaireResponse 提交问卷响应
type SubmitQuestionnaireResponse struct {
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
}

// GetQuestionnaireStatsRequest 获取问卷统计信息请求
type GetQuestionnaireStatsRequest struct {
}

// GetQuestionnaireStatsResponse 获取问卷统计信息响应
type GetQuestionnaireStatsResponse struct {
	TotalSubmissions int64 `json:"totalSubmissions"`
}

// ListQuestionnairesRequest 问卷列表查询请求
type ListQuestionnairesRequest struct {
	pagehelper.PageReq
	StartDate string `json:"startDate"` // 开始日期 YYYY-MM-DD
	EndDate   string `json:"endDate"`   // 结束日期 YYYY-MM-DD
}

// ListQuestionnairesResponse 问卷列表查询响应
type ListQuestionnairesResponse struct {
	Total int64                `json:"total"`
	List  []*QuestionnaireItem `json:"list"`
}

// QuestionnaireItem 问卷列表项
type QuestionnaireItem struct {
	Id                int64                  `json:"id"`
	QuestionnaireName string                 `json:"questionnaireName"`
	SubmitterEmail    string                 `json:"submitterEmail"`
	Answers           map[string]interface{} `json:"answers"`
	RequestId         string                 `json:"requestId"`
	Status            int                    `json:"status"`
	CreateTime        time.Time              `json:"createTime"`
}

package protocol

import (
	"hotel/common/bff"
	"hotel/common/i18n"
	"hotel/user/domain"
)

type AdminHomepageReq struct {
	*domain.User `apidoc:"-"`
}

type AdminHomepageResp struct {
	DownloadButtonURLs map[string]string `json:"downloadButtonURLs"` // button-id -> url
	StarlingMap        i18n.Map          `json:"starlingMap"`         // 根据 key 做翻译
}

type InternalHomepageReq struct {
	*domain.User `apidoc:"-"`
}

type InternalHomepageResp struct {
	DownloadButtonURLs map[string]string `json:"downloadButtonURLs"` // button-id -> url
	MenuRoutes         []*bff.MenuRoute  `json:"menuRoutes,omitzero"`
	StarlingMap        i18n.Map          `json:"starlingMap"` // 根据 key 做翻译
}

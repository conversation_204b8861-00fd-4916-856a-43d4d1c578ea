package service

import (
	"context"

	biService "hotel/bi/service"
)

// LogViewerService API层的日志查看器服务包装器
type LogViewerService struct {
	biLogViewerSrv *biService.LogViewerService
}

func NewLogViewerService() *LogViewerService {
	return &LogViewerService{
		biLogViewerSrv: biService.NewLogViewerService(),
	}
}

func (s *LogViewerService) Name() string {
	return "logViewer"
}

// QueryLogs 查询日志列表
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) QueryLogs(ctx context.Context, req *biService.QueryLogsReq) (*biService.QueryLogsResp, error) {
	return s.biLogViewerSrv.QueryLogs(ctx, req)
}

// GetLogDetail 获取日志详情
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) GetLogDetail(ctx context.Context, req *biService.GetLogDetailReq) (*biService.GetLogDetailResp, error) {
	return s.biLogViewerSrv.GetLogDetail(ctx, req)
}

// ExportLogs 导出日志
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) ExportLogs(ctx context.Context, req *biService.ExportLogsReq) (*biService.ExportLogsResp, error) {
	return s.biLogViewerSrv.ExportLogs(ctx, req)
}

// GetLogStatistics 获取日志统计
// @tags: internal.hotelbyte.com
// @auth: true
func (s *LogViewerService) GetLogStatistics(ctx context.Context, req *biService.GetLogStatisticsReq) (*biService.GetLogStatisticsResp, error) {
	return s.biLogViewerSrv.GetLogStatistics(ctx, req)
}

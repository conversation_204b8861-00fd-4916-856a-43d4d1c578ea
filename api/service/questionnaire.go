package service

import (
	"context"
	"encoding/json"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/api/config"
	"hotel/api/mysql"
	"hotel/api/protocol"
	"hotel/common/bizerr"
	"hotel/common/log"
)

// QuestionnaireService 问卷服务
type QuestionnaireService struct {
	dao *mysql.QuestionnaireDAO
}

// NewQuestionnaireService 使用配置创建问卷服务实例
func NewQuestionnaireService(cfg config.Config) *QuestionnaireService {
	db := sqlx.NewMysql(cfg.MySQL.API)
	dao := mysql.NewQuestionnaireDAO(db)
	return &QuestionnaireService{
		dao: dao,
	}
}

// Name 返回服务名称
func (s *QuestionnaireService) Name() string {
	return "questionnaire"
}

// SubmitQuestionnaire 提交问卷
// @auth: false
func (s *QuestionnaireService) SubmitQuestionnaire(ctx context.Context, req *protocol.SubmitQuestionnaireRequest) (*protocol.SubmitQuestionnaireResponse, error) {
	// 生成请求ID
	requestID := log.GetOrNewLogidFromContext(ctx)
	// 验证请求数据
	if req.QuestionnaireName == "" {
		return nil, bizerr.ParamErr.WithMessage("问卷名称不能为空")
	}

	if req.SubmitterEmail == "" {
		return nil, bizerr.ParamErr.WithMessage("用户邮箱无效")
	}

	if len(req.Answers) == 0 {
		return nil, bizerr.ParamErr.WithMessage("问卷回答内容不能为空")
	}

	// 创建问卷记录
	createReq := &mysql.CreateQuestionnaireRequest{
		RequestID:         requestID,
		QuestionnaireName: req.QuestionnaireName,
		SubmitterEmail:    req.SubmitterEmail,
		Answers:           req.Answers,
	}

	questionnaire, err := s.dao.CreateQuestionnaire(ctx, createReq)
	if err != nil {
		logx.Errorf("创建问卷记录失败: %v", err)
		return &protocol.SubmitQuestionnaireResponse{
			Message: "问卷提交失败，请稍后重试",
		}, nil
	}

	// 记录日志
	logx.Infof("问卷提交成功: %s, ID: %d, 问卷名称: %s, 用户邮箱: %s", requestID, questionnaire.Id, req.QuestionnaireName, req.SubmitterEmail)

	return &protocol.SubmitQuestionnaireResponse{
		Message:   "问卷提交成功",
		RequestId: requestID,
	}, nil
}

// GetQuestionnaireStats 获取问卷统计信息（管理员接口）
// @auth: false
func (s *QuestionnaireService) GetQuestionnaireStats(ctx context.Context, req *protocol.GetQuestionnaireStatsRequest) (*protocol.GetQuestionnaireStatsResponse, error) {
	base := 71
	total, err := s.dao.CountQuestionnaires(ctx)
	if err != nil {
		return nil, err
	}
	return &protocol.GetQuestionnaireStatsResponse{
		TotalSubmissions: total + int64(base),
	}, nil
}

// ListQuestionnaires 分页查询问卷列表
// @auth: true
func (s *QuestionnaireService) ListQuestionnaires(ctx context.Context, req *protocol.ListQuestionnairesRequest) (*protocol.ListQuestionnairesResponse, error) {
	listReq := &mysql.QuestionnaireListRequest{
		Page:      req.PageNum,
		PageSize:  req.PageSize,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	// 设置默认值
	if listReq.Page <= 0 {
		listReq.Page = 1
	}
	if listReq.PageSize <= 0 {
		listReq.PageSize = 20
	}
	if listReq.PageSize > 100 {
		listReq.PageSize = 100
	}

	result, err := s.dao.ListQuestionnaires(ctx, listReq)
	if err != nil {
		logx.Errorf("查询问卷列表失败: %v", err)
		return nil, err
	}

	// 转换为响应格式
	list := make([]*protocol.QuestionnaireItem, 0, len(result.List))
	for _, item := range result.List {
		// 解析JSON格式的answers字段
		var answers map[string]interface{}
		if err := json.Unmarshal([]byte(item.Answers), &answers); err != nil {
			answers = make(map[string]interface{})
		}

		list = append(list, &protocol.QuestionnaireItem{
			Id:                item.Id,
			QuestionnaireName: item.QuestionnaireName,
			SubmitterEmail:    item.SubmitterEmail,
			Answers:           answers,
			RequestId:         item.RequestId,
			Status:            int(item.Status),
			CreateTime:        item.CreateTime,
		})
	}

	return &protocol.ListQuestionnairesResponse{
		Total: result.Total,
		List:  list,
	}, nil
}

package service

import (
	"context"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"
	"math/rand"
	"time"

	"hotel/common/log"

	"hotel/api/protocol"
	"hotel/common/httpdispatcher"
	"hotel/common/money"
)

type FunService struct {
	exchangeRateService money.ExchangeService
}

func (s *FunService) Name() string {
	return "fun"
}

func NewFunService() *FunService {
	return &FunService{
		exchangeRateService: money.NewExchangeService(),
	}
}

func (s *FunService) AskJSON(ctx context.Context, req *protocol.FunReq) (*protocol.FunResp, error) {
	header := httpdispatcher.CtxGetHeader(ctx)
	log.Infoc(ctx, "AskJSON got header %#v", header)
	time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)

	j := gjson.Parse(req.Q)
	udf := j.Get("udf").String()
	switch udf {
	case "ExchangeRate":
		d, err := decimal.NewFromString(j.Get("amount").String())
		if err != nil {
			return nil, err
		}
		v, err := s.exchangeRateService.Exchange(ctx, j.Get("from").String(), j.Get("to").String(), d)
		if err != nil {
			return nil, err
		}
		return &protocol.FunResp{A: v.String()}, nil
	}
	return &protocol.FunResp{A: "我听不懂呢，你找Danceiny吧"}, nil
}

package service

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/crypto/bcrypt"

	apiProto "hotel/api/protocol"
	"hotel/common/bff"
	httpUtil "hotel/common/httpdispatcher"
	"hotel/user/domain"
	userProto "hotel/user/protocol"
	userSrv "hotel/user/service"
)

type AuthService struct {
	userSrv *userSrv.UserService
	jwt     *httpUtil.JwtPlugin
}

func NewAuthService(jwt *httpUtil.JwtPlugin, userSrv *userSrv.UserService) *AuthService {
	return &AuthService{jwt: jwt, userSrv: userSrv}
}
func (s *AuthService) Name() string {
	return "auth"
}

// Ticket
// @desc: Get your api access ticket by your apiKey and apiSecret. You're suggested to use caching tools to cache the ticket to prevent calling Ticket before every call. Since the returned ticket will be expired after TTL you specified in request (default as 1 hour) . If you have no apiKey or apiSecret, please contact your tenant team.
// @tags: openapi
// @auth: false
func (s *AuthService) Ticket(ctx context.Context, req *apiProto.TicketReq) (*apiProto.TicketResp, error) {
	resp, err := s.Login(ctx, &apiProto.LoginReq{
		Email:    req.AppKey,
		Password: req.AppSecret,
		TTL:      req.TTL,
	})
	if err != nil {
		return nil, err
	}
	return &apiProto.TicketResp{
		Ticket: resp.Token,
	}, nil
}

// Login
// @tags: internal.hotelbyte.com
// @auth: false
func (s *AuthService) Login(ctx context.Context, req *apiProto.LoginReq) (*apiProto.LoginResp, error) {
	user, err := func() (*domain.User, error) {
		// hard code super admin
		cfg := s.jwt.GetConfig()
		if req.Email == cfg.Issuer {
			return domain.GetSuperAdminUser(cfg.Issuer, s.jwt.GetConfig().Secret), nil
		}
		userResp, err := s.userSrv.GetUser(ctx, &userProto.GetUserReq{Key: req.Email})
		if err != nil {
			return nil, err
		}
		return userResp.User, nil
	}()
	if err != nil {
		return nil, err
	}
	if err = bcrypt.CompareHashAndPassword([]byte(user.Secret), []byte(req.Password)); err != nil {
		logx.WithContext(ctx).Infof("password verify failed:%v", err)
		return nil, domain.ErrAuthentication
	}

	jwtToken, err := s.jwt.Generate(user, req.TTL)
	if err != nil {
		return nil, err
	}

	return &apiProto.LoginResp{Token: jwtToken, User: user}, nil
}
func (s *AuthService) ForgetPassword(ctx context.Context, req *apiProto.ForgetPasswordReq) (*apiProto.ForgetPasswordResp, error) {
	return &apiProto.ForgetPasswordResp{}, nil
}
func (s *AuthService) ResetPassword(ctx context.Context, req *apiProto.ResetPasswordReq) (*apiProto.ResetPasswordResp, error) {
	ub := req.Operator.UserBasic
	if ub.CompareHashAndPassword(ub.Secret, req.OldPassword) {
		return nil, domain.ErrAuthentication.WithMessage("old password not match")
	}

	_, err := s.userSrv.UpdateUser(ctx, &userProto.UpdateUserReq{
		Operator:     req.Operator,
		TargetUserId: req.Operator.ID,
		Actions: []userProto.UpdateUserAction{
			{
				Action: bff.ActionId_UpdateUserBasicProfile,
				Params: &userProto.UpdateUserBasicValue{
					Secret: req.NewPassword,
				},
			},
		},
	})
	return &apiProto.ResetPasswordResp{}, err
}

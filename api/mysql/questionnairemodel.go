package mysql

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type (
	QuestionnaireModel struct {
		*defaultQuestionnaireModel
	}
)

// NewQuestionnaireModel returns a model for the database table.
func NewQuestionnaireModel(conn sqlx.SqlConn) *QuestionnaireModel {
	return &QuestionnaireModel{
		defaultQuestionnaireModel: newQuestionnaireModel(conn),
	}
}

func (m *QuestionnaireModel) withSession(session sqlx.Session) *QuestionnaireModel {
	return NewQuestionnaireModel(sqlx.NewSqlConnFromSession(session))
}

func (m *QuestionnaireModel) Count(ctx context.Context) (int64, error) {
	query := fmt.Sprintf("select count(1) from %s where `is_deleted` = 0", m.tableName())
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query)
	if err != nil {
		return 0, err
	}
	return count, nil
}

# 动态问卷系统数据库设计

## 概述

本文档描述了动态问卷系统的MySQL数据库设计和CRUD操作实现。新的设计采用了更加灵活的结构：**问卷名 + 填写人ID + 问卷回答内容JSON**，支持任意类型的问卷和动态的问题结构。

## 数据库表结构

### 1. questionnaire 表（动态问卷主表）

```sql
CREATE TABLE questionnaire (
    id                BIGINT AUTO_INCREMENT PRIMARY KEY,
    questionnaire_name VARCHAR(128)    NOT NULL COMMENT '问卷名称',
    submitter_email    VARCHAR(128)    NOT NULL COMMENT '填写人邮箱',
    answers           JSON             NOT NULL COMMENT '问卷回答内容JSON',
    request_id        VARCHAR(64)      NOT NULL COMMENT '问卷请求ID',
    status            TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    is_deleted        TINYINT          NOT NULL DEFAULT 0,
    create_time       TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time       TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 主键，自增ID
- `questionnaire_name`: 问卷名称，用于区分不同类型的问卷
- `submitter_email`: 填写人邮箱
- `answers`: JSON格式存储的问卷回答内容，支持任意结构
- `request_id`: 问卷请求唯一标识符
- `status`: 记录状态（1-正常，0-禁用）
- `is_deleted`: 软删除标记
- `create_time`: 创建时间
- `update_time`: 更新时间

**索引：**
- `uk_request_id`: request_id唯一索引
- `idx_questionnaire_name`: 问卷名称索引，用于按问卷类型查询
- `idx_user_id`: 用户ID索引，用于查询用户的所有问卷
- `idx_create_time`: 创建时间索引，用于时间范围查询
- `idx_status`: 状态索引，用于状态过滤
- `idx_name_user`: 复合索引，用于查询特定用户的特定问卷

### 2. questionnaire_stats 表（统计表）

```sql
CREATE TABLE questionnaire_stats (
    id           BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_date    DATE            NOT NULL COMMENT '统计日期',
    platform     VARCHAR(64)     NOT NULL COMMENT '平台名称',
    submit_count INT UNSIGNED    NOT NULL DEFAULT 0 COMMENT '提交次数',
    create_time  TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time  TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 主键，自增ID
- `stat_date`: 统计日期
- `platform`: 平台名称
- `submit_count`: 该平台在该日期的提交次数
- `create_time`: 创建时间
- `update_time`: 更新时间

**索引：**
- `uk_date_platform`: (stat_date, platform)唯一索引
- `idx_stat_date`: 统计日期索引
- `idx_platform`: 平台索引

## 代码结构

### 1. 数据模型层

- `questionnairemodel_gen.go`: goctl自动生成的基础CRUD模型
- `questionnairestatsmodel_gen.go`: 统计表的基础CRUD模型
- `questionnaire_dao.go`: 扩展的数据访问层，提供复杂查询功能

### 2. 服务层

- `questionnaire.go`: 问卷服务主文件，包含基础的提交和统计功能
- `questionnaire_crud.go`: 扩展的CRUD操作，包含列表查询、详情查询、更新、删除等

### 3. 协议层

- `questionnaire.go`: 定义了所有API的请求和响应结构体

## CRUD 操作说明

### 1. Create（创建）

**API**: `SubmitQuestionnaire`

**功能**: 提交新的问卷记录

**请求参数**:
```json
{
  "platforms": ["booking.com", "expedia"],
  "priorities": "价格优先，服务质量其次",
  "email": "<EMAIL>",
  "name": "张三"
}
```

**响应**:
```json
{
  "success": true,
  "message": "问卷提交成功",
  "request_id": "quest_1234567890"
}
```

### 2. Read（查询）

#### 2.1 列表查询

**API**: `ListQuestionnaires`

**功能**: 分页查询问卷列表，支持多种过滤条件

**请求参数**:
```json
{
  "page": 1,
  "page_size": 20,
  "email": "<EMAIL>",
  "platform": "booking.com",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

**响应**:
```json
{
  "total": 100,
  "list": [
    {
      "id": 1,
      "request_id": "quest_1234567890",
      "platforms": ["booking.com", "expedia"],
      "priorities": "价格优先",
      "email": "<EMAIL>",
      "name": "张三",
      "status": 1,
      "create_time": 1640995200,
      "created_at": "2024-01-01 12:00:00"
    }
  ]
}
```

#### 2.2 详情查询

**API**: `GetQuestionnaireDetail`

**功能**: 根据ID或request_id查询问卷详情

**请求参数**:
```json
{
  "id": 1
}
```

或

```json
{
  "request_id": "quest_1234567890"
}
```

#### 2.3 统计查询

**API**: `GetQuestionnaireStats`

**功能**: 获取问卷统计信息

**响应**:
```json
{
  "total_submissions": 1000,
  "platform_stats": {
    "booking.com": 400,
    "expedia": 300,
    "airbnb": 200,
    "hotels.com": 100
  },
  "recent_submissions": [
    {
      "request_id": "quest_1234567890",
      "platforms": ["booking.com"],
      "email": "<EMAIL>",
      "created_at": "2024-01-01 12:00:00"
    }
  ]
}
```

### 3. Update（更新）

**API**: `UpdateQuestionnaireStatus`

**功能**: 更新问卷状态

**请求参数**:
```json
{
  "id": 1,
  "status": 0
}
```

### 4. Delete（删除）

#### 4.1 单个删除

**API**: `DeleteQuestionnaire`

**功能**: 软删除单个问卷

**请求参数**:
```json
{
  "id": 1
}
```

#### 4.2 批量删除

**API**: `BatchDeleteQuestionnaires`

**功能**: 批量软删除问卷

**请求参数**:
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**响应**:
```json
{
  "success": true,
  "message": "批量删除完成",
  "success_count": 4,
  "failed_ids": [3]
}
```

## 数据库配置

### 配置文件

在 `api/config/config.yaml` 中添加MySQL配置：

```yaml
MySQL:
  API: admin:password@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=UTC
```

### 初始化

服务启动时会自动初始化数据库连接：

```go
// 在 api/service/init.go 中
questionnaireSrv := NewQuestionnaireServiceWithConfig(c)
```

## 使用示例

### 1. 创建问卷表

```bash
# 执行DDL脚本
mysql -u root -p hoteldev < api/mysql/questionnaire.sql
```

### 2. 生成模型代码

```bash
# 使用goctl生成模型
goctl model mysql ddl --src ./api/mysql/questionnaire.sql --dir ./api/mysql/ -i id
```

### 3. 启动服务

```bash
# 启动API服务
go run api/api.go -f api/config/config.yaml
```

### 4. 测试API

```bash
# 提交问卷
curl -X POST http://localhost:8082/api/questionnaire/SubmitQuestionnaire \
  -H "Content-Type: application/json" \
  -d '{
    "platforms": ["booking.com", "expedia"],
    "priorities": "价格优先，服务质量其次",
    "email": "<EMAIL>",
    "name": "测试用户"
  }'

# 查询问卷列表
curl -X POST http://localhost:8082/api/questionnaire/ListQuestionnaires \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "page": 1,
    "page_size": 10
  }'

# 获取统计信息
curl -X POST http://localhost:8082/api/questionnaire/GetQuestionnaireStats \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{}'
```

## 注意事项

1. **权限控制**: 除了`SubmitQuestionnaire`接口外，其他管理接口都需要JWT认证
2. **软删除**: 删除操作采用软删除方式，只修改`is_deleted`和`status`字段
3. **JSON存储**: 平台信息使用JSON格式存储，便于查询和扩展
4. **统计更新**: 问卷提交时会异步更新统计表
5. **分页限制**: 列表查询最大页面大小限制为100条
6. **索引优化**: 根据查询模式建立了合适的索引，提高查询性能

## 扩展建议

1. **缓存**: 可以添加Redis缓存来提高统计查询性能
2. **异步处理**: 可以使用消息队列来异步处理统计更新
3. **数据归档**: 可以定期归档历史数据到其他存储系统
4. **监控**: 添加数据库性能监控和慢查询日志
5. **备份**: 建立定期备份机制
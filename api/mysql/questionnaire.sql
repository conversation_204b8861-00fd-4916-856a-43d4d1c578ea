-- Active: 1753119638455@@hoteltest.chayosky20ji.ap-southeast-1.rds.amazonaws.com@3306
-- 动态问卷表
DROP TABLE IF EXISTS `questionnaire`;

CREATE TABLE IF NOT EXISTS `questionnaire` (
    `id` BIGINT AUTO_INCREMENT,
    `questionnaire_name` VARCHAR(128) NOT NULL COMMENT '问卷名称',
    `submitter_email` VARCHAR(128) NOT NULL COMMENT '填写人邮箱',
    `answers` JSON COMMENT '问卷回答内容JSON',
    `request_id` VARCHAR(64) NOT NULL COMMENT '问卷请求ID',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：1-正常',
    `is_deleted` TINYINT NOT NULL DEFAULT 0,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_request_id` (`request_id`),
    INDEX `idx_questionnaire_name` (`questionnaire_name`),
    INDEX `idx_submitter_email` (`submitter_email`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`)
) COMMENT = '动态问卷提交记录表' ENGINE = InnoDB;
// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.3

package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	questionnaireFieldNames          = builder.RawFieldNames(&Questionnaire{})
	questionnaireRows                = strings.Join(questionnaireFieldNames, ",")
	questionnaireRowsExpectAutoSet   = strings.Join(stringx.Remove(questionnaireFieldNames, "`id`", "`id`"), ",")
	questionnaireRowsWithPlaceHolder = strings.Join(stringx.Remove(questionnaireFieldNames, "`id`", "`id`"), "=?,") + "=?"
)

type (
	questionnaireModel interface {
		Insert(ctx context.Context, data *Questionnaire) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Questionnaire, error)
		FindOneByRequestId(ctx context.Context, requestId string) (*Questionnaire, error)
		Update(ctx context.Context, data *Questionnaire) error
		Delete(ctx context.Context, id int64) error
	}

	defaultQuestionnaireModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Questionnaire struct {
		Id                int64     `db:"id"`
		QuestionnaireName string    `db:"questionnaire_name"` // 问卷名称
		SubmitterEmail    string    `db:"submitter_email"`    // 填写人ID
		Answers           string    `db:"answers"`            // 问卷回答内容JSON
		RequestId         string    `db:"request_id"`         // 问卷请求ID
		Status            uint64    `db:"status"`             // 状态：1-正常，0-删除
		IsDeleted         int64     `db:"is_deleted"`
		CreateTime        time.Time `db:"create_time"`
		UpdateTime        time.Time `db:"update_time"`
	}
)

func newQuestionnaireModel(conn sqlx.SqlConn) *defaultQuestionnaireModel {
	return &defaultQuestionnaireModel{
		conn:  conn,
		table: "`questionnaire`",
	}
}

func (m *defaultQuestionnaireModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultQuestionnaireModel) FindOne(ctx context.Context, id int64) (*Questionnaire, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", questionnaireRows, m.table)
	var resp Questionnaire
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultQuestionnaireModel) FindOneByRequestId(ctx context.Context, requestId string) (*Questionnaire, error) {
	var resp Questionnaire
	query := fmt.Sprintf("select %s from %s where `request_id` = ? limit 1", questionnaireRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, requestId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultQuestionnaireModel) Insert(ctx context.Context, data *Questionnaire) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, questionnaireRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.QuestionnaireName, data.SubmitterEmail, data.Answers, data.RequestId, data.Status, data.IsDeleted, data.CreateTime, data.UpdateTime)
	return ret, err
}

func (m *defaultQuestionnaireModel) Update(ctx context.Context, newData *Questionnaire) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, questionnaireRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.QuestionnaireName, newData.SubmitterEmail, newData.Answers, newData.RequestId, newData.Status, newData.IsDeleted, newData.CreateTime, newData.UpdateTime, newData.Id)
	return err
}

func (m *defaultQuestionnaireModel) tableName() string {
	return m.table
}

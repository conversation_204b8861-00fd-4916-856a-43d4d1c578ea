package service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/spf13/cast"

	"hotel/trade/domain"
	"hotel/trade/protocol"

	"hotel/common/log"
)

// Cancel
// @desc: Cancel hotel order, including FULL cancel & PARTIAL cancel
// @tags: openapi,booking.hotelbyte.com/trade
// @path: /cancel
func (s *TradeService) Cancel(ctx context.Context, req *protocol.CancelReq) error {
	order, err := s.orderDao.Order.FindOne(ctx, cast.ToUint64(req.OrderId))
	if err != nil {
		return fmt.Errorf("order not found: %w", err)
	}

	status := domain.OrderStatus(order.Status)
	// 只有特定状态可以取消
	if status == domain.OrderStateCompleted || status == domain.OrderStateCancelled || status == domain.OrderStateNeedCancel {
		return fmt.Errorf("order cannot be cancelled in current state: %s", status.String())
	}
	order.Status = domain.OrderStateNeedCancel.Int64()
	if err = s.orderDao.Order.Update(ctx, order); err != nil {
		log.Errorc(ctx, "CancelOrderFailed:(%+v),UpdateError:(%+v)", order, err)
		return err
	}
	ob, _ := json.Marshal(order)
	// TODO: 重构以适配新的 CQRS 接口
	// 暂时禁用消息发布功能，避免编译错误
	log.Warnc(ctx, "Message publishing is temporarily disabled due to CQRS refactoring. Order: %+v", order)
	_ = ob // 避免未使用变量警告

	/*
		if sendErr := cqrs.Publish(CancelTopic, ob); sendErr != nil {
			log.Errorc(ctx, "CancelOrderFailed:(%+v),PubOrderError:(%+v)", order, sendErr)
			return sendErr
		} else {
			log.Warnc(ctx, "CancelOrderPubSuccess:(%+v)", order)
		}
	*/
	return nil
}

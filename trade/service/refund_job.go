package service

import (
	"context"
	"encoding/json"
	"time"

	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"

	"hotel/common/log"
)

type RefundOrder struct {
	Order      *dao.Order
	RefundType supplierDomain.RefundableMode
}

const (
	RefundTopic        = "refund_book"
	RefundTopicChannel = "refund_book_channel"
)

func (s *TradeService) startConsumerRefundMessage() error {
	// TODO: 重构以适配新的 CQRS 接口
	// 暂时禁用消费者功能，避免编译错误
	log.Info("Refund message consumer is temporarily disabled due to CQRS refactoring")
	return nil

	/*
		refundCfg := &cqrs.ConsumerConfig{
			TopicName:       RefundTopic,
			ChannelName:     RefundTopicChannel,
			MaxInFlight:     10,
			MaxAttempts:     3,
			RetryDelay:      2 * time.Second,
			DeadLetterTopic: RefundTopic + "_dead_letter",
		}

		consumer, err := cqrs.NewConsumer(nil, refundCfg, s.handleRefundMessage)
		if err != nil {
			log.Error("Failed to create consumer: %+v", err)
			return err
		}
		defer consumer.Stop()

		err = consumer.Start()
		if err != nil {
			log.Error("Failed to start consumer: %v", err)
			return err
		}
	*/

	log.Warn("refund Consumer started. Waiting for messages...")
	// 不需要永久阻塞，让主程序继续运行
	return nil
}

func (s *TradeService) handleRefundMessage(ctx context.Context, refundMsg []byte) error {
	order := &RefundOrder{}
	if err := json.Unmarshal(refundMsg, order); err != nil {
		log.Error("handleRefund:(%+v),failed:(%+v)", order, err)
		return err
	}
	order.Order.UpdateTime = time.Now()
	// todo: cancel supplier order
	if err := s.orderDao.Order.Update(context.Background(), order.Order); err != nil {
		log.Error("failed to update order status: %w", err)
	}
	return nil
}

package service

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	common "hotel/common/domain"
	"hotel/common/money"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	tradeDomain "hotel/trade/domain"
)

// TestOrderDisplayIntegration 测试订单显示的完整集成流程
func TestOrderDisplayIntegration(t *testing.T) {
	// 创建模拟的 BizInfo 数据，模拟真实的订单创建过程
	checkAvailResp := &supplierDomain.CheckAvailResp{
		Status: supplierDomain.CheckAvailStatusAvailable,
		RoomRatePkg: &supplierDomain.RoomRatePkg{
			RatePkgId: "HOTEL123_ROOM456_RATE789",
			CheckIn:   types.DateInt(20250201),
			CheckOut:  types.DateInt(20250203),
			Rate: supplierDomain.Rate{
				FinalRate: money.Money{
					Amount:   15000, // 150.00 USD
					Currency: "USD",
				},
			},
		},
		Supplier: supplierDomain.Supplier_Trip, // Ctrip
	}

	// 模拟预订人信息
	booker := supplierDomain.Booker{
		FirstName: "Alice",
		LastName:  "<PERSON>",
		Email:     "<EMAIL>",
		Phone: common.Phone{
			CountryCode:   "US",
			CountryNumber: 1,
			Number:        "5551234567",
		},
	}

	// 模拟客人信息
	guests := []supplierDomain.Guest{
		{
			FirstName:       "Alice",
			LastName:        "Johnson",
			RoomIndex:       1,
			NationalityCode: "US",
			Age:             32,
		},
		{
			FirstName:       "Bob",
			LastName:        "Johnson",
			RoomIndex:       1,
			NationalityCode: "US",
			Age:             35,
		},
		{
			FirstName:       "Charlie",
			LastName:        "Johnson",
			RoomIndex:       1,
			NationalityCode: "US",
			Age:             8,
		},
	}

	// 创建 SellerInputPayloads
	sellerPayloads := &supplierDomain.SellerInputPayloads{
		Supplier: supplierDomain.Supplier_Trip,
		Book: &supplierDomain.BookReq{
			Booker: booker,
			Guests: guests,
		},
	}

	// 创建完整的 BizInfo
	bizInfo := &tradeDomain.OrderBizInfo{
		CheckAvailResp:      checkAvailResp,
		SellerInputPayloads: sellerPayloads,
	}

	bizInfoJson, err := json.Marshal(bizInfo)
	assert.NoError(t, err)

	// 创建模拟的 DAO Order，模拟数据库中的订单记录
	daoOrder := &dao.Order{
		Id:                       98765,
		ReferenceNo:              "REF-INTEGRATION-TEST-001",
		Status:                   int64(tradeDomain.OrderStatePaid),
		BuyerCurrency:            "USD",
		BuyerAmount:              15000, // 150.00 USD
		SellerCurrency:           "USD",
		SellerAmount:             14500, // 145.00 USD
		TenantRevenueAmountUsd:   300,   // 3.00 USD
		CustomerRevenueAmountUsd: 200,   // 2.00 USD
		CheckIn:                  20250201,
		CheckOut:                 20250203,
		BizInfo:                  string(bizInfoJson),
		CreateTime:               time.Date(2024, 12, 15, 14, 30, 0, 0, time.UTC),
		UpdateTime:               time.Date(2024, 12, 15, 14, 30, 0, 0, time.UTC),
	}

	// 执行转换，模拟查询订单时的数据转换过程
	domainOrder := convertDAOOrderToDomain(daoOrder)

	// 验证基本订单信息
	assert.Equal(t, types.ID(98765), domainOrder.Id)
	assert.Equal(t, "REF-INTEGRATION-TEST-001", domainOrder.ReferenceNo)
	assert.Equal(t, tradeDomain.OrderStatePaid, domainOrder.Status)

	// 验证预订人信息是否正确提取
	assert.Equal(t, "Alice", domainOrder.Booker.FirstName)
	assert.Equal(t, "Johnson", domainOrder.Booker.LastName)
	assert.Equal(t, "<EMAIL>", domainOrder.Booker.Email)
	assert.Equal(t, "5551234567", domainOrder.Booker.Phone.Number)

	// 验证房间信息
	assert.Len(t, domainOrder.Rooms, 1)
	room := domainOrder.Rooms[0]
	assert.Equal(t, int64(1), room.RoomIndex)
	assert.Len(t, room.Guests, 3) // 3个客人

	// 验证客人信息
	guestNames := make([]string, len(room.Guests))
	guestAges := make([]int64, len(room.Guests))
	for i, guest := range room.Guests {
		guestNames[i] = guest.FirstName + " " + guest.LastName
		guestAges[i] = guest.Age
	}
	assert.Contains(t, guestNames, "Alice Johnson")
	assert.Contains(t, guestNames, "Bob Johnson")
	assert.Contains(t, guestNames, "Charlie Johnson")
	assert.Contains(t, guestAges, int64(32))
	assert.Contains(t, guestAges, int64(35))
	assert.Contains(t, guestAges, int64(8))

	// 验证 BizInfo 是否正确解析
	assert.NotNil(t, domainOrder.BizInfo)
	assert.NotNil(t, domainOrder.BizInfo.CheckAvailResp)
	assert.Equal(t, "HOTEL123_ROOM456_RATE789", domainOrder.BizInfo.CheckAvailResp.RoomRatePkg.RatePkgId)
	assert.Equal(t, types.DateInt(20250201), domainOrder.BizInfo.CheckAvailResp.RoomRatePkg.CheckIn)
	assert.Equal(t, types.DateInt(20250203), domainOrder.BizInfo.CheckAvailResp.RoomRatePkg.CheckOut)

	// 验证客人统计信息
	adultsCount, childrenCount := extractGuestInfoFromBizInfo(domainOrder.BizInfo)
	assert.Equal(t, 2, adultsCount)   // Alice (32) 和 Bob (35)
	assert.Equal(t, 1, childrenCount) // Charlie (8)

	// 验证住宿晚数计算
	nights := calculateNightsFromDates(daoOrder.CheckIn, daoOrder.CheckOut)
	assert.Equal(t, 2, nights) // 2025-02-01 到 2025-02-03 是 2 晚

	t.Logf("集成测试通过！订单信息正确提取：")
	t.Logf("- 订单ID: %d", domainOrder.Id)
	t.Logf("- 预订人: %s %s (%s)", domainOrder.Booker.FirstName, domainOrder.Booker.LastName, domainOrder.Booker.Email)
	t.Logf("- 客人数量: %d 成人, %d 儿童", adultsCount, childrenCount)
	t.Logf("- 住宿时间: %d 晚 (从 %d 到 %d)", nights, daoOrder.CheckIn, daoOrder.CheckOut)
	t.Logf("- 房间信息: %s", domainOrder.BizInfo.CheckAvailResp.RoomRatePkg.RatePkgId)
}

// TestOrderDisplayWithEmptyBizInfo 测试空 BizInfo 的处理
func TestOrderDisplayWithEmptyBizInfo(t *testing.T) {
	daoOrder := &dao.Order{
		Id:          12345,
		ReferenceNo: "REF-EMPTY-BIZINFO",
		Status:      int64(tradeDomain.OrderStateCreated),
		CheckIn:     20250101,
		CheckOut:    20250102,
		BizInfo:     "", // 空的 BizInfo
		CreateTime:  time.Now(),
	}

	domainOrder := convertDAOOrderToDomain(daoOrder)

	// 验证基本信息仍然正确
	assert.Equal(t, types.ID(12345), domainOrder.Id)
	assert.Equal(t, "REF-EMPTY-BIZINFO", domainOrder.ReferenceNo)

	// 验证空 BizInfo 的处理
	assert.Nil(t, domainOrder.BizInfo)

	// 验证默认值
	assert.Len(t, domainOrder.Rooms, 1)
	assert.Equal(t, int64(1), domainOrder.Rooms[0].RoomIndex)
	assert.Len(t, domainOrder.Rooms[0].Guests, 0)

	// 验证客人统计返回默认值
	adultsCount, childrenCount := extractGuestInfoFromBizInfo(domainOrder.BizInfo)
	assert.Equal(t, 0, adultsCount)
	assert.Equal(t, 0, childrenCount)

	t.Logf("空 BizInfo 测试通过，系统正确处理了缺失数据的情况")
}

// TestOrderDisplayWithInvalidBizInfo 测试无效 BizInfo 的处理
func TestOrderDisplayWithInvalidBizInfo(t *testing.T) {
	daoOrder := &dao.Order{
		Id:          54321,
		ReferenceNo: "REF-INVALID-BIZINFO",
		Status:      int64(tradeDomain.OrderStateCreated),
		CheckIn:     20250101,
		CheckOut:    20250102,
		BizInfo:     "invalid json content", // 无效的 JSON
		CreateTime:  time.Now(),
	}

	domainOrder := convertDAOOrderToDomain(daoOrder)

	// 验证基本信息仍然正确
	assert.Equal(t, types.ID(54321), domainOrder.Id)
	assert.Equal(t, "REF-INVALID-BIZINFO", domainOrder.ReferenceNo)

	// 验证无效 BizInfo 被正确处理
	assert.Nil(t, domainOrder.BizInfo)

	t.Logf("无效 BizInfo 测试通过，系统正确处理了格式错误的数据")
}

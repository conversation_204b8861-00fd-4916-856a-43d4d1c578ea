package service

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	common "hotel/common/domain"
	"hotel/common/money"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	tradeDomain "hotel/trade/domain"
)

func TestConvertDAOOrderToDomainWithBizInfo(t *testing.T) {
	// 创建测试用的 BizInfo 数据
	checkAvailResp := &supplierDomain.CheckAvailResp{
		Status: supplierDomain.CheckAvailStatusAvailable,
		RoomRatePkg: &supplierDomain.RoomRatePkg{
			RatePkgId: "test-rate-pkg-123",
			CheckIn:   types.DateInt(20250101),
			CheckOut:  types.DateInt(********),
			Rate: supplierDomain.Rate{
				FinalRate: money.Money{
					Amount:   10000, // 100.00 USD
					Currency: "USD",
				},
			},
		},
		Supplier: supplierDomain.Supplier(1), // Ctrip
	}

	booker := supplierDomain.Booker{
		FirstName: "<PERSON>",
		LastName:  "Doe",
		Email:     "<EMAIL>",
		Phone: common.Phone{
			Number: "+**********",
		},
	}

	guests := []supplierDomain.Guest{
		{
			FirstName: "John",
			LastName:  "Doe",
			Age:       30,
		},
		{
			FirstName: "Jane",
			LastName:  "Doe",
			Age:       28,
		},
		{
			FirstName: "Little",
			LastName:  "Doe",
			Age:       8,
		},
	}

	sellerPayloads := &supplierDomain.SellerInputPayloads{
		Supplier: supplierDomain.Supplier(1),
		Book: &supplierDomain.BookReq{
			Booker: booker,
			Guests: guests,
		},
	}

	bizInfo := &tradeDomain.OrderBizInfo{
		CheckAvailResp:      checkAvailResp,
		SellerInputPayloads: sellerPayloads,
	}

	bizInfoJson, err := json.Marshal(bizInfo)
	assert.NoError(t, err)

	// 创建测试用的 DAO Order
	daoOrder := &dao.Order{
		Id:                       12345,
		ReferenceNo:              "REF-TEST-001",
		Status:                   int64(tradeDomain.OrderStatePaid),
		BuyerCurrency:            "USD",
		BuyerAmount:              10000, // 100.00 USD
		SellerCurrency:           "USD",
		SellerAmount:             9500,  // 95.00 USD
		TenantRevenueAmountUsd:   300,   // 3.00 USD
		CustomerRevenueAmountUsd: 200,   // 2.00 USD
		CheckIn:                  20250101,
		CheckOut:                 ********,
		BizInfo:                  string(bizInfoJson),
		CreateTime:               time.Date(2024, 12, 1, 12, 0, 0, 0, time.UTC),
	}

	// 执行转换
	result := convertDAOOrderToDomain(daoOrder)

	// 验证基本字段
	assert.Equal(t, types.ID(12345), result.Id)
	assert.Equal(t, "REF-TEST-001", result.ReferenceNo)
	assert.Equal(t, tradeDomain.OrderStatePaid, result.Status)
	assert.Equal(t, time.Date(2024, 12, 1, 12, 0, 0, 0, time.UTC), result.CreateTime)

	// 验证 OrderAccount 信息 (暂时跳过，因为结构不匹配)
	// assert.NotNil(t, result.OrderAccount)

	// 验证 Booker 信息
	assert.Equal(t, "John", result.Booker.FirstName)
	assert.Equal(t, "Doe", result.Booker.LastName)
	assert.Equal(t, "<EMAIL>", result.Booker.Email)
	assert.Equal(t, "+**********", result.Booker.Phone.Number)

	// 验证 Rooms 信息
	assert.Len(t, result.Rooms, 1)
	room := result.Rooms[0]
	assert.Equal(t, 1, room.RoomIndex)
	assert.Len(t, room.Guests, 3) // 3个客人

	// 验证 BizInfo 被正确解析
	assert.NotNil(t, result.BizInfo)
	assert.NotNil(t, result.BizInfo.CheckAvailResp)
	assert.Equal(t, "test-rate-pkg-123", result.BizInfo.CheckAvailResp.RoomRatePkg.RatePkgId)
	assert.NotNil(t, result.BizInfo.SellerInputPayloads)
	assert.Equal(t, "John", result.BizInfo.SellerInputPayloads.Book.Booker.FirstName)
}

func TestParseBizInfoWithInvalidJson(t *testing.T) {
	// 测试无效的 JSON
	result := parseBizInfo("invalid json")
	assert.Nil(t, result)

	// 测试空字符串
	result = parseBizInfo("")
	assert.Nil(t, result)
}

func TestExtractGuestInfoFromBizInfo(t *testing.T) {
	guests := []supplierDomain.Guest{
		{Age: 30}, // Adult
		{Age: 25}, // Adult
		{Age: 8},  // Child
		{Age: 12}, // Child
	}

	sellerPayloads := &supplierDomain.SellerInputPayloads{
		Book: &supplierDomain.BookReq{
			Guests: guests,
		},
	}

	bizInfo := &tradeDomain.OrderBizInfo{
		SellerInputPayloads: sellerPayloads,
	}

	adults, children := extractGuestInfoFromBizInfo(bizInfo)
	assert.Equal(t, 2, adults)
	assert.Equal(t, 2, children)
}

func TestExtractGuestInfoWithNoAgeInfo(t *testing.T) {
	guests := []supplierDomain.Guest{
		{FirstName: "Guest1"},
		{FirstName: "Guest2"},
	}

	sellerPayloads := &supplierDomain.SellerInputPayloads{
		Book: &supplierDomain.BookReq{
			Guests: guests,
		},
	}

	bizInfo := &tradeDomain.OrderBizInfo{
		SellerInputPayloads: sellerPayloads,
	}

	adults, children := extractGuestInfoFromBizInfo(bizInfo)
	assert.Equal(t, 2, adults) // 假设都是成人
	assert.Equal(t, 0, children)
}

func TestCalculateNightsFromDates(t *testing.T) {
	tests := []struct {
		name     string
		checkIn  int64
		checkOut int64
		expected int
	}{
		{
			name:     "正常的2晚住宿",
			checkIn:  20250101,
			checkOut: ********,
			expected: 2,
		},
		{
			name:     "1晚住宿",
			checkIn:  20250101,
			checkOut: 20250102,
			expected: 1,
		},
		{
			name:     "跨月住宿",
			checkIn:  20241230,
			checkOut: 20250102,
			expected: 3,
		},
		{
			name:     "无效日期应返回1",
			checkIn:  0,
			checkOut: 0,
			expected: 1,
		},
		{
			name:     "退房日期早于入住日期应返回1",
			checkIn:  ********,
			checkOut: 20250101,
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateNightsFromDates(tt.checkIn, tt.checkOut)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractBookerInfoFromBizInfo(t *testing.T) {
	booker := supplierDomain.Booker{
		FirstName: "John",
		LastName:  "Doe",
		Email:     "<EMAIL>",
		Phone: common.Phone{
			Number: "+**********",
		},
	}

	sellerPayloads := &supplierDomain.SellerInputPayloads{
		Book: &supplierDomain.BookReq{
			Booker: booker,
		},
	}

	bizInfo := &tradeDomain.OrderBizInfo{
		SellerInputPayloads: sellerPayloads,
	}

	result := extractBookerInfoFromBizInfo(bizInfo)
	assert.Equal(t, "John", result.FirstName)
	assert.Equal(t, "Doe", result.LastName)
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, "+**********", result.Phone.Number)
}

func TestExtractBookerInfoWithNilBizInfo(t *testing.T) {
	result := extractBookerInfoFromBizInfo(nil)
	assert.Equal(t, supplierDomain.Booker{}, result)
}

package service

import (
	"context"
	"encoding/json"

	"hotel/trade/dao"
	"hotel/trade/domain"

	"hotel/common/log"
)

const (
	CancelTopic        = "cancel_book"
	CancelTopicChannel = "cancel_book_channel"
)

func (s *TradeService) startConsumerCancelMessage() error {
	// TODO: 重构以适配新的 CQRS 接口
	// 暂时禁用消费者功能，避免编译错误
	log.Info("Cancel message consumer is temporarily disabled due to CQRS refactoring")
	return nil

	/*
		cancelCfg := &cqrs.ConsumerConfig{
			TopicName:       CancelTopic,
			ChannelName:     CancelTopicChannel,
			MaxInFlight:     10,
			MaxAttempts:     3,
			RetryDelay:      2 * time.Second,
			DeadLetterTopic: CancelTopic + "_dead_letter",
		}

		consumer, err := cqrs.NewConsumer(nil, cancelCfg, s.handleCancelMessage)
		if err != nil {
			log.Error("Failed to create consumer: %+v", err)
			return err
		}
		defer consumer.Stop()

		err = consumer.Start()
		if err != nil {
			log.Error("Failed to start consumer: %v", err)
			return err
		}
	*/

	log.Error("Cancel Consumer started. Waiting for messages...")
	// 不需要永久阻塞，让主程序继续运行
	return nil
}

func (s *TradeService) handleCancelMessage(ctx context.Context, msg []byte) error {
	// 如果已经提交给供应商，需要调用供应商取消接口
	order := &dao.Order{}
	if err := json.Unmarshal(msg, order); err != nil {
		log.Error("handleCancel:(%+v),failed:(%+v)", order, err)
		return err
	}
	status := domain.OrderStatus(order.Status)
	if status == domain.OrderStateNeedCancel {
		//TODO:调用供应商接口取消订单
	}
	order.Status = int64(domain.OrderStateCancelled)
	if err := s.orderDao.Order.Update(ctx, order); err != nil {
		log.Errorc(ctx, "UpdateOrderFailed:(%+v),Error:(%+v)", order, err)
		return err
	}
	return nil
}

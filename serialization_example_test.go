package cqrs_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"hotel/common/cqrs"
)

// 示例：用户事件
type UserEvent struct {
	UserID    int64     `json:"user_id"`
	EventType string    `json:"event_type"`
	Timestamp time.Time `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
}

// 示例：订单事件
type OrderEvent struct {
	OrderID   string  `json:"order_id"`
	UserID    int64   `json:"user_id"`
	Amount    float64 `json:"amount"`
	Status    string  `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

// TestSerializationExample 展示内部序列化功能
func TestSerializationExample(t *testing.T) {
	// 配置
	config := &cqrs.Config{
		Type: "redis_stream",
		Redis: cqrs.RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	// 创建生产者
	producer, err := cqrs.NewProducer(config)
	if err != nil {
		t.Logf("Failed to create producer (expected if Redis not running): %v", err)
		return
	}
	defer producer.Close()

	ctx := context.Background()

	// 1. 发布字符串消息
	err = producer.Publish(ctx, "notifications", "Welcome to our service!")
	if err != nil {
		t.Logf("Failed to publish string message: %v", err)
	}

	// 2. 发布结构体消息 (自动JSON序列化)
	userEvent := UserEvent{
		UserID:    12345,
		EventType: "user_registered",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"email":    "<EMAIL>",
			"username": "john_doe",
		},
	}
	err = producer.Publish(ctx, "user_events", userEvent)
	if err != nil {
		t.Logf("Failed to publish user event: %v", err)
	}

	// 3. 发布订单事件
	orderEvent := OrderEvent{
		OrderID:   "order_123",
		UserID:    12345,
		Amount:    99.99,
		Status:    "pending",
		CreatedAt: time.Now(),
	}
	err = producer.Publish(ctx, "order_events", orderEvent)
	if err != nil {
		t.Logf("Failed to publish order event: %v", err)
	}

	// 4. 发布带选项的消息
	opts := &cqrs.PublishOptions{
		Headers: map[string]string{
			"event_type": "critical",
			"priority":   "high",
			"source":     "payment_service",
		},
	}
	
	paymentEvent := map[string]interface{}{
		"payment_id": "pay_456",
		"order_id":   "order_123",
		"amount":     99.99,
		"status":     "completed",
	}
	err = producer.PublishWithOptions(ctx, "payment_events", paymentEvent, opts)
	if err != nil {
		t.Logf("Failed to publish payment event: %v", err)
	}

	// 5. 发布原始字节数据 (不序列化)
	rawData := []byte(`{"custom": "format", "binary": true}`)
	err = producer.PublishRaw(ctx, "raw_events", rawData)
	if err != nil {
		t.Logf("Failed to publish raw data: %v", err)
	}

	// 6. 批量发布不同类型的消息
	messages := []interface{}{
		"Simple string message",
		42,
		[]string{"tag1", "tag2", "tag3"},
		map[string]string{"key": "value"},
		UserEvent{
			UserID:    67890,
			EventType: "user_login",
			Timestamp: time.Now(),
		},
	}

	for i, msg := range messages {
		topic := fmt.Sprintf("batch_topic_%d", i)
		err = producer.Publish(ctx, topic, msg)
		if err != nil {
			t.Logf("Failed to publish batch message %d: %v", i, err)
		}
	}

	t.Log("Serialization example completed successfully")
}

// ExampleProducerSerialization 展示如何使用内部序列化
func ExampleProducerSerialization() {
	// 配置
	config := &cqrs.Config{
		Type: "redis_stream",
		Redis: cqrs.RedisConfig{
			Host: "localhost",
			Port: 6379,
		},
	}

	// 创建生产者
	producer, err := cqrs.NewProducer(config)
	if err != nil {
		fmt.Printf("Failed to create producer: %v\n", err)
		return
	}
	defer producer.Close()

	ctx := context.Background()

	// 发布不同类型的消息，producer 会自动序列化
	
	// 字符串 -> 直接转为字节
	producer.Publish(ctx, "topic1", "Hello World")
	
	// 结构体 -> JSON序列化
	user := struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}{ID: 1, Name: "John"}
	producer.Publish(ctx, "topic2", user)
	
	// 数字 -> JSON序列化
	producer.Publish(ctx, "topic3", 42)
	
	// 如果需要发布原始字节，使用 PublishRaw
	producer.PublishRaw(ctx, "topic4", []byte("raw data"))

	fmt.Println("Messages published with automatic serialization")
	// Output: Messages published with automatic serialization
}

// TestSerializationTypes 测试不同类型的序列化
func TestSerializationTypes(t *testing.T) {
	config := &cqrs.Config{
		Type: "redis_stream",
		Redis: cqrs.RedisConfig{Host: "localhost", Port: 6379},
	}

	producer, err := cqrs.NewProducer(config)
	if err != nil {
		t.Logf("Failed to create producer: %v", err)
		return
	}
	defer producer.Close()

	ctx := context.Background()

	// 测试各种类型的序列化
	testCases := []struct {
		name     string
		message  interface{}
		expected string // 期望的序列化结果类型
	}{
		{"string", "hello", "string"},
		{"bytes", []byte("bytes"), "bytes"},
		{"int", 123, "json"},
		{"float", 3.14, "json"},
		{"bool", true, "json"},
		{"slice", []int{1, 2, 3}, "json"},
		{"map", map[string]int{"a": 1}, "json"},
		{"struct", UserEvent{UserID: 1, EventType: "test"}, "json"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := producer.Publish(ctx, "test-types", tc.message)
			if err != nil {
				t.Logf("Failed to publish %s (expected if Redis not running): %v", tc.name, err)
			} else {
				t.Logf("Successfully published %s message", tc.name)
			}
		})
	}
}

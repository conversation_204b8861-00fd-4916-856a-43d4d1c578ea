package protocol

import (
	"hotel/content/domain"
	supplyDomain "hotel/supplier/domain"
)

type ImportHotelReq struct {
	ReferenceFileURLs map[string]string     `json:"referenceFileURLs"`
	HotelFileURLs     []string              `json:"hotelFileURLs"`
	BatchSize         int                   `json:"batchSize"`
	Start             int                   `json:"start"`
	End               int                   `json:"end"`
	Supplier          supplyDomain.Supplier `json:"supplier"` // 新增：导入文件所属供应商
}

type ImportSupplierHotelReq struct {
	Supplier         supplyDomain.Supplier `json:"supplier"`
	SupplierHotelIds []string              `json:"supplierHotelIds"`
}
type ImportSupplierHotelResp struct {
	Hotels domain.HotelList `json:"hotels"`
}

type SyncSupplierHotelReq struct {
	HotelId   string                  `json:"hotelId"`
	Suppliers []supplyDomain.Supplier `json:"suppliers"`
}
type SyncSupplierHotelResp struct {
	Hotels domain.HotelList `json:"hotels"`
}

type StartReduceHotelCronJobReq struct {
	Suppliers []supplyDomain.Supplier `json:"suppliers"`
}
type StartReduceHotelCronJobResp struct {
	JobId string `json:"jobId"`
}

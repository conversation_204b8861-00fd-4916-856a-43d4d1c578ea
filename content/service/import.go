package service

import (
	"context"
	"fmt"
	"hotel/common/types"
	"time"

	"hotel/common/fanout"
	"hotel/common/i18n"
	"hotel/common/idgen"
	"hotel/common/log"
	"hotel/common/utils"
	"hotel/content/domain"
	"hotel/content/mysql"
	"hotel/content/protocol"
	geoDomain "hotel/geography/domain"
	geoSrv "hotel/geography/service"
	supplierSrv "hotel/supplier"
	supplierDomain "hotel/supplier/domain"
	userSrv "hotel/user/service"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

// ImportService 高性能酒店导入服务
type ImportService struct {
	dao        *mysql.HotelDao
	factory    *supplierSrv.Factory
	userSrv    *userSrv.UserService
	geoSrv     *geoSrv.GeographyService
	async      *fanout.Fanout
	dbOperator *DatabaseOperator
}

// NewImportService 创建新的导入服务
func NewImportService(deps *deps) *ImportService {
	return &ImportService{
		dao:        deps.dao,
		factory:    deps.factory,
		userSrv:    deps.userSrv,
		geoSrv:     deps.geoSrv,
		async:      fanout.New("import", fanout.Worker(4), fanout.Buffer(1024)),
		dbOperator: NewDatabaseOperator(deps.dao),
	}
}

// Name 返回服务名称
func (s *ImportService) Name() string {
	return "content/import"
}

// ImportHotel 导入酒店数据
func (s *ImportService) ImportHotel(ctx context.Context, req *protocol.ImportHotelReq) error {
	if req.ReferenceFileURLs["region"] == "" || len(req.HotelFileURLs) == 0 {
		return fmt.Errorf("invalid request parameters")
	}

	if req.Supplier == supplierDomain.Supplier_UNKNOWN {
		req.Supplier = supplierDomain.Supplier_Trip
	}

	regions := loadCtripHotelCityMapping(req.ReferenceFileURLs["region"])

	for _, u := range req.HotelFileURLs {
		if err := s.async.Do(ctx, func(ctx context.Context) {
			importedCnt, err := s.importLargeCSVToHotel(ctx, regions, u, req.BatchSize, req.Start, req.End, req.Supplier)
			if err != nil {
				log.Errorc(ctx, "importLargeCSVToHotel failed: %+v", err)
			}
			log.Infoc(ctx, "importLargeCSVToHotel done, url(%s) imported(%d)", u, importedCnt)
		}); err != nil {
			return err
		}
	}
	return nil
}

// importLargeDelimitedFileToHotel 导入大型分隔符文件到酒店数据库（流式处理）
func (s *ImportService) importLargeCSVToHotel(ctx context.Context, regions map[string]string, fn string, batchSize int, start, end int, supplier supplierDomain.Supplier) (count int, err error) {
	st := time.Now()
	defer func() {
		log.Infoc(ctx, "importLargeDelimitedFileToHotel completed, imported: %d, cost: %s", count, time.Since(st))
	}()

	// 自动检测文件格式并处理
	var hotels []*domain.Hotel
	processedCount := 0
	parseErrCount := 0
	parseNilCount := 0
	processErrCount := 0

	err = utils.ProcessFileAutoDetect(fn, func(line int, row map[string]string) bool {
		if line < start {
			return true
		}
		if line > end {
			return false
		}

		hotel, err := s.parseRowWithSupplier(ctx, row, supplier, regions)
		if err != nil {
			log.Errorc(ctx, "parseRowWithSupplier failed: %+v, row: %#v", err, row)
			parseErrCount++
			return true // 继续处理下一行
		}

		if hotel == nil {
			log.Warnc(ctx, "parseRowWithSupplier returned nil, row: %#v", row)
			parseNilCount++
			return true
		}

		// 处理区域ID映射
		if hotel.HotelStaticProfile.RegionId == 0 {
			ctripcityid := regions[row["prd_city_region_code"]]
			hotel.HotelStaticProfile.RegionId = idgen.String2Int64ID(ctripcityid)
			log.Infoc(ctx, "line:%d, city %v -> %v", line, ctripcityid, hotel.HotelStaticProfile.RegionId)
		}

		hotels = append(hotels, hotel)
		processedCount++

		// 达到批次大小时批量写入数据库
		if len(hotels) >= batchSize {
			if err = s.dbOperator.BatchUpsertHotels(ctx, hotels); err != nil {
				log.Errorc(ctx, "line:%d batch upsert hotels failed: %w", line, err)
				processErrCount += len(hotels)
				return true // 继续处理下一行
			}
			count += len(hotels)
			hotels = hotels[:0] // 清空切片但保留容量
		}

		return true
	})

	// 处理剩余的酒店数据
	if len(hotels) > 0 {
		if err = s.dbOperator.BatchUpsertHotels(ctx, hotels); err != nil {
			return count, fmt.Errorf("batch upsert remaining hotels failed: %w", err)
		}
		count += len(hotels)
	}

	log.Infoc(ctx, "importLargeCSVToHotel processed %d records, imported %d hotels, parseNil(%d) parseErr(%d) processErr(%d), start: %d, end: %d", processedCount, count, parseNilCount, parseErrCount, processErrCount, start, end)
	return count, nil
}

// parseRowWithSupplier 多供应商适配入口（使用正确的master分支逻辑）
func (s *ImportService) parseRowWithSupplier(ctx context.Context, row map[string]string, supplier supplierDomain.Supplier, regions map[string]string) (*domain.Hotel, error) {
	switch supplier {
	case supplierDomain.Supplier_Trip:
		return s.parseCtripRow(row)
	case supplierDomain.Supplier_Dida:
		return s.parseDidaRow(ctx, row)
	case supplierDomain.Supplier_Expedia:
		return s.parseExpediaRow(row)
	default:
		return nil, nil // 未支持的供应商直接跳过
	}
}

// parseCtripRow 解析携程记录（使用正确的master分支逻辑）
func (s *ImportService) parseCtripRow(row map[string]string) (*domain.Hotel, error) {
	if row["supplier_type"] != "1" {
		return nil, nil
	}

	// 解析设施信息
	var fs []*supplierDomain.HotelFacilityCategory
	if fsRaw := row["facility_info"]; fsRaw != "" {
		fsMap := make(map[supplierDomain.Facility]supplierDomain.FacilityList)
		gjson.Parse(fsRaw).ForEach(func(key, value gjson.Result) bool {
			kid := value.Get("FacilityTypeID").String()
			kname := value.Get("FacilityTypeNameI18N")
			k := supplierDomain.Facility{
				Id: kid,
				Name: i18n.I18N{
					Zh: kname.Get("zh_CN").String(),
					En: kname.Get("en_US").String(),
				},
			}
			fsMap[k] = append(fsMap[k], supplierDomain.Facility{
				Id: value.Get("FacilityID").String(),
				Name: i18n.I18N{
					Zh: value.Get("FacilityNameI18N").Get("zh_CN").String(),
					En: value.Get("FacilityNameI18N").Get("en_US").String(),
				},
			})
			return true
		})

		for k, v := range fsMap {
			fs = append(fs, &supplierDomain.HotelFacilityCategory{
				Category:   k,
				Facilities: v,
			})
		}
	}

	return &domain.Hotel{
		ID:             idgen.String2Int64ID(row["supplier_hotel_id"]),
		MasterSupplier: supplierDomain.Supplier_Trip,
		HotelStaticProfile: supplierDomain.HotelStaticProfile{
			LastRefreshStaticTime: time.Time{},
			RegionId:              0, // 稍后填充
			Name: i18n.I18N{
				Zh: mysql.CleanString(row["prd_hotel_name"]),
				En: mysql.CleanString(row["hotel_name_en"]),
			},
			RatingInfo: supplierDomain.RatingInfo{
				Rating:       cast.ToFloat64(row["rating"]),
				NumOfReviews: cast.ToInt64(row["num_of_reviews"]),
			},
			Star: 0,
			LoyaltyProgram: supplierDomain.HotelLoyaltyProgram{
				BrandId: cast.ToInt64(row["brand"]),
				GroupId: 0,
			},
			Address: i18n.I18N{
				Zh: mysql.CleanString(row["address_en0"]),
				En: mysql.CleanString(row["address_en"]),
			},
			CitySummary: geoDomain.CitySummary{},
			LatlngCoordinator: geoDomain.LatlngCoordinator{
				Google: &geoDomain.Latlng{
					Lat: cast.ToFloat64(row["prd_google_lat"]),
					Lng: cast.ToFloat64(row["prd_google_lng"]),
				},
				Gaode: &geoDomain.Latlng{
					Lat: cast.ToFloat64(row["prd_gaode_lat"]),
					Lng: cast.ToFloat64(row["prd_gaode_lng"]),
				},
			},
			LogoURL:         mysql.CleanString(row["hotel_logo_pic"]),
			Phone:           mysql.CleanString(row["telephone"]),
			Email:           mysql.CleanString(row["email"]),
			Fax:             mysql.CleanString(row["fax"]),
			OpenYear:        cast.ToInt64(row["open_year"]),
			FitmentYear:     cast.ToInt64(row["fitment_year"]),
			HotelPictures:   nil,
			HotelFacilities: fs,
			Desc: i18n.I18N{
				Zh: mysql.CleanString(row["desc_zh"]),
				En: mysql.CleanString(row["desc_en"]),
			},
		},
		HotelSupplierRef: []supplierDomain.HotelSupplierRef{
			{Supplier: supplierDomain.Supplier_Trip, SupplierHotelID: row["supplier_hotel_id"]},
		},
	}, nil
}

// Dida 供应商酒店解析
func (s *ImportService) parseDidaRow(ctx context.Context, row map[string]string) (*domain.Hotel, error) {
	didacode := row["DestinationID"]
	var regionId types.ID
	region, err := s.geoSrv.GetRegionBySupplierRegionId(ctx, supplierDomain.Supplier_Dida, didacode)
	if err != nil {
		log.Warnc(ctx, "dida code %v failed: %v, row: %#v", didacode, err, row)
	} else {
		regionId = region.ID
		log.Infoc(ctx, "dida code %v -> %v (%s)", didacode, region.ID, region.Name)
	}

	// 看了几个例子不知道这个是什么，先跳过
	// didaCity := row["CityCode"]

	hotelSupplierRef := []supplierDomain.HotelSupplierRef{
		{Supplier: supplierDomain.Supplier_Dida, SupplierHotelID: row["HotelID"]},
	}
	// 额外供应商ID映射
	if v := row["HBG"]; v != "" {
		hotelSupplierRef = append(hotelSupplierRef, supplierDomain.HotelSupplierRef{
			Supplier:        supplierDomain.Supplier_Hotelbeds,
			SupplierHotelID: v,
		})
	}
	if v := row["EPS"]; v != "" {
		hotelSupplierRef = append(hotelSupplierRef, supplierDomain.HotelSupplierRef{
			Supplier:        supplierDomain.Supplier_Expedia,
			SupplierHotelID: v,
		})
	}
	if v := row["GIATA"]; v != "" {
		hotelSupplierRef = append(hotelSupplierRef, supplierDomain.HotelSupplierRef{
			Supplier:        supplierDomain.Supplier_Giata,
			SupplierHotelID: v,
		})
	}
	return &domain.Hotel{
		ID:             idgen.String2Int64ID("dida_" + row["HotelID"]),
		MasterSupplier: supplierDomain.Supplier_Dida,
		HotelStaticProfile: supplierDomain.HotelStaticProfile{
			RegionId: regionId,
			Name: i18n.I18N{
				En: mysql.CleanString(row["Name"]),
			},
			Address: i18n.I18N{
				En: mysql.CleanString(row["Address"]),
			},
			CitySummary: geoDomain.CitySummary{
				City: geoDomain.RegionSummary{
					Name: i18n.I18N{
						En: mysql.CleanString(row["CityName"]),
					},
				},
			},
			RatingInfo: supplierDomain.RatingInfo{
				Rating: cast.ToFloat64(row["StarRating"]),
			},
			LatlngCoordinator: geoDomain.LatlngCoordinator{
				Google: &geoDomain.Latlng{
					Lat: cast.ToFloat64(row["Latitude"]),
					Lng: cast.ToFloat64(row["Longitude"]),
				},
			},
			Phone:      mysql.CleanString(row["Telephone"]),
			Email:      mysql.CleanString(row["Email"]),
			ZipCode:    mysql.CleanString(row["ZipCode"]),
			WebsiteURL: mysql.CleanString(row["WebsiteURL"]),
		},
		HotelSupplierRef: hotelSupplierRef,
	}, nil
}

// parseExpediaRow 解析Expedia记录（使用正确的master分支逻辑）
func (s *ImportService) parseExpediaRow(row map[string]string) (*domain.Hotel, error) {
	return &domain.Hotel{
		ID:             idgen.String2Int64ID("expedia_" + row["hid"]),
		MasterSupplier: supplierDomain.Supplier_Expedia,
		HotelStaticProfile: supplierDomain.HotelStaticProfile{
			Name: i18n.I18N{
				En: mysql.CleanString(row["name"]),
			},
			Address: i18n.I18N{
				En: mysql.CleanString(row["adr"]),
			},
			RatingInfo: supplierDomain.RatingInfo{
				Rating: cast.ToFloat64(row["star"]),
			},
			LatlngCoordinator: geoDomain.LatlngCoordinator{
				Google: &geoDomain.Latlng{
					Lat: cast.ToFloat64(row["lati"]),
					Lng: cast.ToFloat64(row["longi"]),
				},
			},
		},
		HotelSupplierRef: []supplierDomain.HotelSupplierRef{
			{Supplier: supplierDomain.Supplier_Expedia, SupplierHotelID: row["hid"]},
		},
	}, nil
}

func loadCtripHotelCityMapping(fn string) map[string]string {
	// 自动检测文件格式并读取
	vs, err := utils.ReadFileAutoDetect(fn)
	if err != nil {
		log.Error("loadCtripHotelCityMapping failed to read file: %v", err)
		return make(map[string]string)
	}

	out := make(map[string]string)
	for _, row := range vs {
		out[row["region_code"]] = row["id"]
	}
	return out
}

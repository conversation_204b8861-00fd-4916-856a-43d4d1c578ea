package service

import (
	"context"
	"fmt"
	"hotel/common/i18n"
	"hotel/common/idgen"
	"hotel/common/linktech"
	"hotel/content/protocol"
	"time"

	commonProto "hotel/common/protocol"
	"hotel/content/domain"
	supplierDomain "hotel/supplier/domain"
)

// ImportSupplierHotel 导入供应商酒店数据
func (s *ImportService) ImportSupplierHotel(ctx context.Context, req *protocol.ImportSupplierHotelReq) (*protocol.ImportSupplierHotelResp, error) {
	link := linktech.NewLink(linktech.Participant{
		InNode:  s.userSrv.GetBuyerInNode(ctx),
		OutNode: s.userSrv.GetBuyerOutNode(ctx),
	}, linktech.Participant{
		InNode:  s.userSrv.GetBuyerInNode(ctx),
		OutNode: s.userSrv.GetBuyerOutNode(ctx),
	})

	resp := &protocol.ImportSupplierHotelResp{}

	for _, supplierHotelId := range req.SupplierHotelIds {
		refs := []supplierDomain.HotelSupplierRef{
			{
				SupplierHotelID: supplierHotelId,
				Supplier:        req.Supplier,
			},
		}

		hs, err := s.dao.Find(ctx, &domain.Hotel{
			HotelSupplierRef: refs,
		})
		if err != nil {
			return nil, fmt.Errorf("find hotels: %w", err)
		}

		if len(hs) > 1 {
			return nil, fmt.Errorf("too many hotel suppliers: %v", hs.IDs())
		}

		var h *domain.Hotel
		if len(hs) == 1 {
			h = hs[0]
		}

		cid, _ := link.Seller.InNode.GetCredentialId(ctx)
		ctx = supplierDomain.InjectBaseRequestContextPayload(ctx, supplierDomain.BaseRequestContextPayload{
			Supplier:   req.Supplier,
			Properties: cid,
			Header: commonProto.Header{
				LanguageOption: commonProto.LanguageOption{Language: i18n.EnUs_ISO3166},
				RequestTimeOption: commonProto.RequestTimeOption{
					ClientRequestTimestamp: time.Now().Unix(),
					ServerRequestTimestamp: time.Now().Unix(),
				},
			},
		})

		// 这里需要调用factory的方法，暂时注释
		staticResp, err := s.factory.HotelStaticDetail(ctx, &supplierDomain.HotelStaticDetailReq{SupplierHotelId: supplierHotelId})
		if err != nil {
			return nil, fmt.Errorf("supplierHotelId %s: %w", supplierHotelId, err)
		}

		if h != nil { // update
			// h.HotelStaticProfile = h.HotelStaticProfile.MergeUpdate(staticResp.HotelStaticProfile)
			err = s.dao.Update(ctx, h)
		} else { // insert
			hs, err = s.dao.Find(ctx, &domain.Hotel{
				// HotelStaticProfile: staticResp.HotelStaticProfile,
			})
			if err != nil {
				return nil, fmt.Errorf("find hotels: %w", err)
			}

			if len(hs) > 0 {
				return nil, fmt.Errorf("too many hotel suppliers, hotelIDs(%v)", hs.IDs())
			}
			if len(hs) == 1 {
				h = hs[0]
				err = s.dao.Update(ctx, h)
			} else { //insert
				h = &domain.Hotel{
					ID:                 idgen.MustNextInt64ID(),
					HotelSupplierRef:   refs,
					HotelStaticProfile: staticResp.HotelStaticProfile,
				}
				err = s.dao.Insert(ctx, h)
			}
		}
		if err != nil {
			return nil, err
		}
		resp.Hotels = append(resp.Hotels, h)
	}
	return resp, nil
}

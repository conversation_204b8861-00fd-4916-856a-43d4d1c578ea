# 项目背景 
你正在开发一个酒店 API 分销平台，用户需要接入  Dida/DerbySoft等多个供应商，并实现酒店数据同步、订单处理等功能。
admin-fe 目录是前端代码，这是一个供内部使用的前端应用，也将作为示例，实现我们所有想要构建的产品功能，例如酒店预订、规则引擎等。

# Project Requirements
- For this hotel management project, use admin-fe as frontend root directory.
- Implement end-to-end testing with Cypress or Playwright. User prefers thorough testing over assumptions and real implementation testing over mocks.
- Ensure full-stack integration between frontend and backend APIs. Frontend components must integrate with backend APIs rather than implementing standalone mock functionality.
- User prefers to completely remove frontend mock data as it causes significant interference during integration testing.
- User prefers nested directory structure organized strictly according to menu hierarchy for clearer code organization.
- User prefers shorter path names without redundant 'management' suffix - use 'order' instead of 'order-management' for route paths.
- User prefers to merge system and system-management directories/modules into a single unified structure.
- User prefers UI designs with clear visual hints and user guidance rather than minimal interfaces without explanatory text or visual cues.

# API Integration
- For API integration, consult apifox mcp API documentation to ensure proper implementation.
- For city input in hotel search, call backend (s *SearchService) Search interface to get regionId for subsequent API parameters.
- checkAvail API calls must include headers, especially sessionId which is critical for the request.
- Response headers should be automatically set using reflection to parse ResponseHeader fields from response structs.
- For large integer ID fields, use string type in frontend to avoid JavaScript big number precision issues.
- When modifying common/protocol/header.go, ensure CORS setHeader methods are updated to include all new headers.
- Frontend axios interceptor returns data directly (line 73 in src/utils/http/index.ts), so API code should use response directly, not response.data.
- Frontend should cache InternalAdminHomepage API response locally for at least 10 minutes to avoid unnecessary requests during page navigation.

# Multilingual Support
- Multilingual support should be handled by backend delivery, implement InternalHomepage method for frontend integration.
- UI text should be dynamically delivered by backend with frontend hardcoded text only as fallback.
- Backend should return i18n.I18N objects for all text fields instead of simple strings.
- Language selection should follow user settings/configuration rather than hardcoded priority, though fallback logic is acceptable.
- API-delivered i18n content should have higher priority than starlingMap in the text resolution hierarchy.
- Replace hardcoded language fields (zh/en/ar) with extensible i18n patterns to support future language expansion.
- Menu content should be returned by backend InternalAdminHomepage API, not from frontend static configuration.
- User prefers formatMenuTitle function to explicitly receive user's selected language as input parameter.

# User Management
- User model should include user+entity management with operations: registration, login, invitation, activation, import, and editing.
- Change '业务用户' to '租户用户' and 'Business User' to 'Tenant User' throughout the application.
- Use 'entity' instead of 'building' for entity management terminology.
- Entity type system changed from single type per entity to supporting multiple types using bitwise operations.
- For entity configuration JSON editing, user specifically requires Monaco Editor (VSCode web version).

# Geography Data
- Geography data should be organized by supplier in geography/config/ directory.
- Ctrip and Dida supplier data should be properly combined instead of keeping them separate.
- Geography service needs dida id support and database persistence functionality with incremental updates.
- Type mismatches between suppliers should be resolved during data construction phase rather than during merging.
- User strongly prefers dynamic/data-driven solutions instead of hardcoded approaches like isMajorCity.
- For geography data matching: country-level mismatches should be handled with hardcoded matching logic, while city-level mismatches require operational interfaces.

# Development Best Practices
- For protocol upgrades, backward compatibility is not required unless user explicitly requires it. - deprecated fields should be removed.
- User prefers to keep console logs during development for debugging purposes.
- In Go, when modifying struct properties, ensure the variable is a pointer (&pkg) rather than a struct value (pkg).
- All pages should use consistent bff.Table componentization to avoid mixed approaches.
- User expects performance optimizations to actually improve performance significantly.
- User prefers not to modify backend code when frontend has parsing issues - frontend should adapt to backend protocols.
- Frontend end-to-end tests should be placed in admin-fe/tests/e2e directory, not in the root tests/e2e directory.
- 仅针对复杂业务逻辑做 mock，否则尽量基于真实代码运行环境做 TDD 开发：1. 编写单元测试的环境初始化逻辑； 2. 编写有真实业务意义的单元测试用例；3. 编写业务实现代码；4. 通过UT 驱动代码逻辑完善与 bugfix。
- 编写前端代码时，需要完全 follow 后端已有接口协议，如果有不清楚、不完善的地方，请寻求我的确认。在得到许可后，你可以进一步完善后端逻辑，不可以根据前端需求擅自调整后端代码。在我认可你的后端代码后，你可以继续 「完全 follow 后端已有接口协议」从事前端开发联调工作，确保任务端到端完成。
- 你应该始终致力于穷尽你所有的能力（例如 各种 插件、MCP 等），自主、端到端地解决问题，而不是仅仅给出建议，命令、等待用户来做相关的工作。你只需要并且只能在不知道解决方案、存在多种类似方案无法取舍时寻求用户确认，其他情况请自主行动，不要反复让用户告知一些你已经知道的东西。
- 基于真实接口文档（特别是供应商接口文档）来编写代码，确保代码的正确性。禁止基于猜想来编写代码。
- 尽量不要定义临时匿名结构体，这种代码可维护性极差。
- 单元测试的包名称跟被测试的包名称一致，例如：被测试的包是 content/mysql，则单元测试的包名称是 content/mysql/test
- 在完成任务后，你应该反思项目可能存在的问题和改进方式，包括但不限于文档清晰易懂、代码清晰易懂、高性能、可维护性、可扩展性等，并更新在readme.md文件提交代码到github，并确保代码可以正常运行。

  